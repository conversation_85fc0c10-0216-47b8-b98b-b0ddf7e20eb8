import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getQuestionsByCategory = query({
  args: { category: v.string(), limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const questions = await ctx.db
      .query("questions")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .take(args.limit || 20);
    
    return questions.map(q => ({
      ...q,
      // Don't expose correct answer to client
      correctAnswer: undefined,
    }));
  },
});

export const getAllCategories = query({
  args: {},
  handler: async (ctx) => {
    const questions = await ctx.db.query("questions").collect();
    const categories = [...new Set(questions.map(q => q.category))];
    
    const categoryStats = await Promise.all(
      categories.map(async (category) => {
        const count = questions.filter(q => q.category === category).length;
        return { category, count };
      })
    );
    
    return categoryStats;
  },
});

export const startTestSession = mutation({
  args: { 
    category: v.string(),
    questionCount: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const questions = await ctx.db
      .query("questions")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .take(args.questionCount || 20);

    // Shuffle questions
    const shuffledQuestions = questions.sort(() => Math.random() - 0.5);
    
    const sessionId = await ctx.db.insert("testSessions", {
      userId,
      category: args.category,
      questions: shuffledQuestions.map(q => q._id),
      answers: [],
      startTime: Date.now(),
      completed: false,
    });

    return sessionId;
  },
});

export const submitAnswer = mutation({
  args: {
    sessionId: v.id("testSessions"),
    questionId: v.id("questions"),
    answer: v.string(),
    timeSpent: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const session = await ctx.db.get(args.sessionId);
    if (!session || session.userId !== userId) {
      throw new Error("Session not found");
    }

    const question = await ctx.db.get(args.questionId);
    if (!question) throw new Error("Question not found");

    const isCorrect = question.correctAnswer ? 
      args.answer === question.correctAnswer : undefined;

    const newAnswer = {
      questionId: args.questionId,
      answer: args.answer,
      timeSpent: args.timeSpent,
      isCorrect,
    };

    const updatedAnswers = [...session.answers, newAnswer];
    
    await ctx.db.patch(args.sessionId, {
      answers: updatedAnswers,
    });

    return { isCorrect, explanation: question.explanation };
  },
});

export const completeTestSession = mutation({
  args: { sessionId: v.id("testSessions") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    const session = await ctx.db.get(args.sessionId);
    if (!session || session.userId !== userId) {
      throw new Error("Session not found");
    }

    const correctAnswers = session.answers.filter(a => a.isCorrect).length;
    const totalQuestions = session.answers.length;
    const score = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
    const averageTime = session.answers.reduce((sum, a) => sum + a.timeSpent, 0) / totalQuestions;

    await ctx.db.patch(args.sessionId, {
      endTime: Date.now(),
      score,
      completed: true,
    });

    // Update user progress
    const existingProgress = await ctx.db
      .query("userProgress")
      .withIndex("by_user_category", (q) => 
        q.eq("userId", userId).eq("category", session.category)
      )
      .first();

    if (existingProgress) {
      await ctx.db.patch(existingProgress._id, {
        totalQuestions: existingProgress.totalQuestions + totalQuestions,
        correctAnswers: existingProgress.correctAnswers + correctAnswers,
        averageTime: (existingProgress.averageTime + averageTime) / 2,
        lastTestDate: Date.now(),
        bestScore: Math.max(existingProgress.bestScore, score),
      });
    } else {
      await ctx.db.insert("userProgress", {
        userId,
        category: session.category,
        totalQuestions,
        correctAnswers,
        averageTime,
        lastTestDate: Date.now(),
        bestScore: score,
      });
    }

    return { score, correctAnswers, totalQuestions };
  },
});

export const getTestSession = query({
  args: { sessionId: v.id("testSessions") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const session = await ctx.db.get(args.sessionId);
    if (!session || session.userId !== userId) return null;

    const questions = await Promise.all(
      session.questions.map(async (qId) => {
        const question = await ctx.db.get(qId);
        return question ? {
          ...question,
          correctAnswer: undefined, // Don't expose to client during test
        } : null;
      })
    );

    return {
      ...session,
      questions: questions.filter(Boolean),
    };
  },
});

export const getUserProgress = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    return await ctx.db
      .query("userProgress")
      .withIndex("by_user_category", (q) => q.eq("userId", userId))
      .collect();
  },
});

export const getRecentSessions = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return [];

    return await ctx.db
      .query("testSessions")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("completed"), true))
      .order("desc")
      .take(args.limit || 10);
  },
});
