import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  questions: defineTable({
    category: v.string(), // "logical", "verbal", "numerical", "personality"
    type: v.string(), // "multiple-choice", "true-false", "scale"
    question: v.string(),
    options: v.optional(v.array(v.string())),
    correctAnswer: v.optional(v.string()),
    explanation: v.string(),
    difficulty: v.string(), // "easy", "medium", "hard"
    timeLimit: v.number(), // seconds
  }).index("by_category", ["category"]),

  testSessions: defineTable({
    userId: v.id("users"),
    category: v.string(),
    questions: v.array(v.id("questions")),
    answers: v.array(v.object({
      questionId: v.id("questions"),
      answer: v.string(),
      timeSpent: v.number(),
      isCorrect: v.optional(v.boolean()),
    })),
    startTime: v.number(),
    endTime: v.optional(v.number()),
    score: v.optional(v.number()),
    completed: v.boolean(),
  }).index("by_user", ["userId"]),

  userProgress: defineTable({
    userId: v.id("users"),
    category: v.string(),
    totalQuestions: v.number(),
    correctAnswers: v.number(),
    averageTime: v.number(),
    lastTestDate: v.number(),
    bestScore: v.number(),
  }).index("by_user_category", ["userId", "category"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
