import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const seedQuestions = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if questions already exist
    const existingQuestions = await ctx.db.query("questions").take(1);
    if (existingQuestions.length > 0) {
      return "Soal-soal sudah dimuat sebelumnya";
    }

    const questions = [
      // Logical Reasoning Questions
      {
        category: "logical",
        type: "multiple-choice",
        question: "<PERSON>ka semua mawar adalah bunga dan beberapa bunga berwarna merah, pernyataan mana yang pasti benar?",
        options: ["Se<PERSON>a mawar berwarna merah", "<PERSON><PERSON>apa mawar berwarna merah", "Tidak ada mawar yang berwarna merah", "Beberapa mawar mungkin berwarna merah"],
        correctAnswer: "<PERSON>berapa mawar mungkin berwarna merah",
        explanation: "Kita tahu semua mawar adalah bunga, dan beberapa bunga berwarna merah. Ini berarti beberapa mawar bisa berwarna merah, tetapi kita tidak dapat menyimpulkan bahwa mereka pasti berwarna merah atau tidak.",
        difficulty: "medium",
        timeLimit: 60,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Apa yang selanjutnya dalam urutan: 2, 6, 12, 20, 30, ?",
        options: ["40", "42", "44", "46"],
        correctAnswer: "42",
        explanation: "Selisih antara suku berturut-turut adalah: 4, 6, 8, 10. Selisih berikutnya seharusnya 12, jadi 30 + 12 = 42.",
        difficulty: "medium",
        timeLimit: 90,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Jika A > B, B > C, dan C > D, mana yang terbesar?",
        options: ["A", "B", "C", "D"],
        correctAnswer: "A",
        explanation: "Mengikuti rantai pertidaksamaan: A > B > C > D, oleh karena itu A adalah yang terbesar.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Bentuk mana yang melengkapi pola: Lingkaran, Persegi, Segitiga, Lingkaran, Persegi, ?",
        options: ["Lingkaran", "Persegi", "Segitiga", "Segi lima"],
        correctAnswer: "Segitiga",
        explanation: "Pola berulang setiap tiga bentuk: Lingkaran, Persegi, Segitiga. Bentuk selanjutnya dalam urutan adalah Segitiga.",
        difficulty: "easy",
        timeLimit: 45,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Jika 5 mesin membutuhkan 5 menit untuk membuat 5 widget, berapa lama 100 mesin membuat 100 widget?",
        options: ["5 menit", "20 menit", "100 menit", "500 menit"],
        correctAnswer: "5 menit",
        explanation: "Setiap mesin membuat 1 widget dalam 5 menit. Oleh karena itu, 100 mesin akan membuat 100 widget dalam 5 menit.",
        difficulty: "hard",
        timeLimit: 120,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Dalam sebuah keluarga, Andi lebih tua dari Budi. Budi lebih tua dari Citra. Doni lebih muda dari Citra. Siapa yang paling tua?",
        options: ["Andi", "Budi", "Citra", "Doni"],
        correctAnswer: "Andi",
        explanation: "Urutan usia dari yang tertua: Andi > Budi > Citra > Doni. Jadi Andi adalah yang paling tua.",
        difficulty: "easy",
        timeLimit: 45,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Jika hari ini adalah hari Selasa, hari apa 100 hari lagi?",
        options: ["Senin", "Selasa", "Rabu", "Kamis"],
        correctAnswer: "Kamis",
        explanation: "100 ÷ 7 = 14 sisa 2. Jadi 100 hari dari Selasa adalah 2 hari setelah Selasa, yaitu Kamis.",
        difficulty: "medium",
        timeLimit: 90,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Dalam sebuah kotak terdapat 12 bola merah, 8 bola biru, dan 5 bola hijau. Berapa probabilitas mengambil bola merah?",
        options: ["12/25", "12/20", "8/25", "5/25"],
        correctAnswer: "12/25",
        explanation: "Total bola = 12 + 8 + 5 = 25. Probabilitas bola merah = 12/25.",
        difficulty: "medium",
        timeLimit: 75,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Jika JAKARTA ditulis sebagai JBKBSUB, bagaimana BANDUNG ditulis?",
        options: ["CBOEVOR", "CBOEVOH", "CBNEVOH", "CBOEVOG"],
        correctAnswer: "CBOEVOR",
        explanation: "Setiap huruf digeser maju 1 posisi: B→C, A→B, N→O, D→E, U→V, N→O, G→R. Jadi BANDUNG → CBOEVOR.",
        difficulty: "hard",
        timeLimit: 120,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Dalam sebuah barisan: 1, 1, 2, 3, 5, 8, ?, berapa angka selanjutnya?",
        options: ["11", "13", "15", "17"],
        correctAnswer: "13",
        explanation: "Ini adalah deret Fibonacci: setiap angka adalah jumlah dua angka sebelumnya. 5 + 8 = 13.",
        difficulty: "medium",
        timeLimit: 90,
      },

      // Verbal Ability Questions
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Pilih kata yang paling mirip artinya dengan 'MELIMPAH':",
        options: ["Langka", "Berlimpah", "Sedang", "Terbatas"],
        correctAnswer: "Berlimpah",
        explanation: "Melimpah berarti ada dalam jumlah besar; berlimpah. Kebalikannya adalah langka atau terbatas.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Lengkapi analogi: Buku adalah untuk Perpustakaan seperti Lukisan adalah untuk ?",
        options: ["Seniman", "Kanvas", "Galeri", "Bingkai"],
        correctAnswer: "Galeri",
        explanation: "Buku disimpan dan dipajang di perpustakaan, sama seperti lukisan disimpan dan dipajang di galeri.",
        difficulty: "medium",
        timeLimit: 45,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Kata mana yang TIDAK termasuk dengan yang lain?",
        options: ["Gembira", "Riang", "Ekstasi", "Melankolis"],
        correctAnswer: "Melankolis",
        explanation: "Gembira, riang, dan ekstasi semuanya mengekspresikan kebahagiaan atau kegembiraan, sedangkan melankolis berarti sedih atau murung.",
        difficulty: "medium",
        timeLimit: 60,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Pilih ejaan yang benar:",
        options: ["Akomodasi", "Akomodassi", "Akomondasi", "Akomudasi"],
        correctAnswer: "Akomodasi",
        explanation: "Ejaan yang benar adalah 'akomodasi'.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Apa arti dari idiom 'Memecah es'?",
        options: ["Sangat dingin", "Memulai percakapan", "Memecahkan sesuatu yang beku", "Bersikap agresif"],
        correctAnswer: "Memulai percakapan",
        explanation: "'Memecah es' berarti memulai percakapan atau interaksi sosial, terutama dalam situasi yang canggung.",
        difficulty: "medium",
        timeLimit: 45,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Kata 'OPTIMIS' memiliki arti yang berlawanan dengan:",
        options: ["Pesimis", "Realistis", "Idealis", "Praktis"],
        correctAnswer: "Pesimis",
        explanation: "Optimis berarti berpandangan positif, sedangkan pesimis berarti berpandangan negatif.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Lengkapi peribahasa: 'Seperti katak dalam ...'",
        options: ["air", "tempurung", "sumur", "kolam"],
        correctAnswer: "tempurung",
        explanation: "Peribahasa 'seperti katak dalam tempurung' berarti berpandangan sempit atau tidak mengetahui dunia luar.",
        difficulty: "medium",
        timeLimit: 45,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Manakah penulisan yang benar?",
        options: ["Terima kasih", "Terimakasih", "Terima-kasih", "Terima_kasih"],
        correctAnswer: "Terima kasih",
        explanation: "Penulisan yang benar adalah 'terima kasih' (dipisah) sesuai dengan Kamus Besar Bahasa Indonesia.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Sinonim dari kata 'GIGIH' adalah:",
        options: ["Malas", "Tekun", "Cepat", "Lambat"],
        correctAnswer: "Tekun",
        explanation: "Gigih dan tekun memiliki arti yang sama, yaitu tidak mudah menyerah dan bekerja keras.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Kata yang tepat untuk melengkapi kalimat: 'Dia adalah seorang yang sangat _____ dalam pekerjaannya.'",
        options: ["teliti", "ceroboh", "malas", "lambat"],
        correctAnswer: "teliti",
        explanation: "Dalam konteks positif pekerjaan, 'teliti' adalah kata yang paling tepat.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Makna ungkapan 'Bagai pungguk merindukan bulan' adalah:",
        options: ["Sangat bahagia", "Mengharapkan sesuatu yang mustahil", "Merasa kesepian", "Bekerja keras"],
        correctAnswer: "Mengharapkan sesuatu yang mustahil",
        explanation: "Ungkapan ini berarti mengharapkan atau merindukan sesuatu yang tidak mungkin tercapai.",
        difficulty: "medium",
        timeLimit: 45,
      },

      // Numerical Reasoning Questions
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Berapa 15% dari 240?",
        options: ["32", "36", "40", "44"],
        correctAnswer: "36",
        explanation: "15% dari 240 = 0,15 × 240 = 36",
        difficulty: "easy",
        timeLimit: 60,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Jika suatu produk berharga Rp80.000 setelah diskon 20%, berapa harga aslinya?",
        options: ["Rp96.000", "Rp100.000", "Rp104.000", "Rp120.000"],
        correctAnswer: "Rp100.000",
        explanation: "Jika Rp80.000 mewakili 80% dari harga asli (100% - 20% = 80%), maka harga asli = Rp80.000 ÷ 0,8 = Rp100.000",
        difficulty: "medium",
        timeLimit: 90,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Sebuah kereta menempuh 300 km dalam 4 jam. Berapa kecepatan rata-ratanya?",
        options: ["70 km/jam", "75 km/jam", "80 km/jam", "85 km/jam"],
        correctAnswer: "75 km/jam",
        explanation: "Kecepatan rata-rata = Jarak ÷ Waktu = 300 km ÷ 4 jam = 75 km/jam",
        difficulty: "easy",
        timeLimit: 45,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Apa angka selanjutnya dalam urutan: 1, 4, 9, 16, 25, ?",
        options: ["30", "32", "36", "40"],
        correctAnswer: "36",
        explanation: "Ini adalah kuadrat sempurna: 1², 2², 3², 4², 5², 6² = 36",
        difficulty: "medium",
        timeLimit: 60,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Jika 3x + 7 = 22, berapa nilai x?",
        options: ["3", "4", "5", "6"],
        correctAnswer: "5",
        explanation: "3x + 7 = 22, jadi 3x = 15, oleh karena itu x = 5",
        difficulty: "medium",
        timeLimit: 75,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Jika harga 3 kg apel adalah Rp45.000, berapa harga 5 kg apel?",
        options: ["Rp60.000", "Rp65.000", "Rp70.000", "Rp75.000"],
        correctAnswer: "Rp75.000",
        explanation: "Harga per kg = Rp45.000 ÷ 3 = Rp15.000. Harga 5 kg = 5 × Rp15.000 = Rp75.000.",
        difficulty: "easy",
        timeLimit: 60,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Sebuah toko memberikan diskon 25%. Jika harga setelah diskon adalah Rp150.000, berapa harga aslinya?",
        options: ["Rp180.000", "Rp200.000", "Rp225.000", "Rp250.000"],
        correctAnswer: "Rp200.000",
        explanation: "Harga setelah diskon = 75% dari harga asli. Harga asli = Rp150.000 ÷ 0,75 = Rp200.000.",
        difficulty: "medium",
        timeLimit: 90,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Berapa hasil dari 8² - 6² ?",
        options: ["14", "28", "32", "36"],
        correctAnswer: "28",
        explanation: "8² - 6² = 64 - 36 = 28",
        difficulty: "easy",
        timeLimit: 45,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Jika x + 5 = 3x - 7, berapa nilai x?",
        options: ["4", "5", "6", "7"],
        correctAnswer: "6",
        explanation: "x + 5 = 3x - 7, maka 5 + 7 = 3x - x, sehingga 12 = 2x, jadi x = 6.",
        difficulty: "medium",
        timeLimit: 75,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Sebuah mobil menempuh perjalanan 240 km dengan kecepatan rata-rata 60 km/jam. Berapa waktu yang diperlukan?",
        options: ["3 jam", "4 jam", "5 jam", "6 jam"],
        correctAnswer: "4 jam",
        explanation: "Waktu = Jarak ÷ Kecepatan = 240 km ÷ 60 km/jam = 4 jam.",
        difficulty: "easy",
        timeLimit: 45,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Jika 40% dari suatu bilangan adalah 80, berapa 60% dari bilangan tersebut?",
        options: ["100", "120", "140", "160"],
        correctAnswer: "120",
        explanation: "Bilangan = 80 ÷ 0,4 = 200. Maka 60% dari 200 = 0,6 × 200 = 120.",
        difficulty: "medium",
        timeLimit: 75,
      },

      // Personality Trait Questions
      {
        category: "personality",
        type: "scale",
        question: "Saya lebih suka bekerja dalam tim daripada sendirian.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini mengukur preferensi Anda terhadap lingkungan kerja kolaboratif versus gaya kerja mandiri.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya lebih suka merencanakan hal-hal terlebih dahulu daripada spontan.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini menilai preferensi Anda terhadap struktur dan perencanaan versus fleksibilitas dan spontanitas.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya merasa nyaman berbicara di depan kelompok besar.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini mengukur tingkat kenyamanan Anda dengan berbicara di depan umum dan presentasi kepada orang lain.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya sering mengambil peran kepemimpinan dalam proyek kelompok.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini mengevaluasi kecenderungan alami Anda terhadap kepemimpinan dan mengambil inisiatif.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya lebih suka instruksi yang detail daripada pedoman umum.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini menilai preferensi Anda terhadap arahan spesifik versus otonomi dalam penyelesaian tugas.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya menikmati mengambil risiko yang diperhitungkan untuk mencapai hasil yang lebih baik.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini mengukur toleransi risiko Anda dan kesediaan untuk mengambil peluang demi keuntungan potensial.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya mudah beradaptasi dengan perubahan yang tidak terduga.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini menilai kemampuan adaptasi dan fleksibilitas Anda ketika menghadapi situasi yang tidak terduga.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya lebih suka fokus pada satu tugas pada satu waktu daripada multitasking.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini mengevaluasi preferensi gaya kerja Anda antara fokus pada tugas tunggal dan multitasking.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya percaya penting untuk menjaga keseimbangan kerja-hidup.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini mengukur sikap Anda terhadap keseimbangan antara tanggung jawab profesional dengan kehidupan pribadi.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya lebih suka bekerja dengan deadline yang ketat.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini mengukur preferensi Anda terhadap tekanan waktu dan kemampuan bekerja di bawah tekanan.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya mudah bergaul dengan orang baru.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini menilai tingkat ekstroversi dan kemampuan sosial Anda.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya lebih suka mengikuti prosedur yang sudah ada daripada mencoba cara baru.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini mengukur preferensi Anda terhadap stabilitas versus inovasi.",
        difficulty: "easy",
        timeLimit: 30,
      },
      {
        category: "personality",
        type: "scale",
        question: "Saya merasa nyaman menjadi pusat perhatian.",
        options: ["Sangat Tidak Setuju", "Tidak Setuju", "Netral", "Setuju", "Sangat Setuju"],
        correctAnswer: undefined,
        explanation: "Ini menilai tingkat kenyamanan Anda dengan visibilitas dan perhatian publik.",
        difficulty: "easy",
        timeLimit: 30,
      },

      // Additional Logical Reasoning
      {
        category: "logical",
        type: "multiple-choice",
        question: "Jika beberapa kucing adalah anjing dan semua anjing adalah hewan, mana yang pasti benar?",
        options: ["Semua kucing adalah hewan", "Beberapa kucing adalah hewan", "Tidak ada kucing yang adalah hewan", "Semua hewan adalah kucing"],
        correctAnswer: "Beberapa kucing adalah hewan",
        explanation: "Karena beberapa kucing adalah anjing, dan semua anjing adalah hewan, maka kucing-kucing yang merupakan anjing juga pasti hewan.",
        difficulty: "medium",
        timeLimit: 75,
      },
      {
        category: "logical",
        type: "multiple-choice",
        question: "Apa angka yang hilang: 3, 7, 15, 31, ?",
        options: ["47", "55", "63", "71"],
        correctAnswer: "63",
        explanation: "Setiap angka adalah dua kali angka sebelumnya ditambah 1: 3×2+1=7, 7×2+1=15, 15×2+1=31, 31×2+1=63",
        difficulty: "hard",
        timeLimit: 120,
      },

      // Additional Verbal Ability
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Pilih kata yang paling melengkapi kalimat: 'Penyampaian pembicara yang _____ memikat audiens.'",
        options: ["monoton", "fasih", "ragu-ragu", "terburu-buru"],
        correctAnswer: "fasih",
        explanation: "Fasih berarti lancar dan persuasif dalam berbicara, yang akan memikat audiens.",
        difficulty: "medium",
        timeLimit: 45,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Pasangan kata mana yang merupakan antonim?",
        options: ["Senang - Gembira", "Besar - Luas", "Panas - Dingin", "Cepat - Kilat"],
        correctAnswer: "Panas - Dingin",
        explanation: "Antonim adalah kata-kata dengan makna berlawanan. Panas dan dingin adalah kebalikan.",
        difficulty: "easy",
        timeLimit: 30,
      },

      // Additional Numerical Reasoning
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Sebuah persegi panjang memiliki panjang 12 cm dan lebar 8 cm. Berapa luasnya?",
        options: ["20 cm²", "40 cm²", "96 cm²", "160 cm²"],
        correctAnswer: "96 cm²",
        explanation: "Luas persegi panjang = panjang × lebar = 12 cm × 8 cm = 96 cm²",
        difficulty: "easy",
        timeLimit: 45,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Jika 2/3 dari suatu bilangan adalah 24, berapa bilangan tersebut?",
        options: ["16", "32", "36", "48"],
        correctAnswer: "36",
        explanation: "Jika 2/3 × bilangan = 24, maka bilangan = 24 ÷ (2/3) = 24 × (3/2) = 36",
        difficulty: "medium",
        timeLimit: 75,
      },

      // More Advanced Questions
      {
        category: "logical",
        type: "multiple-choice",
        question: "Dalam kode tertentu, BUNGA ditulis sebagai ATOFZ. Bagaimana TAMAN ditulis?",
        options: ["SZLZM", "SZLZK", "SZLZL", "SZLZN"],
        correctAnswer: "SZLZM",
        explanation: "Setiap huruf digeser mundur 1 posisi dalam alfabet: T→S, A→Z, M→L, A→Z, N→M. Jadi TAMAN → SZLZM.",
        difficulty: "hard",
        timeLimit: 150,
      },
      {
        category: "verbal",
        type: "multiple-choice",
        question: "Pilih kata yang memiliki hubungan yang sama seperti 'Penulis : Novel':",
        options: ["Pelukis : Kuas", "Komposer : Simfoni", "Guru : Murid", "Dokter : Rumah Sakit"],
        correctAnswer: "Komposer : Simfoni",
        explanation: "Penulis menciptakan novel, sama seperti komposer menciptakan simfoni. Keduanya menunjukkan hubungan pencipta-ciptaan.",
        difficulty: "medium",
        timeLimit: 60,
      },
      {
        category: "numerical",
        type: "multiple-choice",
        question: "Rata-rata 5 bilangan adalah 20. Jika satu bilangan dihilangkan, rata-ratanya menjadi 18. Berapa bilangan yang dihilangkan?",
        options: ["24", "26", "28", "30"],
        correctAnswer: "28",
        explanation: "Jumlah 5 bilangan = 5 × 20 = 100. Jumlah 4 bilangan = 4 × 18 = 72. Bilangan yang dihilangkan = 100 - 72 = 28.",
        difficulty: "hard",
        timeLimit: 120,
      },
    ];

    // Insert all questions
    for (const question of questions) {
      await ctx.db.insert("questions", question);
    }

    return `Berhasil memuat ${questions.length} soal`;
  },
});
