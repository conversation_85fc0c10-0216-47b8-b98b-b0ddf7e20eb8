{"version": 3, "sources": ["../../@convex-dev/auth/src/react/index.tsx", "../../@convex-dev/auth/src/react/client.tsx"], "sourcesContent": ["/**\n * React bindings for Convex Auth.\n *\n * @module\n */\n\n\"use client\";\n\nimport { ConvexHttpClient } from \"convex/browser\";\nimport { ConvexProviderWithAuth, ConvexReactClient } from \"convex/react\";\nimport { Value } from \"convex/values\";\nimport { ReactNode, useContext, useMemo } from \"react\";\nimport {\n  AuthProvider,\n  ConvexAuthActionsContext,\n  ConvexAuthTokenContext,\n  useAuth,\n} from \"./client.js\";\nimport { AuthClient } from \"./clientType.js\";\n\n/**\n * Use this hook to access the `signIn` and `signOut` methods:\n *\n * ```ts\n * import { useAuthActions } from \"@convex-dev/auth/react\";\n *\n * function SomeComponent() {\n *   const { signIn, signOut } = useAuthActions();\n *   // ...\n * }\n * ```\n */\nexport function useAuthActions() {\n  return useContext(ConvexAuthActionsContext);\n}\n\n/**\n * Replace your `ConvexProvider` with this component to enable authentication.\n *\n * ```tsx\n * import { ConvexAuthProvider } from \"@convex-dev/auth/react\";\n * import { ConvexReactClient } from \"convex/react\";\n * import { ReactNode } from \"react\";\n *\n * const convex = new ConvexReactClient(/* ... *\\/);\n *\n * function RootComponent({ children }: { children: ReactNode }) {\n *   return <ConvexAuthProvider client={convex}>{children}</ConvexAuthProvider>;\n * }\n * ```\n */\nexport function ConvexAuthProvider(props: {\n  /**\n   * Your [`ConvexReactClient`](https://docs.convex.dev/api/classes/react.ConvexReactClient).\n   */\n  client: ConvexReactClient;\n  /**\n   * Optional custom storage object that implements\n   * the {@link TokenStorage} interface, otherwise\n   * [`localStorage`](https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage)\n   * is used.\n   *\n   * You must set this for React Native.\n   */\n  storage?: TokenStorage;\n  /**\n   * Optional namespace for keys used to store tokens. The keys\n   * determine whether the tokens are shared or not.\n   *\n   * Any non-alphanumeric characters will be ignored (for RN compatibility).\n   *\n   * Defaults to the deployment URL, as configured in the given `client`.\n   */\n  storageNamespace?: string;\n  /**\n   * Provide this function if you're using a JS router (Expo router etc.)\n   * and after OAuth or magic link sign-in the `code` param is not being\n   * erased from the URL.\n   *\n   * The implementation will depend on your chosen router.\n   */\n  replaceURL?: (\n    /**\n     * The URL, always starting with '/' and include the path, query and\n     * fragment components, that the window location should be set to.\n     */\n    relativeUrl: string,\n  ) => void | Promise<void>;\n  /**\n   * Children components can call Convex hooks\n   * and {@link useAuthActions}.\n   */\n  children: ReactNode;\n}) {\n  const { client, storage, storageNamespace, replaceURL, children } = props;\n  const authClient = useMemo(\n    () =>\n      ({\n        authenticatedCall(action, args) {\n          return client.action(action, args);\n        },\n        unauthenticatedCall(action, args) {\n          return new ConvexHttpClient((client as any).address).action(\n            action,\n            args,\n          );\n        },\n        verbose: (client as any).options?.verbose,\n      }) satisfies AuthClient,\n    [client],\n  );\n  return (\n    <AuthProvider\n      client={authClient}\n      storage={\n        storage ??\n        // Handle SSR, RN, Web, etc.\n        // Pretend we always have storage, the component checks\n        // it in first useEffect.\n        (typeof window === \"undefined\" ? undefined : window?.localStorage)!\n      }\n      storageNamespace={storageNamespace ?? (client as any).address}\n      replaceURL={\n        replaceURL ??\n        ((url) => {\n          window.history.replaceState({}, \"\", url);\n        })\n      }\n    >\n      <ConvexProviderWithAuth client={client} useAuth={useAuth}>\n        {children}\n      </ConvexProviderWithAuth>\n    </AuthProvider>\n  );\n}\n\n/**\n * A storage interface for storing and retrieving tokens and other secrets.\n *\n * In browsers `localStorage` and `sessionStorage` implement this interface.\n *\n * `sessionStorage` can be used for creating separate sessions for each\n * browser tab.\n *\n * In React Native we recommend wrapping `expo-secure-store`.\n */\nexport interface TokenStorage {\n  /**\n   * Read a value.\n   * @param key Unique key.\n   */\n  getItem: (\n    key: string,\n  ) => string | undefined | null | Promise<string | undefined | null>;\n  /**\n   * Write a value.\n   * @param key Unique key.\n   * @param value The value to store.\n   */\n  setItem: (key: string, value: string) => void | Promise<void>;\n  /**\n   * Remove a value.\n   * @param key Unique key.\n   */\n  removeItem: (key: string) => void | Promise<void>;\n}\n\n/**\n * The result of calling {@link useAuthActions}.\n */\nexport type ConvexAuthActionsContext = {\n  /**\n   * Sign in via one of your configured authentication providers.\n   *\n   * @returns Whether the user was immediately signed in (ie. the sign-in\n   *          didn't trigger an additional step like email verification\n   *          or OAuth signin).\n   */\n  signIn(\n    this: void,\n    /**\n     * The ID of the provider (lowercase version of the\n     * provider name or a configured `id` option value).\n     */\n    provider: string,\n    /**\n     * Either a `FormData` object containing the sign-in\n     *        parameters or a plain object containing them.\n     *        The shape required depends on the chosen provider's\n     *        implementation.\n     *\n     * Special fields:\n     *  - `redirectTo`: If provided, customizes the destination the user is\n     *     redirected to at the end of an OAuth flow or the magic link URL.\n     *     See [redirect callback](https://labs.convex.dev/auth/api_reference/server#callbacksredirect).\n     *  - `code`: OTP code for email or phone verification, or\n     *     (used only in RN) the code from an OAuth flow or magic link URL.\n     */\n    params?:\n      | FormData\n      | (Record<string, Value> & {\n          /**\n           * If provided, customizes the destination the user is\n           * redirected to at the end of an OAuth flow or the magic link URL.\n           */\n          redirectTo?: string;\n          /**\n           * OTP code for email or phone verification, or\n           * (used only in RN) the code from an OAuth flow or magic link URL.\n           */\n          code?: string;\n        }),\n  ): Promise<{\n    /**\n     * Whether the call led to an immediate successful sign-in.\n     *\n     * Note that there's a delay between the `signIn` function\n     * returning and the client performing the handshake with\n     * the server to confirm the sign-in.\n     */\n    signingIn: boolean;\n    /**\n     * If the sign-in started an OAuth flow, this is the URL\n     * the browser should be redirected to.\n     *\n     * Useful in RN for opening the in-app browser to\n     * this URL.\n     */\n    redirect?: URL;\n  }>;\n\n  /**\n   * Sign out the current user.\n   *\n   * Calls the server to invalidate the server session\n   * and deletes the locally stored JWT and refresh token.\n   */\n  signOut(this: void): Promise<void>;\n};\n\n/**\n * Use this hook to access the JWT token on the client, for authenticating\n * your Convex HTTP actions.\n *\n * You should not pass this token to other servers (think of it\n * as an \"ID token\").\n *\n * ```ts\n * import { useAuthToken } from \"@convex-dev/auth/react\";\n *\n * function SomeComponent() {\n *   const token = useAuthToken();\n *   const onClick = async () => {\n *     await fetch(`${CONVEX_SITE_URL}/someEndpoint`, {\n *       headers: {\n *         Authorization: `Bearer ${token}`,\n *       },\n *     });\n *   };\n *   // ...\n * }\n * ```\n */\nexport function useAuthToken() {\n  return useContext(ConvexAuthTokenContext);\n}\n", "import { Value } from \"convex/values\";\nimport {\n  ReactNode,\n  createContext,\n  useCallback,\n  useContext,\n  useEffect,\n  useMemo,\n  useRef,\n  useState,\n} from \"react\";\nimport type {\n  SignInAction,\n  SignOutAction,\n} from \"../server/implementation/index.js\";\nimport { AuthClient } from \"./clientType.js\";\nimport type {\n  ConvexAuthActionsContext as ConvexAuthActionsContextType,\n  TokenStorage,\n} from \"./index.js\";\n\nexport const ConvexAuthActionsContext =\n  createContext<ConvexAuthActionsContextType>(undefined as any);\n\nconst ConvexAuthInternalContext = createContext<{\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  fetchAccessToken: ({\n    forceRefreshToken,\n  }: {\n    forceRefreshToken: boolean;\n  }) => Promise<string | null>;\n}>(undefined as any);\n\nexport function useAuth() {\n  return useContext(ConvexAuthInternalContext);\n}\n\nexport const ConvexAuthTokenContext = createContext<string | null>(null);\n\nconst VERIFIER_STORAGE_KEY = \"__convexAuthOAuthVerifier\";\nconst JWT_STORAGE_KEY = \"__convexAuthJWT\";\nconst REFRESH_TOKEN_STORAGE_KEY = \"__convexAuthRefreshToken\";\nconst SERVER_STATE_FETCH_TIME_STORAGE_KEY = \"__convexAuthServerStateFetchTime\";\n\nexport function AuthProvider({\n  client,\n  serverState,\n  onChange,\n  storage,\n  storageNamespace,\n  replaceURL,\n  children,\n}: {\n  client: AuthClient;\n  serverState?: {\n    _state: { token: string | null; refreshToken: string | null };\n    _timeFetched: number;\n  };\n  onChange?: () => Promise<unknown>;\n  storage: TokenStorage | null;\n  storageNamespace: string;\n  replaceURL: (relativeUrl: string) => void | Promise<void>;\n  children: ReactNode;\n}) {\n  const token = useRef<string | null>(serverState?._state.token ?? null);\n  const [isLoading, setIsLoading] = useState(token.current === null);\n  const [tokenState, setTokenState] = useState<string | null>(token.current);\n\n  const verbose: boolean = client.verbose ?? false;\n  const logVerbose = useCallback(\n    (message: string) => {\n      if (verbose) {\n        console.debug(`${new Date().toISOString()} ${message}`);\n      }\n    },\n    [verbose],\n  );\n  const { storageSet, storageGet, storageRemove, storageKey } =\n    useNamespacedStorage(storage, storageNamespace);\n\n  const [isRefreshingToken, setIsRefreshingToken] = useState(false);\n  const setToken = useCallback(\n    async (\n      args:\n        | { shouldStore: true; tokens: { token: string; refreshToken: string } }\n        | { shouldStore: false; tokens: { token: string } }\n        | { shouldStore: boolean; tokens: null },\n    ) => {\n      const wasAuthenticated = token.current !== null;\n      let newToken: string | null;\n      if (args.tokens === null) {\n        token.current = null;\n        if (args.shouldStore) {\n          await storageRemove(JWT_STORAGE_KEY);\n          await storageRemove(REFRESH_TOKEN_STORAGE_KEY);\n        }\n        newToken = null;\n      } else {\n        const { token: value } = args.tokens;\n        token.current = value;\n        if (args.shouldStore) {\n          const { refreshToken } = args.tokens;\n          await storageSet(JWT_STORAGE_KEY, value);\n          await storageSet(REFRESH_TOKEN_STORAGE_KEY, refreshToken);\n        }\n        newToken = value;\n      }\n      if (wasAuthenticated !== (newToken !== null)) {\n        await onChange?.();\n      }\n      setTokenState(newToken);\n      setIsLoading(false);\n    },\n    [storageSet, storageRemove],\n  );\n\n  useEffect(() => {\n    const listener = async (e: Event) => {\n      if (isRefreshingToken) {\n        // There are 3 different ways to trigger this pop up so just try all of\n        // them.\n\n        e.preventDefault();\n        // This confirmation message doesn't actually appear in most modern\n        // browsers but we tried.\n        const confirmationMessage =\n          \"Are you sure you want to leave? Your changes may not be saved.\";\n        e.returnValue = true;\n        return confirmationMessage;\n      }\n    };\n    browserAddEventListener(\"beforeunload\", listener);\n    return () => {\n      browserRemoveEventListener(\"beforeunload\", listener);\n    };\n  });\n\n  useEffect(() => {\n    // We're listening for:\n    // 1. sibling tabs in case of localStorage\n    // 2. other frames in case of sessionStorage\n    const listener = (event: StorageEvent) => {\n      void (async () => {\n        // TODO: Test this if statement works in iframes correctly\n        if (event.storageArea !== storage) {\n          return;\n        }\n        // Another tab/frame set the access token, use it\n        if (event.key === storageKey(JWT_STORAGE_KEY)) {\n          const value = event.newValue;\n          logVerbose(`synced access token, is null: ${value === null}`);\n          // We don't write into storage since the event came from there and\n          // we'd trigger a loop, plus we get each key as a separate event so\n          // we don't have the refresh key here.\n          await setToken({\n            shouldStore: false,\n            tokens: value === null ? null : { token: value },\n          });\n        }\n      })();\n    };\n    browserAddEventListener(\"storage\", listener);\n    return () => browserRemoveEventListener(\"storage\", listener);\n  }, [setToken]);\n\n  const verifyCodeAndSetToken = useCallback(\n    async (\n      args: { code: string; verifier?: string } | { refreshToken: string },\n    ) => {\n      const { tokens } = await client.unauthenticatedCall(\n        \"auth:signIn\" as unknown as SignInAction,\n        \"code\" in args\n          ? { params: { code: args.code }, verifier: args.verifier }\n          : args,\n      );\n      logVerbose(`retrieved tokens, is null: ${tokens === null}`);\n      await setToken({ shouldStore: true, tokens: tokens ?? null });\n      return tokens !== null;\n    },\n    [client, setToken],\n  );\n\n  const signIn = useCallback(\n    async (provider?: string, args?: FormData | Record<string, Value>) => {\n      const params =\n        args instanceof FormData\n          ? Array.from(args.entries()).reduce(\n              (acc, [key, value]) => {\n                acc[key] = value as string;\n                return acc;\n              },\n              {} as Record<string, string>,\n            )\n          : args ?? {};\n\n      const verifier = (await storageGet(VERIFIER_STORAGE_KEY)) ?? undefined;\n      await storageRemove(VERIFIER_STORAGE_KEY);\n      const result = await client.authenticatedCall(\n        \"auth:signIn\" as unknown as SignInAction,\n        { provider, params, verifier },\n      );\n      if (result.redirect !== undefined) {\n        const url = new URL(result.redirect);\n        await storageSet(VERIFIER_STORAGE_KEY, result.verifier!);\n        // Do not redirect in React Native\n        if (window.location !== undefined) {\n          window.location.href = url.toString();\n        }\n        return { signingIn: false, redirect: url };\n      } else if (result.tokens !== undefined) {\n        const { tokens } = result;\n        logVerbose(`signed in and got tokens, is null: ${tokens === null}`);\n        await setToken({ shouldStore: true, tokens });\n        return { signingIn: result.tokens !== null };\n      }\n      return { signingIn: false };\n    },\n    [client, setToken, storageGet],\n  );\n\n  const signOut = useCallback(async () => {\n    try {\n      await client.authenticatedCall(\n        \"auth:signOut\" as unknown as SignOutAction,\n      );\n    } catch (error) {\n      // Ignore any errors, they are usually caused by being\n      // already signed out, which is ok.\n    }\n    logVerbose(`signed out, erasing tokens`);\n    await setToken({ shouldStore: true, tokens: null });\n  }, [setToken, client]);\n\n  const fetchAccessToken = useCallback(\n    async ({ forceRefreshToken }: { forceRefreshToken: boolean }) => {\n      if (forceRefreshToken) {\n        const tokenBeforeLockAquisition = token.current;\n        return await browserMutex(REFRESH_TOKEN_STORAGE_KEY, async () => {\n          const tokenAfterLockAquisition = token.current;\n          // Another tab or frame just refreshed the token, we can use it\n          // and skip another refresh.\n          if (tokenAfterLockAquisition !== tokenBeforeLockAquisition) {\n            logVerbose(\n              `returning synced token, is null: ${tokenAfterLockAquisition === null}`,\n            );\n            return tokenAfterLockAquisition;\n          }\n          const refreshToken =\n            (await storageGet(REFRESH_TOKEN_STORAGE_KEY)) ?? null;\n          if (refreshToken !== null) {\n            setIsRefreshingToken(true);\n            await verifyCodeAndSetToken({ refreshToken }).finally(() => {\n              setIsRefreshingToken(false);\n            });\n            logVerbose(\n              `returning retrieved token, is null: ${tokenAfterLockAquisition === null}`,\n            );\n            return token.current;\n          } else {\n            setIsRefreshingToken(false);\n            logVerbose(`returning null, there is no refresh token`);\n            return null;\n          }\n        });\n      }\n      return token.current;\n    },\n    [verifyCodeAndSetToken, signOut, storageGet],\n  );\n  const signingInWithCodeFromURL = useRef<boolean>(false);\n  useEffect(\n    () => {\n      // Has to happen in useEffect to avoid SSR.\n      if (storage === undefined) {\n        throw new Error(\n          \"`localStorage` is not available in this environment, \" +\n            \"set the `storage` prop on `ConvexAuthProvider`!\",\n        );\n      }\n      const readStateFromStorage = async () => {\n        const token = (await storageGet(JWT_STORAGE_KEY)) ?? null;\n        logVerbose(`retrieved token from storage, is null: ${token === null}`);\n        await setToken({\n          shouldStore: false,\n          tokens: token === null ? null : { token },\n        });\n      };\n\n      if (serverState !== undefined) {\n        // First check that this isn't a subsequent render\n        // with stale serverState.\n        const timeFetched = storageGet(SERVER_STATE_FETCH_TIME_STORAGE_KEY);\n        const setTokensFromServerState = (\n          timeFetched: string | null | undefined,\n        ) => {\n          if (!timeFetched || serverState._timeFetched > +timeFetched) {\n            const { token, refreshToken } = serverState._state;\n            const tokens =\n              token === null || refreshToken === null\n                ? null\n                : { token, refreshToken };\n            void storageSet(\n              SERVER_STATE_FETCH_TIME_STORAGE_KEY,\n              serverState._timeFetched.toString(),\n            );\n            void setToken({ tokens, shouldStore: true });\n          } else {\n            void readStateFromStorage();\n          }\n        };\n\n        // We want to avoid async if possible.\n        if (timeFetched instanceof Promise) {\n          void timeFetched.then(setTokensFromServerState);\n        } else {\n          setTokensFromServerState(timeFetched);\n        }\n\n        return;\n      }\n      const code =\n        typeof window?.location !== \"undefined\"\n          ? new URLSearchParams(window.location.search).get(\"code\")\n          : null;\n      // code from URL is only consumed initially,\n      // ref avoids racing in Strict mode\n      if (signingInWithCodeFromURL.current || code) {\n        if (code && !signingInWithCodeFromURL.current) {\n          signingInWithCodeFromURL.current = true;\n          const url = new URL(window.location.href);\n          url.searchParams.delete(\"code\");\n          void (async () => {\n            await replaceURL(url.pathname + url.search + url.hash);\n            await signIn(undefined, { code });\n            signingInWithCodeFromURL.current = false;\n          })();\n        }\n      } else {\n        void readStateFromStorage();\n      }\n    },\n    // Explicitly chosen dependencies.\n    // This effect should mostly only run once\n    // on mount.\n    [client, storageGet],\n  );\n\n  const actions = useMemo(() => ({ signIn, signOut }), [signIn, signOut]);\n  const isAuthenticated = tokenState !== null;\n  const authState = useMemo(\n    () => ({\n      isLoading,\n      isAuthenticated,\n      fetchAccessToken,\n    }),\n    [fetchAccessToken, isLoading, isAuthenticated],\n  );\n\n  return (\n    <ConvexAuthInternalContext.Provider value={authState}>\n      <ConvexAuthActionsContext.Provider value={actions}>\n        <ConvexAuthTokenContext.Provider value={tokenState}>\n          {children}\n        </ConvexAuthTokenContext.Provider>\n      </ConvexAuthActionsContext.Provider>\n    </ConvexAuthInternalContext.Provider>\n  );\n}\n\nfunction useNamespacedStorage(\n  peristentStorage: TokenStorage | null,\n  namespace: string,\n) {\n  const inMemoryStorage = useInMemoryStorage();\n  const storage = useMemo(\n    () => peristentStorage ?? inMemoryStorage(),\n    [peristentStorage],\n  );\n  const escapedNamespace = namespace.replace(/[^a-zA-Z0-9]/g, \"\");\n  const storageKey = useCallback(\n    (key: string) => `${key}_${escapedNamespace}`,\n    [namespace],\n  );\n  const storageSet = useCallback(\n    (key: string, value: string) => storage.setItem(storageKey(key), value),\n    [storage, storageKey],\n  );\n  const storageGet = useCallback(\n    (key: string) => storage.getItem(storageKey(key)),\n    [storage, storageKey],\n  );\n  const storageRemove = useCallback(\n    (key: string) => storage.removeItem(storageKey(key)),\n    [storage, storageKey],\n  );\n  return { storageSet, storageGet, storageRemove, storageKey };\n}\n\nfunction useInMemoryStorage() {\n  const [inMemoryStorage, setInMemoryStorage] = useState<\n    Record<string, string>\n  >({});\n  return () =>\n    ({\n      getItem: (key) => inMemoryStorage[key],\n      setItem: (key, value) => {\n        setInMemoryStorage((prev) => ({ ...prev, [key]: value }));\n      },\n      removeItem: (key) => {\n        setInMemoryStorage((prev) => {\n          const { [key]: _, ...rest } = prev;\n          return rest;\n        });\n      },\n    }) satisfies TokenStorage;\n}\n\n// In the browser, executes the callback as the only tab / frame at a time.\nasync function browserMutex<T>(\n  key: string,\n  callback: () => Promise<T>,\n): Promise<T> {\n  const lockManager = window?.navigator?.locks;\n  return lockManager !== undefined\n    ? await lockManager.request(key, callback)\n    : await manualMutex(key, callback);\n}\n\nfunction getMutexValue(key: string): {\n  currentlyRunning: Promise<void> | null;\n  waiting: Array<() => Promise<void>>;\n} {\n  if ((globalThis as any).__convexAuthMutexes === undefined) {\n    (globalThis as any).__convexAuthMutexes = {} as Record<\n      string,\n      {\n        currentlyRunning: Promise<void>;\n        waiting: Array<() => Promise<void>>;\n      }\n    >;\n  }\n  let mutex = (globalThis as any).__convexAuthMutexes[key];\n  if (mutex === undefined) {\n    (globalThis as any).__convexAuthMutexes[key] = {\n      currentlyRunning: null,\n      waiting: [],\n    };\n  }\n  mutex = (globalThis as any).__convexAuthMutexes[key];\n  return mutex;\n}\n\nfunction setMutexValue(\n  key: string,\n  value: {\n    currentlyRunning: Promise<void> | null;\n    waiting: Array<() => Promise<void>>;\n  },\n) {\n  (globalThis as any).__convexAuthMutexes[key] = value;\n}\n\nasync function enqueueCallbackForMutex(\n  key: string,\n  callback: () => Promise<void>,\n) {\n  const mutex = getMutexValue(key);\n  if (mutex.currentlyRunning === null) {\n    setMutexValue(key, {\n      currentlyRunning: callback().finally(() => {\n        const nextCb = getMutexValue(key).waiting.shift();\n        getMutexValue(key).currentlyRunning = null;\n        setMutexValue(key, {\n          ...getMutexValue(key),\n          currentlyRunning:\n            nextCb === undefined ? null : enqueueCallbackForMutex(key, nextCb),\n        });\n      }),\n      waiting: [],\n    });\n  } else {\n    setMutexValue(key, {\n      ...mutex,\n      waiting: [...mutex.waiting, callback],\n    });\n  }\n}\n\nasync function manualMutex<T>(\n  key: string,\n  callback: () => Promise<T>,\n): Promise<T> {\n  const outerPromise = new Promise<T>((resolve, reject) => {\n    const wrappedCallback: () => Promise<void> = () => {\n      return callback()\n        .then((v) => resolve(v))\n        .catch((e) => reject(e));\n    };\n    void enqueueCallbackForMutex(key, wrappedCallback);\n  });\n  return outerPromise;\n}\n\nfunction browserAddEventListener<K extends keyof WindowEventMap>(\n  type: K,\n  listener: (this: Window, ev: WindowEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions,\n): void {\n  window.addEventListener?.(type, listener, options);\n}\n\nfunction browserRemoveEventListener<K extends keyof WindowEventMap>(\n  type: K,\n  listener: (this: Window, ev: WindowEventMap[K]) => any,\n  options?: boolean | EventListenerOptions,\n): void {\n  window.removeEventListener?.(type, listener, options);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAWA,IAAAA,gBAA+C;;;;ACV/C,mBASO;AAWA,IAAM,+BACX,4BAA4C,MAAgB;AAE9D,IAAM,gCAA4B,4BAQ/B,MAAgB;AAEb,SAAU,UAAO;AACrB,aAAO,yBAAW,yBAAyB;AAC7C;AAEO,IAAM,6BAAyB,4BAA6B,IAAI;AAEvE,IAAM,uBAAuB;AAC7B,IAAM,kBAAkB;AACxB,IAAM,4BAA4B;AAClC,IAAM,sCAAsC;AAEtC,SAAU,aAAa,EAC3B,QACA,aACA,UACA,SACA,kBACA,YACA,SAAQ,GAYT;AACC,QAAM,YAAQ,sBAAsB,2CAAa,OAAO,UAAS,IAAI;AACrE,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,MAAM,YAAY,IAAI;AACjE,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAwB,MAAM,OAAO;AAEzE,QAAM,UAAmB,OAAO,WAAW;AAC3C,QAAM,iBAAa,0BACjB,CAAC,YAAmB;AAClB,QAAI,SAAS;AACX,cAAQ,MAAM,IAAG,oBAAI,KAAI,GAAG,YAAW,CAAE,IAAI,OAAO,EAAE;IACxD;EACF,GACA,CAAC,OAAO,CAAC;AAEX,QAAM,EAAE,YAAY,YAAY,eAAe,WAAU,IACvD,qBAAqB,SAAS,gBAAgB;AAEhD,QAAM,CAAC,mBAAmB,oBAAoB,QAAI,uBAAS,KAAK;AAChE,QAAM,eAAW,0BACf,OACE,SAIE;AACF,UAAM,mBAAmB,MAAM,YAAY;AAC3C,QAAI;AACJ,QAAI,KAAK,WAAW,MAAM;AACxB,YAAM,UAAU;AAChB,UAAI,KAAK,aAAa;AACpB,cAAM,cAAc,eAAe;AACnC,cAAM,cAAc,yBAAyB;MAC/C;AACA,iBAAW;IACb,OAAO;AACL,YAAM,EAAE,OAAO,MAAK,IAAK,KAAK;AAC9B,YAAM,UAAU;AAChB,UAAI,KAAK,aAAa;AACpB,cAAM,EAAE,aAAY,IAAK,KAAK;AAC9B,cAAM,WAAW,iBAAiB,KAAK;AACvC,cAAM,WAAW,2BAA2B,YAAY;MAC1D;AACA,iBAAW;IACb;AACA,QAAI,sBAAsB,aAAa,OAAO;AAC5C,aAAM;IACR;AACA,kBAAc,QAAQ;AACtB,iBAAa,KAAK;EACpB,GACA,CAAC,YAAY,aAAa,CAAC;AAG7B,8BAAU,MAAK;AACb,UAAM,WAAW,OAAO,MAAY;AAClC,UAAI,mBAAmB;AAIrB,UAAE,eAAc;AAGhB,cAAM,sBACJ;AACF,UAAE,cAAc;AAChB,eAAO;MACT;IACF;AACA,4BAAwB,gBAAgB,QAAQ;AAChD,WAAO,MAAK;AACV,iCAA2B,gBAAgB,QAAQ;IACrD;EACF,CAAC;AAED,8BAAU,MAAK;AAIb,UAAM,WAAW,CAAC,UAAuB;AACvC,YAAM,YAAW;AAEf,YAAI,MAAM,gBAAgB,SAAS;AACjC;QACF;AAEA,YAAI,MAAM,QAAQ,WAAW,eAAe,GAAG;AAC7C,gBAAM,QAAQ,MAAM;AACpB,qBAAW,iCAAiC,UAAU,IAAI,EAAE;AAI5D,gBAAM,SAAS;YACb,aAAa;YACb,QAAQ,UAAU,OAAO,OAAO,EAAE,OAAO,MAAK;WAC/C;QACH;MACF,GAAE;IACJ;AACA,4BAAwB,WAAW,QAAQ;AAC3C,WAAO,MAAM,2BAA2B,WAAW,QAAQ;EAC7D,GAAG,CAAC,QAAQ,CAAC;AAEb,QAAM,4BAAwB,0BAC5B,OACE,SACE;AACF,UAAM,EAAE,OAAM,IAAK,MAAM,OAAO,oBAC9B,eACA,UAAU,OACN,EAAE,QAAQ,EAAE,MAAM,KAAK,KAAI,GAAI,UAAU,KAAK,SAAQ,IACtD,IAAI;AAEV,eAAW,8BAA8B,WAAW,IAAI,EAAE;AAC1D,UAAM,SAAS,EAAE,aAAa,MAAM,QAAQ,UAAU,KAAI,CAAE;AAC5D,WAAO,WAAW;EACpB,GACA,CAAC,QAAQ,QAAQ,CAAC;AAGpB,QAAM,aAAS,0BACb,OAAO,UAAmB,SAA2C;AACnE,UAAM,SACJ,gBAAgB,WACZ,MAAM,KAAK,KAAK,QAAO,CAAE,EAAE,OACzB,CAAC,KAAK,CAAC,KAAK,KAAK,MAAK;AACpB,UAAI,GAAG,IAAI;AACX,aAAO;IACT,GACA,CAAA,CAA4B,IAE9B,QAAQ,CAAA;AAEd,UAAM,WAAY,MAAM,WAAW,oBAAoB,KAAM;AAC7D,UAAM,cAAc,oBAAoB;AACxC,UAAM,SAAS,MAAM,OAAO,kBAC1B,eACA,EAAE,UAAU,QAAQ,SAAQ,CAAE;AAEhC,QAAI,OAAO,aAAa,QAAW;AACjC,YAAM,MAAM,IAAI,IAAI,OAAO,QAAQ;AACnC,YAAM,WAAW,sBAAsB,OAAO,QAAS;AAEvD,UAAI,OAAO,aAAa,QAAW;AACjC,eAAO,SAAS,OAAO,IAAI,SAAQ;MACrC;AACA,aAAO,EAAE,WAAW,OAAO,UAAU,IAAG;IAC1C,WAAW,OAAO,WAAW,QAAW;AACtC,YAAM,EAAE,OAAM,IAAK;AACnB,iBAAW,sCAAsC,WAAW,IAAI,EAAE;AAClE,YAAM,SAAS,EAAE,aAAa,MAAM,OAAM,CAAE;AAC5C,aAAO,EAAE,WAAW,OAAO,WAAW,KAAI;IAC5C;AACA,WAAO,EAAE,WAAW,MAAK;EAC3B,GACA,CAAC,QAAQ,UAAU,UAAU,CAAC;AAGhC,QAAM,cAAU,0BAAY,YAAW;AACrC,QAAI;AACF,YAAM,OAAO,kBACX,cAA0C;IAE9C,SAAS,OAAO;IAGhB;AACA,eAAW,4BAA4B;AACvC,UAAM,SAAS,EAAE,aAAa,MAAM,QAAQ,KAAI,CAAE;EACpD,GAAG,CAAC,UAAU,MAAM,CAAC;AAErB,QAAM,uBAAmB,0BACvB,OAAO,EAAE,kBAAiB,MAAsC;AAC9D,QAAI,mBAAmB;AACrB,YAAM,4BAA4B,MAAM;AACxC,aAAO,MAAM,aAAa,2BAA2B,YAAW;AAC9D,cAAM,2BAA2B,MAAM;AAGvC,YAAI,6BAA6B,2BAA2B;AAC1D,qBACE,oCAAoC,6BAA6B,IAAI,EAAE;AAEzE,iBAAO;QACT;AACA,cAAM,eACH,MAAM,WAAW,yBAAyB,KAAM;AACnD,YAAI,iBAAiB,MAAM;AACzB,+BAAqB,IAAI;AACzB,gBAAM,sBAAsB,EAAE,aAAY,CAAE,EAAE,QAAQ,MAAK;AACzD,iCAAqB,KAAK;UAC5B,CAAC;AACD,qBACE,uCAAuC,6BAA6B,IAAI,EAAE;AAE5E,iBAAO,MAAM;QACf,OAAO;AACL,+BAAqB,KAAK;AAC1B,qBAAW,2CAA2C;AACtD,iBAAO;QACT;MACF,CAAC;IACH;AACA,WAAO,MAAM;EACf,GACA,CAAC,uBAAuB,SAAS,UAAU,CAAC;AAE9C,QAAM,+BAA2B,qBAAgB,KAAK;AACtD;IACE,MAAK;AAEH,UAAI,YAAY,QAAW;AACzB,cAAM,IAAI,MACR,sGACmD;MAEvD;AACA,YAAM,uBAAuB,YAAW;AACtC,cAAMC,SAAS,MAAM,WAAW,eAAe,KAAM;AACrD,mBAAW,0CAA0CA,WAAU,IAAI,EAAE;AACrE,cAAM,SAAS;UACb,aAAa;UACb,QAAQA,WAAU,OAAO,OAAO,EAAE,OAAAA,OAAK;SACxC;MACH;AAEA,UAAI,gBAAgB,QAAW;AAG7B,cAAM,cAAc,WAAW,mCAAmC;AAClE,cAAM,2BAA2B,CAC/BC,iBACE;AACF,cAAI,CAACA,gBAAe,YAAY,eAAe,CAACA,cAAa;AAC3D,kBAAM,EAAE,OAAAD,QAAO,aAAY,IAAK,YAAY;AAC5C,kBAAM,SACJA,WAAU,QAAQ,iBAAiB,OAC/B,OACA,EAAE,OAAAA,QAAO,aAAY;AAC3B,iBAAK,WACH,qCACA,YAAY,aAAa,SAAQ,CAAE;AAErC,iBAAK,SAAS,EAAE,QAAQ,aAAa,KAAI,CAAE;UAC7C,OAAO;AACL,iBAAK,qBAAoB;UAC3B;QACF;AAGA,YAAI,uBAAuB,SAAS;AAClC,eAAK,YAAY,KAAK,wBAAwB;QAChD,OAAO;AACL,mCAAyB,WAAW;QACtC;AAEA;MACF;AACA,YAAM,OACJ,QAAO,iCAAQ,cAAa,cACxB,IAAI,gBAAgB,OAAO,SAAS,MAAM,EAAE,IAAI,MAAM,IACtD;AAGN,UAAI,yBAAyB,WAAW,MAAM;AAC5C,YAAI,QAAQ,CAAC,yBAAyB,SAAS;AAC7C,mCAAyB,UAAU;AACnC,gBAAM,MAAM,IAAI,IAAI,OAAO,SAAS,IAAI;AACxC,cAAI,aAAa,OAAO,MAAM;AAC9B,gBAAM,YAAW;AACf,kBAAM,WAAW,IAAI,WAAW,IAAI,SAAS,IAAI,IAAI;AACrD,kBAAM,OAAO,QAAW,EAAE,KAAI,CAAE;AAChC,qCAAyB,UAAU;UACrC,GAAE;QACJ;MACF,OAAO;AACL,aAAK,qBAAoB;MAC3B;IACF;;;;IAIA,CAAC,QAAQ,UAAU;EAAC;AAGtB,QAAM,cAAU,sBAAQ,OAAO,EAAE,QAAQ,QAAO,IAAK,CAAC,QAAQ,OAAO,CAAC;AACtE,QAAM,kBAAkB,eAAe;AACvC,QAAM,gBAAY,sBAChB,OAAO;IACL;IACA;IACA;MAEF,CAAC,kBAAkB,WAAW,eAAe,CAAC;AAGhD,aACE,mBAAAE,KAAC,0BAA0B,UAAQ,EAAC,OAAO,WAAS,cAClD,mBAAAA,KAAC,yBAAyB,UAAQ,EAAC,OAAO,SAAO,cAC/C,mBAAAA,KAAC,uBAAuB,UAAQ,EAAC,OAAO,YAAU,SACvC,CAAA,EACuB,CAAA,EACA,CAAA;AAG1C;AAEA,SAAS,qBACP,kBACA,WAAiB;AAEjB,QAAM,kBAAkB,mBAAkB;AAC1C,QAAM,cAAU,sBACd,MAAM,oBAAoB,gBAAe,GACzC,CAAC,gBAAgB,CAAC;AAEpB,QAAM,mBAAmB,UAAU,QAAQ,iBAAiB,EAAE;AAC9D,QAAM,iBAAa,0BACjB,CAAC,QAAgB,GAAG,GAAG,IAAI,gBAAgB,IAC3C,CAAC,SAAS,CAAC;AAEb,QAAM,iBAAa,0BACjB,CAAC,KAAa,UAAkB,QAAQ,QAAQ,WAAW,GAAG,GAAG,KAAK,GACtE,CAAC,SAAS,UAAU,CAAC;AAEvB,QAAM,iBAAa,0BACjB,CAAC,QAAgB,QAAQ,QAAQ,WAAW,GAAG,CAAC,GAChD,CAAC,SAAS,UAAU,CAAC;AAEvB,QAAM,oBAAgB,0BACpB,CAAC,QAAgB,QAAQ,WAAW,WAAW,GAAG,CAAC,GACnD,CAAC,SAAS,UAAU,CAAC;AAEvB,SAAO,EAAE,YAAY,YAAY,eAAe,WAAU;AAC5D;AAEA,SAAS,qBAAkB;AACzB,QAAM,CAAC,iBAAiB,kBAAkB,QAAI,uBAE5C,CAAA,CAAE;AACJ,SAAO,OACJ;IACC,SAAS,CAAC,QAAQ,gBAAgB,GAAG;IACrC,SAAS,CAAC,KAAK,UAAS;AACtB,yBAAmB,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAK,EAAG;IAC1D;IACA,YAAY,CAAC,QAAO;AAClB,yBAAmB,CAAC,SAAQ;AAC1B,cAAM,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,KAAI,IAAK;AAC9B,eAAO;MACT,CAAC;IACH;;AAEN;AAGA,eAAe,aACb,KACA,UAA0B;;AAE1B,QAAM,eAAc,sCAAQ,cAAR,mBAAmB;AACvC,SAAO,gBAAgB,SACnB,MAAM,YAAY,QAAQ,KAAK,QAAQ,IACvC,MAAM,YAAY,KAAK,QAAQ;AACrC;AAEA,SAAS,cAAc,KAAW;AAIhC,MAAK,WAAmB,wBAAwB,QAAW;AACxD,eAAmB,sBAAsB,CAAA;EAO5C;AACA,MAAI,QAAS,WAAmB,oBAAoB,GAAG;AACvD,MAAI,UAAU,QAAW;AACtB,eAAmB,oBAAoB,GAAG,IAAI;MAC7C,kBAAkB;MAClB,SAAS,CAAA;;EAEb;AACA,UAAS,WAAmB,oBAAoB,GAAG;AACnD,SAAO;AACT;AAEA,SAAS,cACP,KACA,OAGC;AAEA,aAAmB,oBAAoB,GAAG,IAAI;AACjD;AAEA,eAAe,wBACb,KACA,UAA6B;AAE7B,QAAM,QAAQ,cAAc,GAAG;AAC/B,MAAI,MAAM,qBAAqB,MAAM;AACnC,kBAAc,KAAK;MACjB,kBAAkB,SAAQ,EAAG,QAAQ,MAAK;AACxC,cAAM,SAAS,cAAc,GAAG,EAAE,QAAQ,MAAK;AAC/C,sBAAc,GAAG,EAAE,mBAAmB;AACtC,sBAAc,KAAK;UACjB,GAAG,cAAc,GAAG;UACpB,kBACE,WAAW,SAAY,OAAO,wBAAwB,KAAK,MAAM;SACpE;MACH,CAAC;MACD,SAAS,CAAA;KACV;EACH,OAAO;AACL,kBAAc,KAAK;MACjB,GAAG;MACH,SAAS,CAAC,GAAG,MAAM,SAAS,QAAQ;KACrC;EACH;AACF;AAEA,eAAe,YACb,KACA,UAA0B;AAE1B,QAAM,eAAe,IAAI,QAAW,CAAC,SAAS,WAAU;AACtD,UAAM,kBAAuC,MAAK;AAChD,aAAO,SAAQ,EACZ,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC,EACtB,MAAM,CAAC,MAAM,OAAO,CAAC,CAAC;IAC3B;AACA,SAAK,wBAAwB,KAAK,eAAe;EACnD,CAAC;AACD,SAAO;AACT;AAEA,SAAS,wBACP,MACA,UACA,SAA2C;;AAE3C,eAAO,qBAAP,gCAA0B,MAAM,UAAU;AAC5C;AAEA,SAAS,2BACP,MACA,UACA,SAAwC;;AAExC,eAAO,wBAAP,gCAA6B,MAAM,UAAU;AAC/C;;;ADteM,SAAU,iBAAc;AAC5B,aAAO,0BAAW,wBAAwB;AAC5C;AAiBM,SAAU,mBAAmB,OA0ClC;AACC,QAAM,EAAE,QAAQ,SAAS,kBAAkB,YAAY,SAAQ,IAAK;AACpE,QAAM,iBAAa,uBACjB,MAAG;AAhGP;AAiGO;MACC,kBAAkB,QAAQ,MAAI;AAC5B,eAAO,OAAO,OAAO,QAAQ,IAAI;MACnC;MACA,oBAAoB,QAAQ,MAAI;AAC9B,eAAO,IAAI,iBAAkB,OAAe,OAAO,EAAE,OACnD,QACA,IAAI;MAER;MACA,UAAU,YAAe,YAAf,mBAAwB;;KAEtC,CAAC,MAAM,CAAC;AAEV,aACE,oBAAAC,KAAC,cAAY,EACX,QAAQ,YACR,SACE;;;GAIC,OAAO,WAAW,cAAc,SAAY,iCAAQ,eAEvD,kBAAkB,oBAAqB,OAAe,SACtD,YACE,eACC,CAAC,QAAO;AACP,WAAO,QAAQ,aAAa,CAAA,GAAI,IAAI,GAAG;EACzC,IAAE,cAGJ,oBAAAA,KAAC,wBAAsB,EAAC,QAAgB,SAAgB,SAC7C,CAAA,EACc,CAAA;AAG/B;AAiIM,SAAU,eAAY;AAC1B,aAAO,0BAAW,sBAAsB;AAC1C;", "names": ["import_react", "token", "timeFetched", "_jsx", "_jsx"]}