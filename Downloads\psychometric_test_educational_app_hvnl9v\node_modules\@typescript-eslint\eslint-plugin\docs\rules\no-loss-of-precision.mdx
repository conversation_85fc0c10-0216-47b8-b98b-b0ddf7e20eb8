---
description: 'Disallow literal numbers that lose precision.'
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/no-loss-of-precision** for documentation.

:::danger Deprecated

This rule has been deprecated because the base [`eslint/no-loss-of-precision`](https://eslint.org/docs/rules/no-loss-of-precision) rule added support for [numeric separators](https://github.com/tc39/proposal-numeric-separator).
There is no longer any need to use this extension rule.

:::
