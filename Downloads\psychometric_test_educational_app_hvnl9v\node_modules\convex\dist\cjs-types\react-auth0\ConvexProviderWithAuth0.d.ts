import React from "react";
import { ReactNode } from "react";
import { AuthTokenFetcher } from "../browser/sync/client.js";
type IConvexReactClient = {
    setAuth(fetchToken: AuthTokenFetcher): void;
    clearAuth(): void;
};
/**
 * A wrapper React component which provides a {@link react.ConvexReactClient}
 * authenticated with Auth0.
 *
 * It must be wrapped by a configured `Auth0Provider` from `@auth0/auth0-react`.
 *
 * See [Convex Auth0](https://docs.convex.dev/auth/auth0) on how to set up
 * Convex with Auth0.
 *
 * @public
 */
export declare function ConvexProviderWithAuth0({ children, client, }: {
    children: ReactNode;
    client: IConvexReactClient;
}): React.JSX.Element;
export {};
//# sourceMappingURL=ConvexProviderWithAuth0.d.ts.map