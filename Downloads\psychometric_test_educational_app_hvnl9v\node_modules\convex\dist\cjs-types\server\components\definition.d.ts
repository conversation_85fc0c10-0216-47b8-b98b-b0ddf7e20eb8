export type ComponentDefinitionExport = {
    name: string;
    path: string;
    definitionType: {
        type: "childComponent";
        name: string;
        args: [string, {
            type: "value";
            value: string;
        }][];
    };
    childComponents: [];
    exports: {
        type: "branch";
        branch: [];
    };
};
export type ComponentDefinitionType = {
    type: "childComponent";
    name: string;
    args: [string, {
        type: "value";
        value: string;
    }][];
};
export type AppDefinitionType = {
    type: "app";
};
type ComponentInstantiation = {
    name: string;
    path: string;
    args: [string, {
        type: "value";
        value: string;
    }][];
};
export type HttpMount = string;
type ComponentExport = {
    type: "branch";
    branch: [string, ComponentExport][];
} | {
    type: "leaf";
    leaf: string;
};
export type ComponentDefinitionAnalysis = {
    name: string;
    definitionType: ComponentDefinitionType;
    childComponents: ComponentInstantiation[];
    httpMounts: Record<string, HttpMount>;
    exports: ComponentExport;
};
export type AppDefinitionAnalysis = {
    definitionType: AppDefinitionType;
    childComponents: ComponentInstantiation[];
    httpMounts: Record<string, HttpMount>;
    exports: ComponentExport;
};
export {};
//# sourceMappingURL=definition.d.ts.map