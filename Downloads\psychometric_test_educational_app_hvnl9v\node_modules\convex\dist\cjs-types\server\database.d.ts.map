{"version": 3, "file": "database.d.ts", "sourceRoot": "", "sources": ["../../../src/server/database.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EACL,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,qBAAqB,EACtB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAC9C,OAAO,EACL,wBAAwB,EACxB,mBAAmB,EACpB,MAAM,oBAAoB,CAAC;AAE5B,UAAU,kBAAkB,CAAC,SAAS,SAAS,gBAAgB;IAC7D;;;;;OAKG;IACH,GAAG,CAAC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,EACpD,EAAE,EAAE,SAAS,CAAC,SAAS,CAAC,GACvB,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;IAExD;;;;;;;;OAQG;IACH,KAAK,CAAC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,EACtD,SAAS,EAAE,SAAS,GACnB,gBAAgB,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;IAE1D;;;;;;;;;;;OAWG;IACH,WAAW,CAAC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,EAC5D,SAAS,EAAE,SAAS,EACpB,EAAE,EAAE,MAAM,GACT,SAAS,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;CAChC;AAED,UAAU,2BAA2B,CAAC,SAAS,SAAS,gBAAgB;IACtE;;OAEG;IACH,KAAK,CAAC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,EACtD,SAAS,EAAE,SAAS,GACnB,eAAe,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;CAC1C;AAED,MAAM,WAAW,eAAe,CAC9B,SAAS,SAAS,gBAAgB,EAClC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC;IAElD;;;;;OAKG;IACH,GAAG,CACD,EAAE,EAAE,SAAS,CAAC,SAAS,CAAC,GACvB,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;IAExD;;;;;;;OAOG;IACH,KAAK,IAAI,gBAAgB,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;CACjE;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,WAAW,qBAAqB,CAAC,SAAS,SAAS,gBAAgB,CACvE,SAAQ,kBAAkB,CAAC,SAAS,CAAC;IACrC;;;;;;;;;OASG;IACH,MAAM,EAAE,kBAAkB,CAAC,eAAe,CAAC,CAAC;CAC7C;AAED,MAAM,WAAW,8BAA8B,CAC7C,SAAS,SAAS,gBAAgB,CAClC,SAAQ,2BAA2B,CAAC,SAAS,CAAC;IAC9C;;;;;;;;;OASG;IACH,MAAM,EAAE,2BAA2B,CAAC,eAAe,CAAC,CAAC;CACtD;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,WAAW,qBAAqB,CAAC,SAAS,SAAS,gBAAgB,CACvE,SAAQ,qBAAqB,CAAC,SAAS,CAAC;IACxC;;;;;;OAMG;IACH,MAAM,CAAC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,EACvD,KAAK,EAAE,SAAS,EAChB,KAAK,EAAE,mBAAmB,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,GAC/D,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;IAEjC;;;;;;;;;;OAUG;IACH,KAAK,CAAC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,EACtD,EAAE,EAAE,SAAS,CAAC,SAAS,CAAC,EACxB,KAAK,EAAE,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,GACnD,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB;;;;;;OAMG;IACH,OAAO,CAAC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,EACxD,EAAE,EAAE,SAAS,CAAC,SAAS,CAAC,EACxB,KAAK,EAAE,wBAAwB,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,GACpE,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB;;;;OAIG;IACH,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CACxE;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,WAAW,8BAA8B,CAC7C,SAAS,SAAS,gBAAgB,CAClC,SAAQ,8BAA8B,CAAC,SAAS,CAAC;IACjD;;OAEG;IACH,KAAK,CAAC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,EACtD,SAAS,EAAE,SAAS,GACnB,eAAe,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;CAC1C;AAED,MAAM,WAAW,eAAe,CAC9B,SAAS,SAAS,gBAAgB,EAClC,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,CAClD,SAAQ,eAAe,CAAC,SAAS,EAAE,SAAS,CAAC;IAC7C;;;;;OAKG;IACH,MAAM,CACJ,KAAK,EAAE,mBAAmB,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,GAC/D,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;IAEjC;;;;;;;;;;OAUG;IACH,KAAK,CACH,EAAE,EAAE,SAAS,CAAC,SAAS,CAAC,EACxB,KAAK,EAAE,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,GACnD,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB;;;;;;OAMG;IACH,OAAO,CACL,EAAE,EAAE,SAAS,CAAC,SAAS,CAAC,EACxB,KAAK,EAAE,wBAAwB,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,GACpE,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB;;;;OAIG;IACH,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CACjD"}