import { Value } from "../../values/index.js";
import { FunctionReference } from "../../server/api.js";
export declare function setupActionCalls(requestId: string): {
    runQuery: (query: FunctionReference<"query", "public" | "internal">, args?: Record<string, Value>) => Promise<any>;
    runMutation: (mutation: FunctionReference<"mutation", "public" | "internal">, args?: Record<string, Value>) => Promise<any>;
    runAction: (action: FunctionReference<"action", "public" | "internal">, args?: Record<string, Value>) => Promise<any>;
};
//# sourceMappingURL=actions_impl.d.ts.map