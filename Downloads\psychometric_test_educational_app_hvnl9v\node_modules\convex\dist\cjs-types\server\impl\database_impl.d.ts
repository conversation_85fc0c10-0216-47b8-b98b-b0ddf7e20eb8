import { GenericDatabaseReader, GenericDatabaseWriter, GenericDatabaseWriterWithTable } from "../database.js";
import { GenericDataModel } from "../data_model.js";
export declare function setupReader(): GenericDatabaseReader<GenericDataModel>;
export declare function setupWriter(): GenericDatabaseWriter<GenericDataModel> & GenericDatabaseWriterWithTable<GenericDataModel>;
//# sourceMappingURL=database_impl.d.ts.map