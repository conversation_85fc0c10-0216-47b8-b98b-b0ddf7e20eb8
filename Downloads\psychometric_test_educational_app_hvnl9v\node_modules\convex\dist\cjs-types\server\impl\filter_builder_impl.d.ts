import { JSONValue, Value } from "../../values/index.js";
import { GenericTableInfo } from "../data_model.js";
import { Expression, ExpressionOrValue, FilterBuilder } from "../filter_builder.js";
export declare class ExpressionImpl extends Expression<any> {
    private inner;
    constructor(inner: JSONValue);
    serialize(): JSONValue;
}
export declare function serializeExpression(expr: ExpressionOrValue<Value | undefined>): JSONValue;
export declare const filterBuilderImpl: FilterBuilder<GenericTableInfo>;
//# sourceMappingURL=filter_builder_impl.d.ts.map