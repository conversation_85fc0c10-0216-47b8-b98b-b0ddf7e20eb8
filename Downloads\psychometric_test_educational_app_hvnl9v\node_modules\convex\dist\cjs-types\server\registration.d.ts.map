{"version": 3, "file": "registration.d.ts", "sourceRoot": "", "sources": ["../../../src/server/registration.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,IAAI,EACJ,qBAAqB,EACrB,8BAA8B,EAC9B,qBAAqB,EACrB,8BAA8B,EAC9B,mBAAmB,EACnB,aAAa,EACb,aAAa,EACd,MAAM,YAAY,CAAC;AACpB,OAAO,EACL,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAChB,yBAAyB,EAC1B,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EACL,gBAAgB,EAChB,KAAK,EACL,UAAU,EACV,kBAAkB,EACnB,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,EAAE,EAAE,MAAM,oBAAoB,CAAC;AACxC,OAAO,EACL,gBAAgB,EAChB,cAAc,EACd,qBAAqB,EACrB,gBAAgB,EACjB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AACvD,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAC1C,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAEpD;;;;;;;;;;GAUG;AACH,MAAM,WAAW,kBAAkB,CAAC,SAAS,SAAS,gBAAgB;IACpE;;OAEG;IACH,EAAE,EAAE,qBAAqB,CAAC,SAAS,CAAC,CAAC;IAErC;;OAEG;IACH,IAAI,EAAE,IAAI,CAAC;IAEX;;OAEG;IACH,OAAO,EAAE,aAAa,CAAC;IAEvB;;OAEG;IACH,SAAS,EAAE,SAAS,CAAC;IAErB;;;;;;OAMG;IACH,QAAQ,EAAE,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,UAAU,CAAC,EACxE,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,KAC7B,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;IAExC;;;;;;;;;;OAUG;IACH,WAAW,EAAE,CACX,QAAQ,SAAS,iBAAiB,CAAC,UAAU,EAAE,QAAQ,GAAG,UAAU,CAAC,EAErE,QAAQ,EAAE,QAAQ,EAClB,GAAG,IAAI,EAAE,gBAAgB,CAAC,QAAQ,CAAC,KAChC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;CAC5C;AAED;;;;;;;;;;GAUG;AACH,MAAM,MAAM,2BAA2B,CAAC,SAAS,SAAS,gBAAgB,IACxE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,GAAG;IAC1C,EAAE,EAAE,8BAA8B,CAAC,SAAS,CAAC,CAAC;CAC/C,CAAC;AAEJ;;;;;;;;;;;GAWG;AACH,MAAM,WAAW,eAAe,CAAC,SAAS,SAAS,gBAAgB;IACjE;;OAEG;IACH,EAAE,EAAE,qBAAqB,CAAC,SAAS,CAAC,CAAC;IAErC;;OAEG;IACH,IAAI,EAAE,IAAI,CAAC;IAEX;;OAEG;IACH,OAAO,EAAE,aAAa,CAAC;IAEvB;;;;;;OAMG;IACH,QAAQ,EAAE,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,UAAU,CAAC,EACxE,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,KAC7B,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;CACzC;AAED;;;;;;;;;;;GAWG;AACH,MAAM,MAAM,wBAAwB,CAAC,SAAS,SAAS,gBAAgB,IAAI,IAAI,CAC7E,eAAe,CAAC,SAAS,CAAC,EAC1B,IAAI,CACL,GAAG;IACF,EAAE,EAAE,8BAA8B,CAAC,SAAS,CAAC,CAAC;CAC/C,CAAC;AAEF;;;;;;;;;;GAUG;AACH,MAAM,WAAW,gBAAgB,CAAC,SAAS,SAAS,gBAAgB;IAClE;;;;;;;;;OASG;IACH,QAAQ,CAAC,KAAK,SAAS,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,UAAU,CAAC,EACtE,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,GAC/B,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;IAEtC;;;;;;;;;OASG;IACH,WAAW,CACT,QAAQ,SAAS,iBAAiB,CAAC,UAAU,EAAE,QAAQ,GAAG,UAAU,CAAC,EAErE,QAAQ,EAAE,QAAQ,EAClB,GAAG,IAAI,EAAE,gBAAgB,CAAC,QAAQ,CAAC,GAClC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEzC;;;;;;;;;OASG;IACH,SAAS,CAAC,MAAM,SAAS,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,GAAG,UAAU,CAAC,EACzE,MAAM,EAAE,MAAM,EACd,GAAG,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,GAChC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;IAEvC;;OAEG;IACH,SAAS,EAAE,SAAS,CAAC;IAErB;;OAEG;IACH,IAAI,EAAE,IAAI,CAAC;IAEX;;OAEG;IACH,OAAO,EAAE,mBAAmB,CAAC;IAE7B;;;;;;;;;OASG;IACH,YAAY,CACV,SAAS,SAAS,qBAAqB,CAAC,SAAS,CAAC,EAClD,SAAS,SAAS,gBAAgB,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,EAExE,SAAS,EAAE,SAAS,EACpB,SAAS,EAAE,SAAS,EACpB,KAAK,EAAE,MAAM,CACX,iBAAiB,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC,CACnE,GACA,OAAO,CAAC,KAAK,CAAC;QAAE,GAAG,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC,CAAC;CAC3D;AAED;;;;;;;GAOG;AACH,MAAM,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAE1D;;;;GAIG;AACH,KAAK,WAAW,CAAC,UAAU,SAAS,mBAAmB,GAAG,mBAAmB,IAC3E;IAAC,UAAU;CAAC,CAAC;AAEf;;GAEG;AACH,KAAK,WAAW,GAAG,EAAE,CAAC;AAEtB;;;;;;;GAOG;AACH,MAAM,MAAM,SAAS,GAAG,WAAW,GAAG,WAAW,CAAC;AAElD;;;;GAIG;AACH,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAEhD;;;;;GAKG;AACH,MAAM,MAAM,iBAAiB,CAAC,IAAI,SAAS,SAAS,IAClD,IAAI,SAAS,WAAW,CAAC,MAAM,UAAU,CAAC,GAAG,UAAU,GAAG,WAAW,CAAC;AAExE;;;;GAIG;AACH,MAAM,MAAM,kBAAkB,GAAG,QAAQ,GAAG,UAAU,CAAC;AAEvD;;;GAGG;AACH,KAAK,oBAAoB,CAAC,SAAS,SAAS,kBAAkB,IAC5D,SAAS,SAAS,QAAQ,GACtB;IACE,QAAQ,EAAE,IAAI,CAAC;CAChB,GACD;IACE,UAAU,EAAE,IAAI,CAAC;CAClB,CAAC;AAER;;;;;;;GAOG;AACH,MAAM,MAAM,kBAAkB,CAC5B,UAAU,SAAS,kBAAkB,EACrC,IAAI,SAAS,mBAAmB,EAChC,OAAO,IACL;IACF,gBAAgB,EAAE,IAAI,CAAC;IACvB,UAAU,EAAE,IAAI,CAAC;CAalB,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAErC;;;;;;;GAOG;AACH,MAAM,MAAM,eAAe,CACzB,UAAU,SAAS,kBAAkB,EACrC,IAAI,SAAS,mBAAmB,EAChC,OAAO,IACL;IACF,gBAAgB,EAAE,IAAI,CAAC;IACvB,OAAO,EAAE,IAAI,CAAC;CAaf,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAErC;;;;;;;GAOG;AACH,MAAM,MAAM,gBAAgB,CAC1B,UAAU,SAAS,kBAAkB,EACrC,IAAI,SAAS,mBAAmB,EAChC,OAAO,IACL;IACF,gBAAgB,EAAE,IAAI,CAAC;IACvB,QAAQ,EAAE,IAAI,CAAC;CAahB,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAErC;;;;;;;GAOG;AACH,MAAM,MAAM,gBAAgB,GAAG;IAC7B,MAAM,EAAE,IAAI,CAAC;CAMd,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,MAAM,mBAAmB,CAAC,GAAG,EAAE,IAAI,SAAS,SAAS,EAAE,OAAO,IAChE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC,GACtC;IACE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC;CAC/C,CAAC;AAEN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,WAAW,iBAAiB,CAChC,GAAG,EACH,aAAa,SAAS,kBAAkB,EACxC,OAAO;IAEP;;;;;;;;;;;;;;OAcG;IACH,IAAI,EAAE,aAAa,CAAC;IAEpB;;;;;;;;;;;OAWG;IACH,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,aAAa,CAAC,KAAK,OAAO,CAAC;CACjE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AAEH,MAAM,MAAM,+BAA+B,CACzC,gBAAgB,SAAS,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,kBAAkB,GAAG,IAAI,IAC3E,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GACrD,yBAAyB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,GAClD,CAAC,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAC7C,yBAAyB,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,GACvD,GAAG,CAAC;AAEV,MAAM,MAAM,6BAA6B,CACvC,aAAa,SAAS,gBAAgB,GAAG,kBAAkB,GAAG,IAAI,IAChE,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAClD,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,GACjC,CAAC,aAAa,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAC1C,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,GACtC,SAAS,CAAC;AAEhB,MAAM,MAAM,+BAA+B,CACzC,aAAa,SAAS,gBAAgB,GAAG,kBAAkB,GAAG,IAAI,IAChE,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAClD,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,GACtB,CAAC,aAAa,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAC1C,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,GAC3B,WAAW,CAAC;AAElB;;;;;GAKG;AACH,MAAM,MAAM,eAAe,CACzB,SAAS,SAAS,gBAAgB,EAClC,UAAU,SAAS,kBAAkB,IACnC;IACF,CACE,aAAa,SACT,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,gBAAgB,SACZ,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,WAAW,SAAS,+BAA+B,CAAC,gBAAgB,CAAC,GAAG,GAAG,EAC3E,aAAa,SACX,6BAA6B,CAAC,aAAa,CAAC,GAAG,+BAA+B,CAAC,aAAa,CAAC,EAE/F,QAAQ,EACJ;QACE;;;;;;;;;;;WAWG;QACH,IAAI,CAAC,EAAE,aAAa,CAAC;QACrB;;;;;;;;;;;WAWG;QACH,OAAO,CAAC,EAAE,gBAAgB,CAAC;QAC3B;;;;;;;;;;;WAWG;QACH,OAAO,EAAE,CACP,GAAG,EAAE,kBAAkB,CAAC,SAAS,CAAC,EAClC,GAAG,IAAI,EAAE,aAAa,KACnB,WAAW,CAAC;KAClB,GACD;QACE;;;;;;;;;;;WAWG;QACH,CACE,GAAG,EAAE,kBAAkB,CAAC,SAAS,CAAC,EAClC,GAAG,IAAI,EAAE,aAAa,GACrB,WAAW,CAAC;KAChB,GACJ,kBAAkB,CACnB,UAAU,EACV,iBAAiB,CAAC,aAAa,CAAC,EAChC,WAAW,CACZ,CAAC;CACH,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,wBAAwB,CAClC,SAAS,SAAS,gBAAgB,EAClC,UAAU,SAAS,kBAAkB,IACnC;IACF,CACE,aAAa,SACT,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,gBAAgB,SACZ,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,WAAW,SAAS,+BAA+B,CAAC,gBAAgB,CAAC,GAAG,GAAG,EAC3E,aAAa,SACX,6BAA6B,CAAC,aAAa,CAAC,GAAG,+BAA+B,CAAC,aAAa,CAAC,EAE/F,QAAQ,EACJ;QACE;;;;;;;;;;;WAWG;QACH,IAAI,CAAC,EAAE,aAAa,CAAC;QACrB;;;;;;;;;;;WAWG;QACH,OAAO,CAAC,EAAE,gBAAgB,CAAC;QAC3B;;;;;;;;;;;WAWG;QACH,OAAO,EAAE,CACP,GAAG,EAAE,2BAA2B,CAAC,SAAS,CAAC,EAC3C,GAAG,IAAI,EAAE,aAAa,KACnB,WAAW,CAAC;KAClB,GACD;QACE;;;;;;;;;;;WAWG;QACH,CACE,GAAG,EAAE,2BAA2B,CAAC,SAAS,CAAC,EAC3C,GAAG,IAAI,EAAE,aAAa,GACrB,WAAW,CAAC;KAChB,GACJ,kBAAkB,CACnB,UAAU,EACV,iBAAiB,CAAC,aAAa,CAAC,EAChC,WAAW,CACZ,CAAC;CACH,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,YAAY,CACtB,SAAS,SAAS,gBAAgB,EAClC,UAAU,SAAS,kBAAkB,IACnC;IACF,CACE,aAAa,SACT,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,gBAAgB,SACZ,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,WAAW,SAAS,+BAA+B,CAAC,gBAAgB,CAAC,GAAG,GAAG,EAC3E,aAAa,SACX,6BAA6B,CAAC,aAAa,CAAC,GAAG,+BAA+B,CAAC,aAAa,CAAC,EAE/F,KAAK,EACD;QACE;;;;;;;;;;;WAWG;QACH,IAAI,CAAC,EAAE,aAAa,CAAC;QACrB;;;;;;;;;;;WAWG;QACH,OAAO,CAAC,EAAE,gBAAgB,CAAC;QAC3B;;;;;;;;;;;WAWG;QACH,OAAO,EAAE,CACP,GAAG,EAAE,eAAe,CAAC,SAAS,CAAC,EAC/B,GAAG,IAAI,EAAE,aAAa,KACnB,WAAW,CAAC;KAClB,GACD;QACE;;;;;;;;;;;WAWG;QACH,CACE,GAAG,EAAE,eAAe,CAAC,SAAS,CAAC,EAC/B,GAAG,IAAI,EAAE,aAAa,GACrB,WAAW,CAAC;KAChB,GACJ,eAAe,CAAC,UAAU,EAAE,iBAAiB,CAAC,aAAa,CAAC,EAAE,WAAW,CAAC,CAAC;CAC/E,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,qBAAqB,CAC/B,SAAS,SAAS,gBAAgB,EAClC,UAAU,SAAS,kBAAkB,IACnC;IACF,CACE,aAAa,SACT,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,gBAAgB,SACZ,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,WAAW,SAAS,+BAA+B,CAAC,gBAAgB,CAAC,GAAG,GAAG,EAC3E,aAAa,SACX,6BAA6B,CAAC,aAAa,CAAC,GAAG,+BAA+B,CAAC,aAAa,CAAC,EAE/F,KAAK,EACD;QACE;;;;;;;;;;;WAWG;QACH,IAAI,CAAC,EAAE,aAAa,CAAC;QACrB;;;;;;;;;;;WAWG;QACH,OAAO,CAAC,EAAE,gBAAgB,CAAC;QAC3B;;;;;;;;;;;WAWG;QACH,OAAO,EAAE,CACP,GAAG,EAAE,wBAAwB,CAAC,SAAS,CAAC,EACxC,GAAG,IAAI,EAAE,aAAa,KACnB,WAAW,CAAC;KAClB,GACD;QACE;;;;;;;;;;;WAWG;QACH,CACE,GAAG,EAAE,wBAAwB,CAAC,SAAS,CAAC,EACxC,GAAG,IAAI,EAAE,aAAa,GACrB,WAAW,CAAC;KAChB,GACJ,eAAe,CAAC,UAAU,EAAE,iBAAiB,CAAC,aAAa,CAAC,EAAE,WAAW,CAAC,CAAC;CAC/E,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,aAAa,CACvB,SAAS,SAAS,gBAAgB,EAClC,UAAU,SAAS,kBAAkB,IACnC;IACF,CACE,aAAa,SACT,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,gBAAgB,SACZ,kBAAkB,GAClB,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,CAAC,GAC/B,IAAI,EACR,WAAW,SAAS,+BAA+B,CAAC,gBAAgB,CAAC,GAAG,GAAG,EAC3E,aAAa,SACX,6BAA6B,CAAC,aAAa,CAAC,GAAG,+BAA+B,CAAC,aAAa,CAAC,EAE/F,IAAI,EACA;QACE;;;;;;;;;;;;WAYG;QACH,IAAI,CAAC,EAAE,aAAa,CAAC;QACrB;;;;;;;;;;;WAWG;QACH,OAAO,CAAC,EAAE,gBAAgB,CAAC;QAC3B;;;;;;;;;;;WAWG;QACH,OAAO,EAAE,CACP,GAAG,EAAE,gBAAgB,CAAC,SAAS,CAAC,EAChC,GAAG,IAAI,EAAE,aAAa,KACnB,WAAW,CAAC;KAClB,GACD;QACE;;;;;;;;;;;WAWG;QACH,CACE,GAAG,EAAE,gBAAgB,CAAC,SAAS,CAAC,EAChC,GAAG,IAAI,EAAE,aAAa,GACrB,WAAW,CAAC;KAChB,GACJ,gBAAgB,CACjB,UAAU,EACV,iBAAiB,CAAC,aAAa,CAAC,EAChC,WAAW,CACZ,CAAC;CACH,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,MAAM,iBAAiB,GAAG,CAC9B,IAAI,EAAE,CAAC,GAAG,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,CAAC,QAAQ,CAAC,KACtE,gBAAgB,CAAC"}