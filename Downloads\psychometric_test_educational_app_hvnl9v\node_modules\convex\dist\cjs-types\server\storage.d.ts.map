{"version": 3, "file": "storage.d.ts", "sourceRoot": "", "sources": ["../../../src/server/storage.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAE/C;;;;;;;GAOG;AACH,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC;AAC/B,MAAM,MAAM,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;AAC9D;;;;GAIG;AACH,MAAM,MAAM,YAAY,GAAG;IACzB;;OAEG;IACH,SAAS,EAAE,SAAS,CAAC;IACrB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;CAC5B,CAAC;AAEF;;;;GAIG;AACH,MAAM,WAAW,aAAa;IAC5B;;;;;;;OAOG;IACH,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;IAEjE;;;;;;;;;OASG;IACH,MAAM,CAAC,CAAC,SAAS,SAAS,EACxB,SAAS,EAAE,CAAC,SAAS;QAAE,WAAW,EAAE,GAAG,CAAA;KAAE,GAAG,KAAK,GAAG,CAAC,GACpD,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;IAE1B;;;;;;;OAOG;IACH,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;IAE5E;;;;;;;OAOG;IACH,WAAW,CAAC,CAAC,SAAS,SAAS,EAC7B,SAAS,EAAE,CAAC,SAAS;QAAE,WAAW,EAAE,GAAG,CAAA;KAAE,GAAG,KAAK,GAAG,CAAC,GACpD,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;CACjC;AAED;;;;GAIG;AACH,MAAM,WAAW,aAAc,SAAQ,aAAa;IAClD;;;;;;;;OAQG;IACH,iBAAiB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;IACrC;;;;;;OAMG;IACH,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAExD;;;;;;;;OAQG;IACH,MAAM,CAAC,CAAC,SAAS,SAAS,EACxB,SAAS,EAAE,CAAC,SAAS;QAAE,WAAW,EAAE,GAAG,CAAA;KAAE,GAAG,KAAK,GAAG,CAAC,GACpD,OAAO,CAAC,IAAI,CAAC,CAAC;CAClB;AAED;;;;GAIG;AACH,MAAM,WAAW,mBAAoB,SAAQ,aAAa;IACxD;;OAEG;IACH,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IAE5D;;;;OAIG;IACH,GAAG,CAAC,CAAC,SAAS,SAAS,EACrB,SAAS,EAAE,CAAC,SAAS;QAAE,WAAW,EAAE,GAAG,CAAA;KAAE,GAAG,KAAK,GAAG,CAAC,GACpD,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IACxB;;;;OAIG;IACH,KAAK,CACH,IAAI,EAAE,IAAI,EACV,OAAO,CAAC,EAAE;QAAE,MAAM,CAAC,EAAE,MAAM,CAAA;KAAE,GAC5B,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;CACnC"}