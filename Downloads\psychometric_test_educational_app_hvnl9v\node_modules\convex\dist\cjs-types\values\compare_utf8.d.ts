/**
 * Taken from https://github.com/rocicorp/compare-utf8/blob/main/LICENSE
 * (Apache Version 2.0, January 2004)
 */
/**
 * This is copied here instead of added as a dependency to avoid bundling issues.
 */
/**
 * Compares two JavaScript strings as if they were UTF-8 encoded byte arrays.
 * @param {string} a
 * @param {string} b
 * @returns {number}
 */
export declare function compareUTF8(a: string, b: string): number;
/**
 * @param {number} aCodePoint
 * @returns {number}
 */
export declare function utf16LengthForCodePoint(aCodePoint: number): 1 | 2;
/**
 * @param {string} a
 * @param {string} b
 * @returns {boolean}
 */
export declare function greaterThan(a: string, b: string): boolean;
/**
 * @param {string} a
 * @param {string} b
 * @returns {boolean}
 */
export declare function greaterThanEq(a: string, b: string): boolean;
/**
 * @param {string} a
 * @param {string} b
 * @returns {boolean}
 */
export declare function lessThan(a: string, b: string): boolean;
/**
 * @param {string} a
 * @param {string} b
 * @returns {boolean}
 */
export declare function lessThanEq(a: string, b: string): boolean;
//# sourceMappingURL=compare_utf8.d.ts.map