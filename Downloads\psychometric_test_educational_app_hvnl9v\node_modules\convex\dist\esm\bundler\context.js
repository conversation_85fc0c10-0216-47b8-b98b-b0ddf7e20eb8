"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
import * as Sentry from "@sentry/node";
import chalk from "chalk";
import ora from "ora";
import { nodeFs } from "./fs.js";
import { format } from "util";
import ProgressBar from "progress";
import { initializeBigBrainAuth } from "../cli/lib/deploymentSelection.js";
async function flushAndExit(exitCode, err) {
  if (err) {
    Sentry.captureException(err);
  }
  await Sentry.close();
  return process.exit(exitCode);
}
class OneoffContextImpl {
  constructor() {
    __publicField(this, "_cleanupFns", {});
    __publicField(this, "fs", nodeFs);
    __publicField(this, "deprecationMessagePrinted", false);
    __publicField(this, "spinner");
    __publicField(this, "_bigBrainAuth", null);
    __publicField(this, "crash", async (args) => {
      if (args.printedMessage !== null) {
        logFailure(this, args.printedMessage);
      }
      return await this.flushAndExit(args.exitCode, args.errForSentry);
    });
    __publicField(this, "flushAndExit", async (exitCode, err) => {
      logVerbose(this, "Flushing and exiting, error:", err);
      if (err) {
        logVerbose(this, err.stack);
      }
      const cleanupFns = this._cleanupFns;
      this._cleanupFns = {};
      const fns = Object.values(cleanupFns);
      logVerbose(this, `Running ${fns.length} cleanup functions`);
      for (const fn of fns) {
        await fn(exitCode, err);
      }
      logVerbose(this, "All cleanup functions ran");
      return flushAndExit(exitCode, err);
    });
  }
  registerCleanup(fn) {
    const handle = Math.random().toString(36).slice(2);
    this._cleanupFns[handle] = fn;
    return handle;
  }
  removeCleanup(handle) {
    const value = this._cleanupFns[handle];
    delete this._cleanupFns[handle];
    return value ?? null;
  }
  bigBrainAuth() {
    return this._bigBrainAuth;
  }
  _updateBigBrainAuth(auth) {
    logVerbose(this, `Updating big brain auth to ${auth?.kind ?? "null"}`);
    this._bigBrainAuth = auth;
  }
}
export const oneoffContext = async (args) => {
  const ctx = new OneoffContextImpl();
  await initializeBigBrainAuth(ctx, {
    url: args.url,
    adminKey: args.adminKey,
    envFile: args.envFile
  });
  return ctx;
};
function logToStderr(...args) {
  process.stderr.write(`${format(...args)}
`);
}
export function logError(ctx, message) {
  ctx.spinner?.clear();
  logToStderr(message);
}
export function logWarning(ctx, ...logged) {
  ctx.spinner?.clear();
  logToStderr(...logged);
}
export function logMessage(ctx, ...logged) {
  ctx.spinner?.clear();
  logToStderr(...logged);
}
export function logOutput(ctx, ...logged) {
  ctx.spinner?.clear();
  console.log(...logged);
}
export function logVerbose(ctx, ...logged) {
  if (process.env.CONVEX_VERBOSE) {
    logMessage(ctx, `[verbose] ${(/* @__PURE__ */ new Date()).toISOString()}`, ...logged);
  }
}
export function startLogProgress(ctx, format2, progressBarOptions) {
  ctx.spinner?.clear();
  return new ProgressBar(format2, progressBarOptions);
}
export function showSpinner(ctx, message) {
  ctx.spinner?.stop();
  ctx.spinner = ora({
    // Add newline to prevent clobbering when a message
    // we can't pipe through `logMessage` et al gets printed
    text: message + "\n",
    stream: process.stderr,
    // hideCursor: true doesn't work with `tsx`.
    // see https://github.com/tapjs/signal-exit/issues/49#issuecomment-1459408082
    // See CX-6822 for an issue to bring back cursor hiding, probably by upgrading libraries.
    hideCursor: process.env.CONVEX_RUNNING_LIVE_IN_MONOREPO ? false : true
  }).start();
}
export function changeSpinner(ctx, message) {
  if (ctx.spinner) {
    ctx.spinner.text = message + "\n";
  } else {
    logToStderr(message);
  }
}
export function logFailure(ctx, message) {
  if (ctx.spinner) {
    ctx.spinner.fail(message);
    ctx.spinner = void 0;
  } else {
    logToStderr(`${chalk.red(`\u2716`)} ${message}`);
  }
}
export function logFinishedStep(ctx, message) {
  if (ctx.spinner) {
    ctx.spinner.succeed(message);
    ctx.spinner = void 0;
  } else {
    logToStderr(`${chalk.green(`\u2714`)} ${message}`);
  }
}
export function stopSpinner(ctx) {
  if (ctx.spinner) {
    ctx.spinner.stop();
    ctx.spinner = void 0;
  }
}
export async function showSpinnerIfSlow(ctx, message, delayMs, fn) {
  const timeout = setTimeout(() => {
    showSpinner(ctx, message);
  }, delayMs);
  await fn();
  clearTimeout(timeout);
}
//# sourceMappingURL=context.js.map
