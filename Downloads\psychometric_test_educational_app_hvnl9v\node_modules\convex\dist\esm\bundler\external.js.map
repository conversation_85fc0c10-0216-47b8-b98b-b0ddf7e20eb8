{"version": 3, "sources": ["../../../src/bundler/external.ts"], "sourcesContent": ["import { PluginB<PERSON> } from \"esbuild\";\nimport type { Plugin } from \"esbuild\";\nimport { Context } from \"./context.js\";\nimport path from \"path\";\n\nimport { findUp } from \"find-up\";\nimport { findParentConfigs } from \"../cli/lib/utils/utils.js\";\n\n/**\n * Mimics Node.js node_modules resolution. Ideally we would be able to\n * reuse the logic in esbuild but calling build.resolve() from onResolve()\n * results in infinite recursion. See https://esbuild.github.io/plugins/#resolve\n */\nasync function resolveNodeModule(\n  ctx: Context,\n  moduleDir: string,\n  resolveDir: string,\n): Promise<string | null> {\n  let nodeModulesPath: string | undefined;\n\n  while (\n    (nodeModulesPath = await findUp(\"node_modules\", {\n      type: \"directory\",\n      cwd: resolveDir,\n    }))\n  ) {\n    const maybePath = path.join(nodeModulesPath, moduleDir);\n    if (ctx.fs.exists(maybePath)) {\n      return maybePath;\n    }\n    resolveDir = path.dirname(path.dirname(nodeModulesPath));\n  }\n\n  return null;\n}\n\nfunction getModule(importPath: string): { name: string; dirName: string } {\n  // In case of scoped package\n  if (importPath.startsWith(\"@\")) {\n    const split = importPath.split(\"/\");\n    return {\n      name: `${split[0]}/${split[1]}`,\n      dirName: path.join(split[0], split[1]),\n    };\n  } else {\n    const moduleName = importPath.split(\"/\")[0];\n    return {\n      name: moduleName,\n      dirName: moduleName,\n    };\n  }\n}\n\nexport type ExternalPackage = {\n  path: string;\n};\n\n// Inspired by https://www.npmjs.com/package/esbuild-node-externals.\nexport function createExternalPlugin(\n  ctx: Context,\n  externalPackages: Map<string, ExternalPackage>,\n): {\n  plugin: Plugin;\n  externalModuleNames: Set<string>;\n  bundledModuleNames: Set<string>;\n} {\n  const externalModuleNames = new Set<string>();\n  const bundledModuleNames = new Set<string>();\n  return {\n    plugin: {\n      name: \"convex-node-externals\",\n      setup(build: PluginBuild) {\n        // On every module resolved, we check if the module name should be an external\n        build.onResolve({ namespace: \"file\", filter: /.*/ }, async (args) => {\n          if (args.path.startsWith(\".\")) {\n            // Relative import.\n            return null;\n          }\n\n          const module = getModule(args.path);\n          const externalPackage = externalPackages.get(module.name);\n          if (externalPackage) {\n            const resolved = await resolveNodeModule(\n              ctx,\n              module.dirName,\n              args.resolveDir,\n            );\n            if (resolved && externalPackage.path === resolved) {\n              // Mark as external.\n              externalModuleNames.add(module.name);\n              return { path: args.path, external: true };\n            }\n          }\n\n          bundledModuleNames.add(module.name);\n          return null;\n        });\n      },\n    },\n    externalModuleNames: externalModuleNames,\n    bundledModuleNames: bundledModuleNames,\n  };\n}\n\n// Returns the versions of the packages referenced by the package.json.\nexport async function computeExternalPackages(\n  ctx: Context,\n  externalPackagesAllowList: string[],\n): Promise<Map<string, ExternalPackage>> {\n  if (externalPackagesAllowList.length === 0) {\n    // No external packages in the allow list.\n    return new Map<string, ExternalPackage>();\n  }\n\n  const { parentPackageJson: packageJsonPath } = await findParentConfigs(ctx);\n  const externalPackages = new Map<string, ExternalPackage>();\n  let packageJson: any;\n  try {\n    const packageJsonString = ctx.fs.readUtf8File(packageJsonPath);\n    packageJson = JSON.parse(packageJsonString);\n  } catch (error: any) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `Couldn't parse \"${packageJsonPath}\". Make sure it's a valid JSON. Error: ${error}`,\n    });\n  }\n\n  for (const key of [\n    \"dependencies\",\n    \"devDependencies\",\n    \"peerDependencies\",\n    \"optionalDependencies\",\n  ]) {\n    for (const [packageName, packageJsonVersion] of Object.entries(\n      packageJson[key] ?? {},\n    )) {\n      if (externalPackages.has(packageName)) {\n        // Package version and path already found.\n        continue;\n      }\n\n      if (typeof packageJsonVersion !== \"string\") {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"invalid filesystem data\",\n          printedMessage: `Invalid \"${packageJsonPath}\". \"${key}.${packageName}\" version has type ${typeof packageJsonVersion}.`,\n        });\n      }\n\n      if (\n        !shouldMarkExternal(\n          packageName,\n          packageJsonVersion,\n          externalPackagesAllowList,\n        )\n      ) {\n        // Package should be bundled.\n        continue;\n      }\n\n      // Check if the package path is referenced.\n      const packagePath = path.join(\n        path.dirname(packageJsonPath),\n        \"node_modules\",\n        getModule(packageName).dirName,\n      );\n      if (ctx.fs.exists(packagePath)) {\n        externalPackages.set(packageName, {\n          path: packagePath,\n        });\n      }\n    }\n  }\n\n  return externalPackages;\n}\n\nexport function shouldMarkExternal(\n  packageName: string,\n  packageJsonVersion: string,\n  externalPackagesAllowList: string[],\n): boolean {\n  // Always bundle convex.\n  if (packageName === \"convex\") {\n    return false;\n  }\n\n  if (\n    packageJsonVersion.startsWith(\"file:\") ||\n    packageJsonVersion.startsWith(\"git+file://\")\n  ) {\n    // Bundle instead of marking as external.\n    return false;\n  }\n  if (\n    packageJsonVersion.startsWith(\"http://\") ||\n    packageJsonVersion.startsWith(\"https://\") ||\n    packageJsonVersion.startsWith(\"git://\") ||\n    packageJsonVersion.startsWith(\"git+ssh://\") ||\n    packageJsonVersion.startsWith(\"git+http://\") ||\n    packageJsonVersion.startsWith(\"git+https://\")\n  ) {\n    // Installing those might or might not work. There are some corner cases\n    // like http://127.0.0.1/. Lets bundle for time being.\n    return false;\n  }\n\n  return (\n    externalPackagesAllowList.includes(packageName) ||\n    externalPackagesAllowList.includes(\"*\")\n  );\n}\n\nexport async function findExactVersionAndDependencies(\n  ctx: Context,\n  moduleName: string,\n  modulePath: string,\n): Promise<{\n  version: string;\n  peerAndOptionalDependencies: Set<string>;\n}> {\n  const modulePackageJsonPath = path.join(modulePath, \"package.json\");\n  let modulePackageJson: any;\n  try {\n    const packageJsonString = ctx.fs.readUtf8File(modulePackageJsonPath);\n    modulePackageJson = JSON.parse(packageJsonString);\n  } catch {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `Missing \"${modulePackageJsonPath}\", which is required for\n      installing external package \"${moduleName}\" configured in convex.json.`,\n    });\n  }\n  if (modulePackageJson[\"version\"] === undefined) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `\"${modulePackageJsonPath}\" misses a 'version' field. which is required for\n      installing external package \"${moduleName}\" configured in convex.json.`,\n    });\n  }\n\n  const peerAndOptionalDependencies = new Set<string>();\n  for (const key of [\"peerDependencies\", \"optionalDependencies\"]) {\n    for (const [packageName, packageJsonVersion] of Object.entries(\n      modulePackageJson[key] ?? {},\n    )) {\n      if (typeof packageJsonVersion !== \"string\") {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"invalid filesystem data\",\n          printedMessage: `Invalid \"${modulePackageJsonPath}\". \"${key}.${packageName}\" version has type ${typeof packageJsonVersion}.`,\n        });\n      }\n      peerAndOptionalDependencies.add(packageName);\n    }\n  }\n\n  return {\n    version: modulePackageJson[\"version\"],\n    peerAndOptionalDependencies: peerAndOptionalDependencies,\n  };\n}\n"], "mappings": ";AAGA,OAAO,UAAU;AAEjB,SAAS,cAAc;AACvB,SAAS,yBAAyB;AAOlC,eAAe,kBACb,KACA,WACA,YACwB;AACxB,MAAI;AAEJ,SACG,kBAAkB,MAAM,OAAO,gBAAgB;AAAA,IAC9C,MAAM;AAAA,IACN,KAAK;AAAA,EACP,CAAC,GACD;AACA,UAAM,YAAY,KAAK,KAAK,iBAAiB,SAAS;AACtD,QAAI,IAAI,GAAG,OAAO,SAAS,GAAG;AAC5B,aAAO;AAAA,IACT;AACA,iBAAa,KAAK,QAAQ,KAAK,QAAQ,eAAe,CAAC;AAAA,EACzD;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,YAAuD;AAExE,MAAI,WAAW,WAAW,GAAG,GAAG;AAC9B,UAAM,QAAQ,WAAW,MAAM,GAAG;AAClC,WAAO;AAAA,MACL,MAAM,GAAG,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MAC7B,SAAS,KAAK,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,IACvC;AAAA,EACF,OAAO;AACL,UAAM,aAAa,WAAW,MAAM,GAAG,EAAE,CAAC;AAC1C,WAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AACF;AAOO,gBAAS,qBACd,KACA,kBAKA;AACA,QAAM,sBAAsB,oBAAI,IAAY;AAC5C,QAAM,qBAAqB,oBAAI,IAAY;AAC3C,SAAO;AAAA,IACL,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,MAAM,OAAoB;AAExB,cAAM,UAAU,EAAE,WAAW,QAAQ,QAAQ,KAAK,GAAG,OAAO,SAAS;AACnE,cAAI,KAAK,KAAK,WAAW,GAAG,GAAG;AAE7B,mBAAO;AAAA,UACT;AAEA,gBAAM,SAAS,UAAU,KAAK,IAAI;AAClC,gBAAM,kBAAkB,iBAAiB,IAAI,OAAO,IAAI;AACxD,cAAI,iBAAiB;AACnB,kBAAM,WAAW,MAAM;AAAA,cACrB;AAAA,cACA,OAAO;AAAA,cACP,KAAK;AAAA,YACP;AACA,gBAAI,YAAY,gBAAgB,SAAS,UAAU;AAEjD,kCAAoB,IAAI,OAAO,IAAI;AACnC,qBAAO,EAAE,MAAM,KAAK,MAAM,UAAU,KAAK;AAAA,YAC3C;AAAA,UACF;AAEA,6BAAmB,IAAI,OAAO,IAAI;AAClC,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,sBAAsB,wBACpB,KACA,2BACuC;AACvC,MAAI,0BAA0B,WAAW,GAAG;AAE1C,WAAO,oBAAI,IAA6B;AAAA,EAC1C;AAEA,QAAM,EAAE,mBAAmB,gBAAgB,IAAI,MAAM,kBAAkB,GAAG;AAC1E,QAAM,mBAAmB,oBAAI,IAA6B;AAC1D,MAAI;AACJ,MAAI;AACF,UAAM,oBAAoB,IAAI,GAAG,aAAa,eAAe;AAC7D,kBAAc,KAAK,MAAM,iBAAiB;AAAA,EAC5C,SAAS,OAAY;AACnB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,mBAAmB,eAAe,0CAA0C,KAAK;AAAA,IACnG,CAAC;AAAA,EACH;AAEA,aAAW,OAAO;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,eAAW,CAAC,aAAa,kBAAkB,KAAK,OAAO;AAAA,MACrD,YAAY,GAAG,KAAK,CAAC;AAAA,IACvB,GAAG;AACD,UAAI,iBAAiB,IAAI,WAAW,GAAG;AAErC;AAAA,MACF;AAEA,UAAI,OAAO,uBAAuB,UAAU;AAC1C,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,YAAY,eAAe,OAAO,GAAG,IAAI,WAAW,sBAAsB,OAAO,kBAAkB;AAAA,QACrH,CAAC;AAAA,MACH;AAEA,UACE,CAAC;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,MACF,GACA;AAEA;AAAA,MACF;AAGA,YAAM,cAAc,KAAK;AAAA,QACvB,KAAK,QAAQ,eAAe;AAAA,QAC5B;AAAA,QACA,UAAU,WAAW,EAAE;AAAA,MACzB;AACA,UAAI,IAAI,GAAG,OAAO,WAAW,GAAG;AAC9B,yBAAiB,IAAI,aAAa;AAAA,UAChC,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,gBAAS,mBACd,aACA,oBACA,2BACS;AAET,MAAI,gBAAgB,UAAU;AAC5B,WAAO;AAAA,EACT;AAEA,MACE,mBAAmB,WAAW,OAAO,KACrC,mBAAmB,WAAW,aAAa,GAC3C;AAEA,WAAO;AAAA,EACT;AACA,MACE,mBAAmB,WAAW,SAAS,KACvC,mBAAmB,WAAW,UAAU,KACxC,mBAAmB,WAAW,QAAQ,KACtC,mBAAmB,WAAW,YAAY,KAC1C,mBAAmB,WAAW,aAAa,KAC3C,mBAAmB,WAAW,cAAc,GAC5C;AAGA,WAAO;AAAA,EACT;AAEA,SACE,0BAA0B,SAAS,WAAW,KAC9C,0BAA0B,SAAS,GAAG;AAE1C;AAEA,sBAAsB,gCACpB,KACA,YACA,YAIC;AACD,QAAM,wBAAwB,KAAK,KAAK,YAAY,cAAc;AAClE,MAAI;AACJ,MAAI;AACF,UAAM,oBAAoB,IAAI,GAAG,aAAa,qBAAqB;AACnE,wBAAoB,KAAK,MAAM,iBAAiB;AAAA,EAClD,QAAQ;AACN,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,YAAY,qBAAqB;AAAA,qCAClB,UAAU;AAAA,IAC3C,CAAC;AAAA,EACH;AACA,MAAI,kBAAkB,SAAS,MAAM,QAAW;AAC9C,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,IAAI,qBAAqB;AAAA,qCACV,UAAU;AAAA,IAC3C,CAAC;AAAA,EACH;AAEA,QAAM,8BAA8B,oBAAI,IAAY;AACpD,aAAW,OAAO,CAAC,oBAAoB,sBAAsB,GAAG;AAC9D,eAAW,CAAC,aAAa,kBAAkB,KAAK,OAAO;AAAA,MACrD,kBAAkB,GAAG,KAAK,CAAC;AAAA,IAC7B,GAAG;AACD,UAAI,OAAO,uBAAuB,UAAU;AAC1C,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,YAAY,qBAAqB,OAAO,GAAG,IAAI,WAAW,sBAAsB,OAAO,kBAAkB;AAAA,QAC3H,CAAC;AAAA,MACH;AACA,kCAA4B,IAAI,WAAW;AAAA,IAC7C;AAAA,EACF;AAEA,SAAO;AAAA,IACL,SAAS,kBAAkB,SAAS;AAAA,IACpC;AAAA,EACF;AACF;", "names": []}