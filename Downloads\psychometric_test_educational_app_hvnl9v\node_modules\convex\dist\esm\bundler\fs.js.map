{"version": 3, "sources": ["../../../src/bundler/fs.ts"], "sourcesContent": ["// Disable our restriction on `throw` because these aren't developer-facing\n// error messages.\n/* eslint-disable no-restricted-imports */\n/* eslint-disable no-restricted-syntax */\nimport chalk from \"chalk\";\nimport stdFs, { Dirent, Mode, ReadStream, Stats } from \"fs\";\nimport * as fsPromises from \"fs/promises\";\nimport os from \"os\";\nimport path from \"path\";\nimport crypto from \"crypto\";\nimport { Readable } from \"stream\";\n\nexport type NormalizedPath = string;\n\nconst tmpDirOverrideVar = \"CONVEX_TMPDIR\";\nfunction tmpDirPath() {\n  // Allow users to override the temporary directory path with an environment variable.\n  // This override needs to (1) be project-specific, since the user may have projects\n  // on different filesystems, but also (2) be device-specific and not checked in, since\n  // it's dependent on where the user has checked out their project. So, we don't want\n  // this state in the project-specific `convex.json`, which is shared across all\n  // devices, or in the top-level `~/.convex` directory, which is shared across all\n  // projects on the local machine.\n  //\n  // Therefore, just let advanced users configure this behavior with an environment\n  // variable that they're responsible for managing themselves for now.\n  const envTmpDir = process.env[tmpDirOverrideVar];\n  return envTmpDir ?? os.tmpdir();\n}\nconst tmpDirRoot = tmpDirPath();\n\nlet warned = false;\nfunction warnCrossFilesystem(dstPath: string) {\n  const dstDir = path.dirname(dstPath);\n  if (!warned) {\n    // It's hard for these to use `logMessage` without creating a circular dependency, so just log directly.\n    // eslint-disable-next-line no-console\n    console.warn(\n      chalk.yellow(\n        `Temporary directory '${tmpDirRoot}' and project directory '${dstDir}' are on different filesystems.`,\n      ),\n    );\n    // eslint-disable-next-line no-console\n    console.warn(\n      chalk.gray(\n        `  If you're running into errors with other tools watching the project directory, override the temporary directory location with the ${chalk.bold(\n          tmpDirOverrideVar,\n        )} environment variable.`,\n      ),\n    );\n    // eslint-disable-next-line no-console\n    console.warn(\n      chalk.gray(\n        `  Be sure to pick a temporary directory that's on the same filesystem as your project.`,\n      ),\n    );\n    warned = true;\n  }\n}\n\nexport interface Filesystem {\n  listDir(dirPath: string): Dirent[];\n\n  exists(path: string): boolean;\n  stat(path: string): Stats;\n  readUtf8File(path: string): string;\n  // createReadStream returns a stream for which [Symbol.asyncIterator]\n  // yields chunks of size highWaterMark (until the last one), or 64KB if\n  // highWaterMark isn't specified.\n  // https://nodejs.org/api/stream.html#readablesymbolasynciterator\n  createReadStream(\n    path: string,\n    options: { highWaterMark?: number },\n  ): ReadStream;\n  access(path: string): void;\n\n  writeUtf8File(path: string, contents: string, mode?: Mode): void;\n  mkdir(\n    dirPath: string,\n    options?: { allowExisting?: boolean; recursive?: boolean },\n  ): void;\n  rmdir(path: string): void;\n  unlink(path: string): void;\n  swapTmpFile(fromPath: TempPath, toPath: string): void;\n\n  registerPath(path: string, st: Stats | null): void;\n  invalidate(): void;\n}\n\nexport type TempPath = string & { __tempPath: \"tempPath\" };\n\nexport interface TempDir {\n  writeUtf8File(contents: string): TempPath;\n  writeFileStream(\n    path: TempPath,\n    stream: Readable,\n    onData?: (chunk: any) => void,\n  ): Promise<void>;\n  registerTempPath(st: Stats | null): TempPath;\n  path: TempPath;\n}\n\nexport async function withTmpDir(\n  callback: (tmpDir: TempDir) => Promise<void>,\n): Promise<void> {\n  // Create temporary directories inside `tmpDirRoot` of the form `convex-<random>`.\n  const tmpPath = stdFs.mkdtempSync(path.join(tmpDirRoot, \"convex\"));\n  const tmpDir = {\n    writeUtf8File(contents: string): TempPath {\n      const filePath = path.join(tmpPath, crypto.randomUUID());\n      nodeFs.writeUtf8File(filePath, contents);\n      return filePath as TempPath;\n    },\n    registerTempPath(st: Stats | null): TempPath {\n      const filePath = path.join(tmpPath, crypto.randomUUID());\n      nodeFs.registerPath(filePath, st);\n      return filePath as TempPath;\n    },\n    writeFileStream(\n      path: TempPath,\n      stream: Readable,\n      onData?: (chunk: any) => void,\n    ): Promise<void> {\n      return nodeFs.writeFileStream(path, stream, onData);\n    },\n    path: tmpPath as TempPath,\n  };\n  try {\n    await callback(tmpDir);\n  } finally {\n    stdFs.rmSync(tmpPath, { force: true, recursive: true });\n  }\n}\n\n// Use `nodeFs` when you just want to read and write to the local filesystem\n// and don't care about collecting the paths touched. One-off commands\n// should use the singleton `nodeFs`.\nexport class NodeFs implements Filesystem {\n  listDir(dirPath: string) {\n    return stdFs.readdirSync(dirPath, { withFileTypes: true });\n  }\n  exists(path: string) {\n    try {\n      stdFs.statSync(path);\n      return true;\n    } catch (e: any) {\n      if (e.code === \"ENOENT\") {\n        return false;\n      }\n      throw e;\n    }\n  }\n  stat(path: string) {\n    return stdFs.statSync(path);\n  }\n  readUtf8File(path: string) {\n    return stdFs.readFileSync(path, { encoding: \"utf-8\" });\n  }\n  createReadStream(\n    path: string,\n    options: { highWaterMark?: number },\n  ): ReadStream {\n    return stdFs.createReadStream(path, options);\n  }\n  // To avoid issues with filesystem events triggering for our own streamed file\n  // writes, writeFileStream is intentionally not on the Filesystem interface\n  // and not implemented by RecordingFs.\n  async writeFileStream(\n    path: string,\n    stream: Readable,\n    onData?: (chunk: any) => void,\n  ): Promise<void> {\n    // 'wx' means O_CREAT | O_EXCL | O_WRONLY\n    // 0o644 means owner has readwrite access, everyone else has read access.\n    const fileHandle = await fsPromises.open(path, \"wx\", 0o644);\n    try {\n      for await (const chunk of stream) {\n        // For some reason, adding `stream.on(\"data\", onData)` causes issues with\n        // the stream, but calling a callback here works.\n        if (onData) {\n          onData(chunk);\n        }\n        await fileHandle.write(chunk);\n      }\n    } finally {\n      await fileHandle.close();\n    }\n  }\n  access(path: string) {\n    return stdFs.accessSync(path);\n  }\n  writeUtf8File(path: string, contents: string, mode?: Mode) {\n    const fd = stdFs.openSync(path, \"w\", mode);\n    try {\n      stdFs.writeFileSync(fd, contents, { encoding: \"utf-8\" });\n      stdFs.fsyncSync(fd);\n    } finally {\n      stdFs.closeSync(fd);\n    }\n  }\n  mkdir(\n    dirPath: string,\n    options?: { allowExisting?: boolean; recursive?: boolean },\n  ): void {\n    try {\n      stdFs.mkdirSync(dirPath, { recursive: options?.recursive });\n    } catch (e: any) {\n      if (options?.allowExisting && e.code === \"EEXIST\") {\n        return;\n      }\n      throw e;\n    }\n  }\n  rmdir(path: string) {\n    stdFs.rmdirSync(path);\n  }\n  unlink(path: string) {\n    return stdFs.unlinkSync(path);\n  }\n  swapTmpFile(fromPath: TempPath, toPath: string) {\n    try {\n      return stdFs.renameSync(fromPath, toPath);\n    } catch (e: any) {\n      // Fallback to copying the file if we're on different volumes.\n      if (e.code === \"EXDEV\") {\n        warnCrossFilesystem(toPath);\n        stdFs.copyFileSync(fromPath, toPath);\n        return;\n      }\n      throw e;\n    }\n  }\n  registerPath(_path: string, _st: Stats | null) {\n    // The node filesystem doesn't track reads, so we don't need to do anything here.\n  }\n  invalidate() {\n    // We don't track invalidations for the node filesystem either.\n  }\n}\nexport const nodeFs = new NodeFs();\n\n// Filesystem implementation that records all paths observed. This is useful\n// for implementing continuous watch commands that need to manage a filesystem\n// watcher and know when a command's inputs were invalidated.\nexport class RecordingFs implements Filesystem {\n  // Absolute path -> Set of observed child names\n  private observedDirectories: Map<string, Set<string>> = new Map();\n\n  // Absolute path -> observed stat (or null if observed nonexistent)\n  private observedFiles: Map<string, Stats | null> = new Map();\n\n  // Have we noticed that files have changed while recording?\n  private invalidated = false;\n\n  private traceEvents: boolean;\n\n  constructor(traceEvents: boolean) {\n    this.traceEvents = traceEvents;\n  }\n\n  listDir(dirPath: string): Dirent[] {\n    const absDirPath = path.resolve(dirPath);\n\n    // Register observing the directory itself.\n    const dirSt = nodeFs.stat(absDirPath);\n    this.registerNormalized(absDirPath, dirSt);\n\n    // List the directory and register observing all of its children.\n    const entries = nodeFs.listDir(dirPath);\n    for (const entry of entries) {\n      const childPath = path.join(absDirPath, entry.name);\n      const childSt = nodeFs.stat(childPath);\n      this.registerPath(childPath, childSt);\n    }\n\n    // Register observing the directory's children.\n    const observedNames = new Set(entries.map((e) => e.name));\n    const existingNames = this.observedDirectories.get(absDirPath);\n    if (existingNames) {\n      if (!setsEqual(observedNames, existingNames)) {\n        if (this.traceEvents) {\n          // eslint-disable-next-line no-console\n          console.log(\n            \"Invalidating due to directory children mismatch\",\n            observedNames,\n            existingNames,\n          );\n        }\n        this.invalidated = true;\n      }\n    }\n    this.observedDirectories.set(absDirPath, observedNames);\n\n    return entries;\n  }\n\n  exists(path: string): boolean {\n    try {\n      const st = nodeFs.stat(path);\n      this.registerPath(path, st);\n      return true;\n    } catch (err: any) {\n      if (err.code === \"ENOENT\") {\n        this.registerPath(path, null);\n        return false;\n      }\n      throw err;\n    }\n  }\n  stat(path: string): Stats {\n    try {\n      const st = nodeFs.stat(path);\n      this.registerPath(path, st);\n      return st;\n    } catch (err: any) {\n      if (err.code === \"ENOENT\") {\n        this.registerPath(path, null);\n      }\n      throw err;\n    }\n  }\n  readUtf8File(path: string): string {\n    try {\n      const st = nodeFs.stat(path);\n      this.registerPath(path, st);\n      return nodeFs.readUtf8File(path);\n    } catch (err: any) {\n      if (err.code === \"ENOENT\") {\n        this.registerPath(path, null);\n      }\n      throw err;\n    }\n  }\n  createReadStream(\n    path: string,\n    options: { highWaterMark?: number },\n  ): ReadStream {\n    try {\n      const st = nodeFs.stat(path);\n      this.registerPath(path, st);\n      return nodeFs.createReadStream(path, options);\n    } catch (err: any) {\n      if (err.code === \"ENOENT\") {\n        this.registerPath(path, null);\n      }\n      throw err;\n    }\n  }\n  access(path: string) {\n    try {\n      const st = nodeFs.stat(path);\n      this.registerPath(path, st);\n      return nodeFs.access(path);\n    } catch (err: any) {\n      if (err.code === \"ENOENT\") {\n        this.registerPath(path, null);\n      }\n      throw err;\n    }\n  }\n\n  writeUtf8File(filePath: string, contents: string, mode?: Mode) {\n    const absPath = path.resolve(filePath);\n\n    nodeFs.writeUtf8File(filePath, contents, mode);\n\n    this.updateOnWrite(absPath);\n  }\n\n  mkdir(\n    dirPath: string,\n    options?: { allowExisting?: boolean; recursive?: boolean },\n  ): void {\n    const absPath = path.resolve(dirPath);\n    try {\n      stdFs.mkdirSync(absPath, { recursive: options?.recursive });\n    } catch (e: any) {\n      if (options?.allowExisting && e.code === \"EEXIST\") {\n        const st = nodeFs.stat(absPath);\n        this.registerNormalized(absPath, st);\n        return;\n      }\n      throw e;\n    }\n    this.updateOnWrite(absPath);\n  }\n\n  rmdir(dirPath: string) {\n    const absPath = path.resolve(dirPath);\n    stdFs.rmdirSync(absPath);\n    this.updateOnDelete(absPath);\n  }\n  unlink(filePath: string) {\n    const absPath = path.resolve(filePath);\n    stdFs.unlinkSync(absPath);\n    this.updateOnDelete(absPath);\n  }\n  swapTmpFile(fromPath: TempPath, toPath: string) {\n    const absToPath = path.resolve(toPath);\n    nodeFs.swapTmpFile(fromPath, absToPath);\n    this.updateOnWrite(absToPath);\n  }\n\n  private updateOnWrite(absPath: string) {\n    // Stat the file or dir after writing and make it our expected observation. If we read the file after\n    // writing it and it doesn't match this stat (implying a subsequent write), we'll invalidate\n    // the current reader.\n    const newSt = nodeFs.stat(absPath);\n    // Skip invalidation checking since we don't want to conflict if we previously read this file.\n    this.observedFiles.set(absPath, newSt);\n\n    // If we observed the parent, add our newly created file.\n    const parentPath = path.resolve(path.dirname(absPath));\n    const observedParent = this.observedDirectories.get(parentPath);\n    if (observedParent !== undefined) {\n      observedParent.add(path.basename(absPath));\n    }\n  }\n  private updateOnDelete(absPath: string) {\n    // Expect this file to be gone.\n    this.observedFiles.set(absPath, null);\n\n    // Unlink it from our parent if observed.\n    const parentPath = path.resolve(path.dirname(absPath));\n    const observedParent = this.observedDirectories.get(parentPath);\n    if (observedParent !== undefined) {\n      observedParent.delete(path.basename(absPath));\n    }\n  }\n\n  registerPath(p: string, st: Stats | null) {\n    const absPath = path.resolve(p);\n    this.registerNormalized(absPath, st);\n  }\n\n  invalidate() {\n    this.invalidated = true;\n  }\n\n  registerNormalized(absPath: string, observed: Stats | null): void {\n    const existing = this.observedFiles.get(absPath);\n    if (existing !== undefined) {\n      const stMatch = stMatches(observed, existing);\n      if (!stMatch.matches) {\n        if (this.traceEvents) {\n          // eslint-disable-next-line no-console\n          console.log(\n            \"Invalidating due to st mismatch\",\n            absPath,\n            observed,\n            existing,\n            stMatch.reason,\n          );\n        }\n        this.invalidated = true;\n      }\n    }\n    this.observedFiles.set(absPath, observed);\n  }\n\n  finalize(): Observations | \"invalidated\" {\n    if (this.invalidated) {\n      return \"invalidated\";\n    }\n    return new Observations(this.observedDirectories, this.observedFiles);\n  }\n}\n\nexport type WatchEvent = {\n  name: \"add\" | \"addDir\" | \"change\" | \"unlink\" | \"unlinkDir\";\n  absPath: string;\n};\n\nexport class Observations {\n  directories: Map<string, Set<string>>;\n  files: Map<string, Stats | null>;\n\n  constructor(\n    directories: Map<string, Set<string>>,\n    files: Map<string, Stats | null>,\n  ) {\n    this.directories = directories;\n    this.files = files;\n  }\n\n  paths(): string[] {\n    const out = [];\n    for (const path of this.directories.keys()) {\n      out.push(path);\n    }\n    for (const path of this.files.keys()) {\n      out.push(path);\n    }\n    return out;\n  }\n\n  overlaps({\n    absPath,\n  }: WatchEvent): { overlaps: false } | { overlaps: true; reason: string } {\n    let currentSt: null | Stats;\n    try {\n      currentSt = nodeFs.stat(absPath);\n    } catch (e: any) {\n      if (e.code === \"ENOENT\") {\n        currentSt = null;\n      } else {\n        throw e;\n      }\n    }\n\n    // First, check to see if we observed `absPath` as a file.\n    const observedSt = this.files.get(absPath);\n    if (observedSt !== undefined) {\n      const stMatch = stMatches(observedSt, currentSt);\n      if (!stMatch.matches) {\n        const reason = `modified (${stMatch.reason})`;\n        return { overlaps: true, reason };\n      }\n    }\n\n    // Second, check if we listed the directory this file is in.\n    const parentPath = path.resolve(path.dirname(absPath));\n    const observedParent = this.directories.get(parentPath);\n    if (observedParent !== undefined) {\n      const filename = path.basename(absPath);\n\n      // If the file is gone now, but we observed it in its directory, then\n      // it was deleted.\n      if (currentSt === null && observedParent.has(filename)) {\n        return { overlaps: true, reason: \"deleted\" };\n      }\n\n      // If the file exists now, but we didn't see it when listing its directory,\n      // then it was added.\n      if (currentSt !== null && !observedParent.has(filename)) {\n        return { overlaps: true, reason: \"added\" };\n      }\n    }\n\n    return { overlaps: false };\n  }\n}\n\nfunction setsEqual<T>(a: Set<T>, b: Set<T>): boolean {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const elem of a.keys()) {\n    if (!b.has(elem)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function stMatches(\n  a: Stats | null,\n  b: Stats | null,\n): { matches: true } | { matches: false; reason: string } {\n  if (a === null && b === null) {\n    return { matches: true };\n  }\n  if (a !== null && b !== null) {\n    if (a.dev !== b.dev) {\n      return { matches: false, reason: \"device boundary\" };\n    }\n    if (a.isFile() || b.isFile()) {\n      if (!a.isFile() || !b.isFile()) {\n        return { matches: false, reason: \"file type\" };\n      }\n      if (a.ino !== b.ino) {\n        return {\n          matches: false,\n          reason: `file inode (${a.ino} vs. ${b.ino})`,\n        };\n      }\n      if (a.size !== b.size) {\n        return {\n          matches: false,\n          reason: `file size (${a.size} vs. ${b.size})`,\n        };\n      }\n      if (a.mtimeMs !== b.mtimeMs) {\n        return {\n          matches: false,\n          reason: `file mtime (${a.mtimeMs} vs. ${b.mtimeMs})`,\n        };\n      }\n      return { matches: true };\n    }\n    if (a.isDirectory() || b.isDirectory()) {\n      if (!b.isDirectory() || !b.isDirectory()) {\n        return { matches: false, reason: \"dir file type\" };\n      }\n      if (a.ino !== b.ino) {\n        return {\n          matches: false,\n          reason: `dir inode (${a.ino} vs. ${b.ino})`,\n        };\n      }\n      return { matches: true };\n    }\n    // If we have something other than a file or directory, just compare inodes.\n    if (a.ino !== b.ino) {\n      return {\n        matches: false,\n        reason: `special inode (${a.ino} vs. ${b.ino})`,\n      };\n    }\n    return { matches: true };\n  }\n  return { matches: false, reason: \"deleted mismatch\" };\n}\n\n// Sort consistent with unix directory listings.\nexport function consistentPathSort(a: Dirent, b: Dirent) {\n  for (let i = 0; i < Math.min(a.name.length, b.name.length); i++) {\n    if (a.name.charCodeAt(i) !== b.name.charCodeAt(i)) {\n      return a.name.charCodeAt(i) - b.name.charCodeAt(i);\n    }\n  }\n  return a.name.length - b.name.length;\n}\n"], "mappings": ";;;;AAIA,OAAO,WAAW;AAClB,OAAO,WAAgD;AACvD,YAAY,gBAAgB;AAC5B,OAAO,QAAQ;AACf,OAAO,UAAU;AACjB,OAAO,YAAY;AAKnB,MAAM,oBAAoB;AAC1B,SAAS,aAAa;AAWpB,QAAM,YAAY,QAAQ,IAAI,iBAAiB;AAC/C,SAAO,aAAa,GAAG,OAAO;AAChC;AACA,MAAM,aAAa,WAAW;AAE9B,IAAI,SAAS;AACb,SAAS,oBAAoB,SAAiB;AAC5C,QAAM,SAAS,KAAK,QAAQ,OAAO;AACnC,MAAI,CAAC,QAAQ;AAGX,YAAQ;AAAA,MACN,MAAM;AAAA,QACJ,wBAAwB,UAAU,4BAA4B,MAAM;AAAA,MACtE;AAAA,IACF;AAEA,YAAQ;AAAA,MACN,MAAM;AAAA,QACJ,uIAAuI,MAAM;AAAA,UAC3I;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,YAAQ;AAAA,MACN,MAAM;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,aAAS;AAAA,EACX;AACF;AA4CA,sBAAsB,WACpB,UACe;AAEf,QAAM,UAAU,MAAM,YAAY,KAAK,KAAK,YAAY,QAAQ,CAAC;AACjE,QAAM,SAAS;AAAA,IACb,cAAc,UAA4B;AACxC,YAAM,WAAW,KAAK,KAAK,SAAS,OAAO,WAAW,CAAC;AACvD,aAAO,cAAc,UAAU,QAAQ;AACvC,aAAO;AAAA,IACT;AAAA,IACA,iBAAiB,IAA4B;AAC3C,YAAM,WAAW,KAAK,KAAK,SAAS,OAAO,WAAW,CAAC;AACvD,aAAO,aAAa,UAAU,EAAE;AAChC,aAAO;AAAA,IACT;AAAA,IACA,gBACEA,OACA,QACA,QACe;AACf,aAAO,OAAO,gBAAgBA,OAAM,QAAQ,MAAM;AAAA,IACpD;AAAA,IACA,MAAM;AAAA,EACR;AACA,MAAI;AACF,UAAM,SAAS,MAAM;AAAA,EACvB,UAAE;AACA,UAAM,OAAO,SAAS,EAAE,OAAO,MAAM,WAAW,KAAK,CAAC;AAAA,EACxD;AACF;AAKO,aAAM,OAA6B;AAAA,EACxC,QAAQ,SAAiB;AACvB,WAAO,MAAM,YAAY,SAAS,EAAE,eAAe,KAAK,CAAC;AAAA,EAC3D;AAAA,EACA,OAAOA,OAAc;AACnB,QAAI;AACF,YAAM,SAASA,KAAI;AACnB,aAAO;AAAA,IACT,SAAS,GAAQ;AACf,UAAI,EAAE,SAAS,UAAU;AACvB,eAAO;AAAA,MACT;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,KAAKA,OAAc;AACjB,WAAO,MAAM,SAASA,KAAI;AAAA,EAC5B;AAAA,EACA,aAAaA,OAAc;AACzB,WAAO,MAAM,aAAaA,OAAM,EAAE,UAAU,QAAQ,CAAC;AAAA,EACvD;AAAA,EACA,iBACEA,OACA,SACY;AACZ,WAAO,MAAM,iBAAiBA,OAAM,OAAO;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,gBACJA,OACA,QACA,QACe;AAGf,UAAM,aAAa,MAAM,WAAW,KAAKA,OAAM,MAAM,GAAK;AAC1D,QAAI;AACF,uBAAiB,SAAS,QAAQ;AAGhC,YAAI,QAAQ;AACV,iBAAO,KAAK;AAAA,QACd;AACA,cAAM,WAAW,MAAM,KAAK;AAAA,MAC9B;AAAA,IACF,UAAE;AACA,YAAM,WAAW,MAAM;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAOA,OAAc;AACnB,WAAO,MAAM,WAAWA,KAAI;AAAA,EAC9B;AAAA,EACA,cAAcA,OAAc,UAAkB,MAAa;AACzD,UAAM,KAAK,MAAM,SAASA,OAAM,KAAK,IAAI;AACzC,QAAI;AACF,YAAM,cAAc,IAAI,UAAU,EAAE,UAAU,QAAQ,CAAC;AACvD,YAAM,UAAU,EAAE;AAAA,IACpB,UAAE;AACA,YAAM,UAAU,EAAE;AAAA,IACpB;AAAA,EACF;AAAA,EACA,MACE,SACA,SACM;AACN,QAAI;AACF,YAAM,UAAU,SAAS,EAAE,WAAW,SAAS,UAAU,CAAC;AAAA,IAC5D,SAAS,GAAQ;AACf,UAAI,SAAS,iBAAiB,EAAE,SAAS,UAAU;AACjD;AAAA,MACF;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,MAAMA,OAAc;AAClB,UAAM,UAAUA,KAAI;AAAA,EACtB;AAAA,EACA,OAAOA,OAAc;AACnB,WAAO,MAAM,WAAWA,KAAI;AAAA,EAC9B;AAAA,EACA,YAAY,UAAoB,QAAgB;AAC9C,QAAI;AACF,aAAO,MAAM,WAAW,UAAU,MAAM;AAAA,IAC1C,SAAS,GAAQ;AAEf,UAAI,EAAE,SAAS,SAAS;AACtB,4BAAoB,MAAM;AAC1B,cAAM,aAAa,UAAU,MAAM;AACnC;AAAA,MACF;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,aAAa,OAAe,KAAmB;AAAA,EAE/C;AAAA,EACA,aAAa;AAAA,EAEb;AACF;AACO,aAAM,SAAS,IAAI,OAAO;AAK1B,aAAM,YAAkC;AAAA,EAY7C,YAAY,aAAsB;AAVlC;AAAA,wBAAQ,uBAAgD,oBAAI,IAAI;AAGhE;AAAA,wBAAQ,iBAA2C,oBAAI,IAAI;AAG3D;AAAA,wBAAQ,eAAc;AAEtB,wBAAQ;AAGN,SAAK,cAAc;AAAA,EACrB;AAAA,EAEA,QAAQ,SAA2B;AACjC,UAAM,aAAa,KAAK,QAAQ,OAAO;AAGvC,UAAM,QAAQ,OAAO,KAAK,UAAU;AACpC,SAAK,mBAAmB,YAAY,KAAK;AAGzC,UAAM,UAAU,OAAO,QAAQ,OAAO;AACtC,eAAW,SAAS,SAAS;AAC3B,YAAM,YAAY,KAAK,KAAK,YAAY,MAAM,IAAI;AAClD,YAAM,UAAU,OAAO,KAAK,SAAS;AACrC,WAAK,aAAa,WAAW,OAAO;AAAA,IACtC;AAGA,UAAM,gBAAgB,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC;AACxD,UAAM,gBAAgB,KAAK,oBAAoB,IAAI,UAAU;AAC7D,QAAI,eAAe;AACjB,UAAI,CAAC,UAAU,eAAe,aAAa,GAAG;AAC5C,YAAI,KAAK,aAAa;AAEpB,kBAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AACA,SAAK,oBAAoB,IAAI,YAAY,aAAa;AAEtD,WAAO;AAAA,EACT;AAAA,EAEA,OAAOA,OAAuB;AAC5B,QAAI;AACF,YAAM,KAAK,OAAO,KAAKA,KAAI;AAC3B,WAAK,aAAaA,OAAM,EAAE;AAC1B,aAAO;AAAA,IACT,SAAS,KAAU;AACjB,UAAI,IAAI,SAAS,UAAU;AACzB,aAAK,aAAaA,OAAM,IAAI;AAC5B,eAAO;AAAA,MACT;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,KAAKA,OAAqB;AACxB,QAAI;AACF,YAAM,KAAK,OAAO,KAAKA,KAAI;AAC3B,WAAK,aAAaA,OAAM,EAAE;AAC1B,aAAO;AAAA,IACT,SAAS,KAAU;AACjB,UAAI,IAAI,SAAS,UAAU;AACzB,aAAK,aAAaA,OAAM,IAAI;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,aAAaA,OAAsB;AACjC,QAAI;AACF,YAAM,KAAK,OAAO,KAAKA,KAAI;AAC3B,WAAK,aAAaA,OAAM,EAAE;AAC1B,aAAO,OAAO,aAAaA,KAAI;AAAA,IACjC,SAAS,KAAU;AACjB,UAAI,IAAI,SAAS,UAAU;AACzB,aAAK,aAAaA,OAAM,IAAI;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,iBACEA,OACA,SACY;AACZ,QAAI;AACF,YAAM,KAAK,OAAO,KAAKA,KAAI;AAC3B,WAAK,aAAaA,OAAM,EAAE;AAC1B,aAAO,OAAO,iBAAiBA,OAAM,OAAO;AAAA,IAC9C,SAAS,KAAU;AACjB,UAAI,IAAI,SAAS,UAAU;AACzB,aAAK,aAAaA,OAAM,IAAI;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,OAAOA,OAAc;AACnB,QAAI;AACF,YAAM,KAAK,OAAO,KAAKA,KAAI;AAC3B,WAAK,aAAaA,OAAM,EAAE;AAC1B,aAAO,OAAO,OAAOA,KAAI;AAAA,IAC3B,SAAS,KAAU;AACjB,UAAI,IAAI,SAAS,UAAU;AACzB,aAAK,aAAaA,OAAM,IAAI;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,cAAc,UAAkB,UAAkB,MAAa;AAC7D,UAAM,UAAU,KAAK,QAAQ,QAAQ;AAErC,WAAO,cAAc,UAAU,UAAU,IAAI;AAE7C,SAAK,cAAc,OAAO;AAAA,EAC5B;AAAA,EAEA,MACE,SACA,SACM;AACN,UAAM,UAAU,KAAK,QAAQ,OAAO;AACpC,QAAI;AACF,YAAM,UAAU,SAAS,EAAE,WAAW,SAAS,UAAU,CAAC;AAAA,IAC5D,SAAS,GAAQ;AACf,UAAI,SAAS,iBAAiB,EAAE,SAAS,UAAU;AACjD,cAAM,KAAK,OAAO,KAAK,OAAO;AAC9B,aAAK,mBAAmB,SAAS,EAAE;AACnC;AAAA,MACF;AACA,YAAM;AAAA,IACR;AACA,SAAK,cAAc,OAAO;AAAA,EAC5B;AAAA,EAEA,MAAM,SAAiB;AACrB,UAAM,UAAU,KAAK,QAAQ,OAAO;AACpC,UAAM,UAAU,OAAO;AACvB,SAAK,eAAe,OAAO;AAAA,EAC7B;AAAA,EACA,OAAO,UAAkB;AACvB,UAAM,UAAU,KAAK,QAAQ,QAAQ;AACrC,UAAM,WAAW,OAAO;AACxB,SAAK,eAAe,OAAO;AAAA,EAC7B;AAAA,EACA,YAAY,UAAoB,QAAgB;AAC9C,UAAM,YAAY,KAAK,QAAQ,MAAM;AACrC,WAAO,YAAY,UAAU,SAAS;AACtC,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EAEQ,cAAc,SAAiB;AAIrC,UAAM,QAAQ,OAAO,KAAK,OAAO;AAEjC,SAAK,cAAc,IAAI,SAAS,KAAK;AAGrC,UAAM,aAAa,KAAK,QAAQ,KAAK,QAAQ,OAAO,CAAC;AACrD,UAAM,iBAAiB,KAAK,oBAAoB,IAAI,UAAU;AAC9D,QAAI,mBAAmB,QAAW;AAChC,qBAAe,IAAI,KAAK,SAAS,OAAO,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA,EACQ,eAAe,SAAiB;AAEtC,SAAK,cAAc,IAAI,SAAS,IAAI;AAGpC,UAAM,aAAa,KAAK,QAAQ,KAAK,QAAQ,OAAO,CAAC;AACrD,UAAM,iBAAiB,KAAK,oBAAoB,IAAI,UAAU;AAC9D,QAAI,mBAAmB,QAAW;AAChC,qBAAe,OAAO,KAAK,SAAS,OAAO,CAAC;AAAA,IAC9C;AAAA,EACF;AAAA,EAEA,aAAa,GAAW,IAAkB;AACxC,UAAM,UAAU,KAAK,QAAQ,CAAC;AAC9B,SAAK,mBAAmB,SAAS,EAAE;AAAA,EACrC;AAAA,EAEA,aAAa;AACX,SAAK,cAAc;AAAA,EACrB;AAAA,EAEA,mBAAmB,SAAiB,UAA8B;AAChE,UAAM,WAAW,KAAK,cAAc,IAAI,OAAO;AAC/C,QAAI,aAAa,QAAW;AAC1B,YAAM,UAAU,UAAU,UAAU,QAAQ;AAC5C,UAAI,CAAC,QAAQ,SAAS;AACpB,YAAI,KAAK,aAAa;AAEpB,kBAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AACA,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AACA,SAAK,cAAc,IAAI,SAAS,QAAQ;AAAA,EAC1C;AAAA,EAEA,WAAyC;AACvC,QAAI,KAAK,aAAa;AACpB,aAAO;AAAA,IACT;AACA,WAAO,IAAI,aAAa,KAAK,qBAAqB,KAAK,aAAa;AAAA,EACtE;AACF;AAOO,aAAM,aAAa;AAAA,EAIxB,YACE,aACA,OACA;AANF;AACA;AAME,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,QAAkB;AAChB,UAAM,MAAM,CAAC;AACb,eAAWA,SAAQ,KAAK,YAAY,KAAK,GAAG;AAC1C,UAAI,KAAKA,KAAI;AAAA,IACf;AACA,eAAWA,SAAQ,KAAK,MAAM,KAAK,GAAG;AACpC,UAAI,KAAKA,KAAI;AAAA,IACf;AACA,WAAO;AAAA,EACT;AAAA,EAEA,SAAS;AAAA,IACP;AAAA,EACF,GAAyE;AACvE,QAAI;AACJ,QAAI;AACF,kBAAY,OAAO,KAAK,OAAO;AAAA,IACjC,SAAS,GAAQ;AACf,UAAI,EAAE,SAAS,UAAU;AACvB,oBAAY;AAAA,MACd,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AAGA,UAAM,aAAa,KAAK,MAAM,IAAI,OAAO;AACzC,QAAI,eAAe,QAAW;AAC5B,YAAM,UAAU,UAAU,YAAY,SAAS;AAC/C,UAAI,CAAC,QAAQ,SAAS;AACpB,cAAM,SAAS,aAAa,QAAQ,MAAM;AAC1C,eAAO,EAAE,UAAU,MAAM,OAAO;AAAA,MAClC;AAAA,IACF;AAGA,UAAM,aAAa,KAAK,QAAQ,KAAK,QAAQ,OAAO,CAAC;AACrD,UAAM,iBAAiB,KAAK,YAAY,IAAI,UAAU;AACtD,QAAI,mBAAmB,QAAW;AAChC,YAAM,WAAW,KAAK,SAAS,OAAO;AAItC,UAAI,cAAc,QAAQ,eAAe,IAAI,QAAQ,GAAG;AACtD,eAAO,EAAE,UAAU,MAAM,QAAQ,UAAU;AAAA,MAC7C;AAIA,UAAI,cAAc,QAAQ,CAAC,eAAe,IAAI,QAAQ,GAAG;AACvD,eAAO,EAAE,UAAU,MAAM,QAAQ,QAAQ;AAAA,MAC3C;AAAA,IACF;AAEA,WAAO,EAAE,UAAU,MAAM;AAAA,EAC3B;AACF;AAEA,SAAS,UAAa,GAAW,GAAoB;AACnD,MAAI,EAAE,SAAS,EAAE,MAAM;AACrB,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,EAAE,KAAK,GAAG;AAC3B,QAAI,CAAC,EAAE,IAAI,IAAI,GAAG;AAChB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEO,gBAAS,UACd,GACA,GACwD;AACxD,MAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,WAAO,EAAE,SAAS,KAAK;AAAA,EACzB;AACA,MAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,QAAI,EAAE,QAAQ,EAAE,KAAK;AACnB,aAAO,EAAE,SAAS,OAAO,QAAQ,kBAAkB;AAAA,IACrD;AACA,QAAI,EAAE,OAAO,KAAK,EAAE,OAAO,GAAG;AAC5B,UAAI,CAAC,EAAE,OAAO,KAAK,CAAC,EAAE,OAAO,GAAG;AAC9B,eAAO,EAAE,SAAS,OAAO,QAAQ,YAAY;AAAA,MAC/C;AACA,UAAI,EAAE,QAAQ,EAAE,KAAK;AACnB,eAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ,eAAe,EAAE,GAAG,QAAQ,EAAE,GAAG;AAAA,QAC3C;AAAA,MACF;AACA,UAAI,EAAE,SAAS,EAAE,MAAM;AACrB,eAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ,cAAc,EAAE,IAAI,QAAQ,EAAE,IAAI;AAAA,QAC5C;AAAA,MACF;AACA,UAAI,EAAE,YAAY,EAAE,SAAS;AAC3B,eAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ,eAAe,EAAE,OAAO,QAAQ,EAAE,OAAO;AAAA,QACnD;AAAA,MACF;AACA,aAAO,EAAE,SAAS,KAAK;AAAA,IACzB;AACA,QAAI,EAAE,YAAY,KAAK,EAAE,YAAY,GAAG;AACtC,UAAI,CAAC,EAAE,YAAY,KAAK,CAAC,EAAE,YAAY,GAAG;AACxC,eAAO,EAAE,SAAS,OAAO,QAAQ,gBAAgB;AAAA,MACnD;AACA,UAAI,EAAE,QAAQ,EAAE,KAAK;AACnB,eAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ,cAAc,EAAE,GAAG,QAAQ,EAAE,GAAG;AAAA,QAC1C;AAAA,MACF;AACA,aAAO,EAAE,SAAS,KAAK;AAAA,IACzB;AAEA,QAAI,EAAE,QAAQ,EAAE,KAAK;AACnB,aAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ,kBAAkB,EAAE,GAAG,QAAQ,EAAE,GAAG;AAAA,MAC9C;AAAA,IACF;AACA,WAAO,EAAE,SAAS,KAAK;AAAA,EACzB;AACA,SAAO,EAAE,SAAS,OAAO,QAAQ,mBAAmB;AACtD;AAGO,gBAAS,mBAAmB,GAAW,GAAW;AACvD,WAAS,IAAI,GAAG,IAAI,KAAK,IAAI,EAAE,KAAK,QAAQ,EAAE,KAAK,MAAM,GAAG,KAAK;AAC/D,QAAI,EAAE,KAAK,WAAW,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,GAAG;AACjD,aAAO,EAAE,KAAK,WAAW,CAAC,IAAI,EAAE,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACF;AACA,SAAO,EAAE,KAAK,SAAS,EAAE,KAAK;AAChC;", "names": ["path"]}