{"version": 3, "sources": ["../../../src/bundler/wasm.ts"], "sourcesContent": ["import { PluginBuild } from \"esbuild\";\nimport path from \"path\";\n// TODO wasm contents aren't watched\n// eslint-disable-next-line no-restricted-imports\nimport fs from \"fs\";\n\nexport const wasmPlugin = {\n  name: \"convex-wasm\",\n  setup(build: PluginBuild) {\n    // Resolve \".wasm\" files to a path with a namespace\n    build.onResolve({ filter: /\\.wasm$/ }, (args) => {\n      // If this is the import inside the stub module, import the\n      // binary itself. Put the path in the \"wasm-binary\" namespace\n      // to tell our binary load callback to load the binary file.\n      if (args.namespace === \"wasm-stub\") {\n        return {\n          path: args.path,\n          namespace: \"wasm-binary\",\n        };\n      }\n\n      // Otherwise, generate the JavaScript stub module for this\n      // \".wasm\" file. Put it in the \"wasm-stub\" namespace to tell\n      // our stub load callback to fill it with JavaScript.\n      //\n      // Resolve relative paths to absolute paths here since this\n      // resolve callback is given \"resolveDir\", the directory to\n      // resolve imports against.\n      if (args.resolveDir === \"\") {\n        return; // Ignore unresolvable paths\n      }\n      return {\n        path: path.isAbsolute(args.path)\n          ? args.path\n          : path.join(args.resolveDir, args.path),\n        namespace: \"wasm-stub\",\n      };\n    });\n\n    // Virtual modules in the \"wasm-stub\" namespace are filled with\n    // the JavaScript code for compiling the WebAssembly binary. The\n    // binary itself is imported from a second virtual module.\n    build.onLoad({ filter: /.*/, namespace: \"wasm-stub\" }, async (args) => ({\n      contents: `import wasm from ${JSON.stringify(args.path)}\n          export default new WebAssembly.Module(wasm)`,\n    }));\n\n    // Virtual modules in the \"wasm-binary\" namespace contain the\n    // actual bytes of the WebAssembly file. This uses esbuild's\n    // built-in \"binary\" loader instead of manually embedding the\n    // binary data inside JavaScript code ourselves.\n    build.onLoad({ filter: /.*/, namespace: \"wasm-binary\" }, async (args) => ({\n      contents: await fs.promises.readFile(args.path),\n      loader: \"binary\",\n    }));\n  },\n};\n"], "mappings": ";AACA,OAAO,UAAU;AAGjB,OAAO,QAAQ;AAER,aAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EACN,MAAM,OAAoB;AAExB,UAAM,UAAU,EAAE,QAAQ,UAAU,GAAG,CAAC,SAAS;AAI/C,UAAI,KAAK,cAAc,aAAa;AAClC,eAAO;AAAA,UACL,MAAM,KAAK;AAAA,UACX,WAAW;AAAA,QACb;AAAA,MACF;AASA,UAAI,KAAK,eAAe,IAAI;AAC1B;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM,KAAK,WAAW,KAAK,IAAI,IAC3B,KAAK,OACL,KAAK,KAAK,KAAK,YAAY,KAAK,IAAI;AAAA,QACxC,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAKD,UAAM,OAAO,EAAE,QAAQ,MAAM,WAAW,YAAY,GAAG,OAAO,UAAU;AAAA,MACtE,UAAU,oBAAoB,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA;AAAA,IAEzD,EAAE;AAMF,UAAM,OAAO,EAAE,QAAQ,MAAM,WAAW,cAAc,GAAG,OAAO,UAAU;AAAA,MACxE,UAAU,MAAM,GAAG,SAAS,SAAS,KAAK,IAAI;AAAA,MAC9C,QAAQ;AAAA,IACV,EAAE;AAAA,EACJ;AACF;", "names": []}