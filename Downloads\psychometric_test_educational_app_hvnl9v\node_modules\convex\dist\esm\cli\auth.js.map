{"version": 3, "sources": ["../../../src/cli/auth.ts"], "sourcesContent": ["import { Command, Option } from \"@commander-js/extra-typings\";\nimport { oneoffContext } from \"../bundler/context.js\";\n\nconst list = new Command(\"list\").action(async () => {\n  const ctx = await oneoffContext({\n    url: undefined,\n    adminKey: undefined,\n    envFile: undefined,\n  });\n  await ctx.crash({\n    exitCode: 1,\n    errorType: \"fatal\",\n    errForSentry: \"Ran deprecated `convex auth list`\",\n    printedMessage:\n      \"convex auth commands were removed, see https://docs.convex.dev/auth for up to date instructions.\",\n  });\n});\n\nconst rm = new Command(\"remove\").action(async () => {\n  const ctx = await oneoffContext({\n    url: undefined,\n    adminKey: undefined,\n    envFile: undefined,\n  });\n  await ctx.crash({\n    exitCode: 1,\n    errorType: \"fatal\",\n    errForSentry: \"Ran deprecated `convex auth remove`\",\n    printedMessage:\n      \"convex auth commands were removed, see https://docs.convex.dev/auth for up to date instructions.\",\n  });\n});\n\nconst add = new Command(\"add\")\n  .addOption(new Option(\"--identity-provider-url <url>\").hideHelp())\n  .addOption(new Option(\"--application-id <applicationId>\").hideHelp())\n  .action(async () => {\n    const ctx = await oneoffContext({\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n    await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      errForSentry: \"Ran deprecated `convex auth add`\",\n      printedMessage:\n        \"convex auth commands were removed, see https://docs.convex.dev/auth for up to date instructions.\",\n    });\n  });\n\nexport const auth = new Command(\"auth\")\n  .addCommand(list)\n  .addCommand(rm)\n  .addCommand(add);\n"], "mappings": ";AAAA,SAAS,SAAS,cAAc;AAChC,SAAS,qBAAqB;AAE9B,MAAM,OAAO,IAAI,QAAQ,MAAM,EAAE,OAAO,YAAY;AAClD,QAAM,MAAM,MAAM,cAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,QAAM,IAAI,MAAM;AAAA,IACd,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,gBACE;AAAA,EACJ,CAAC;AACH,CAAC;AAED,MAAM,KAAK,IAAI,QAAQ,QAAQ,EAAE,OAAO,YAAY;AAClD,QAAM,MAAM,MAAM,cAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,QAAM,IAAI,MAAM;AAAA,IACd,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,gBACE;AAAA,EACJ,CAAC;AACH,CAAC;AAED,MAAM,MAAM,IAAI,QAAQ,KAAK,EAC1B,UAAU,IAAI,OAAO,+BAA+B,EAAE,SAAS,CAAC,EAChE,UAAU,IAAI,OAAO,kCAAkC,EAAE,SAAS,CAAC,EACnE,OAAO,YAAY;AAClB,QAAM,MAAM,MAAM,cAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,QAAM,IAAI,MAAM;AAAA,IACd,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,gBACE;AAAA,EACJ,CAAC;AACH,CAAC;AAEI,aAAM,OAAO,IAAI,QAAQ,MAAM,EACnC,WAAW,IAAI,EACf,WAAW,EAAE,EACb,WAAW,GAAG;", "names": []}