{"version": 3, "sources": ["../../../../src/cli/codegen_templates/api_cjs.ts"], "sourcesContent": ["import { apiCodegen as esmApiCodegen } from \"./api.js\";\nimport { header } from \"./common.js\";\n\nexport function apiCjsCodegen(modulePaths: string[]) {\n  const { DTS } = esmApiCodegen(modulePaths);\n  const apiJS = `${header(\"Generated `api` utility.\")}\n  const { anyApi } = require(\"convex/server\");\n  module.exports = {\n    api: anyApi,\n    internal: anyApi,\n  };\n  `;\n  return {\n    DTS,\n    JS: apiJS,\n  };\n}\n"], "mappings": ";AAAA,SAAS,cAAc,qBAAqB;AAC5C,SAAS,cAAc;AAEhB,gBAAS,cAAc,aAAuB;AACnD,QAAM,EAAE,IAAI,IAAI,cAAc,WAAW;AACzC,QAAM,QAAQ,GAAG,OAAO,0BAA0B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOnD,SAAO;AAAA,IACL;AAAA,IACA,IAAI;AAAA,EACN;AACF;", "names": []}