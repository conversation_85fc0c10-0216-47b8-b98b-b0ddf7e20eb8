{"version": 3, "sources": ["../../../../src/cli/codegen_templates/validator_helpers.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { jsonToConvex, Value } from \"../../values/index.js\";\nimport {\n  ConvexValidator,\n  convexValidator,\n} from \"../lib/deployApi/validator.js\";\n\nexport function parseValidator(\n  validator: string | null,\n): ConvexValidator | null {\n  if (!validator) {\n    return null;\n  }\n  return z.nullable(convexValidator).parse(JSON.parse(validator));\n}\n\nexport function validatorToType(\n  validator: ConvexValidator,\n  useIdType: boolean,\n): string {\n  if (validator.type === \"null\") {\n    return \"null\";\n  } else if (validator.type === \"number\") {\n    return \"number\";\n  } else if (validator.type === \"bigint\") {\n    return \"bigint\";\n  } else if (validator.type === \"boolean\") {\n    return \"boolean\";\n  } else if (validator.type === \"string\") {\n    return \"string\";\n  } else if (validator.type === \"bytes\") {\n    return \"ArrayBuffer\";\n  } else if (validator.type === \"any\") {\n    return \"any\";\n  } else if (validator.type === \"literal\") {\n    const convexValue = jsonToConvex(validator.value);\n    return convexValueToLiteral(convexValue);\n  } else if (validator.type === \"id\") {\n    return useIdType ? `Id<\"${validator.tableName}\">` : \"string\";\n  } else if (validator.type === \"array\") {\n    return `Array<${validatorToType(validator.value, useIdType)}>`;\n  } else if (validator.type === \"record\") {\n    return `Record<${validatorToType(validator.keys, useIdType)}, ${validatorToType(validator.values.fieldType, useIdType)}>`;\n  } else if (validator.type === \"union\") {\n    return validator.value\n      .map((v) => validatorToType(v, useIdType))\n      .join(\" | \");\n  } else if (validator.type === \"object\") {\n    return objectValidatorToType(validator.value, useIdType);\n  } else {\n    // eslint-disable-next-line no-restricted-syntax\n    throw new Error(`Unsupported validator type`);\n  }\n}\n\nfunction objectValidatorToType(\n  fields: Record<string, { fieldType: ConvexValidator; optional: boolean }>,\n  useIdType: boolean,\n): string {\n  const fieldStrings: string[] = [];\n  for (const [fieldName, field] of Object.entries(fields)) {\n    const fieldType = validatorToType(field.fieldType, useIdType);\n    fieldStrings.push(`${fieldName}${field.optional ? \"?\" : \"\"}: ${fieldType}`);\n  }\n  return `{ ${fieldStrings.join(\", \")} }`;\n}\n\nfunction convexValueToLiteral(value: Value): string {\n  if (value === null) {\n    return \"null\";\n  }\n  if (typeof value === \"bigint\") {\n    return `${value}n`;\n  }\n  if (typeof value === \"number\") {\n    return `${value}`;\n  }\n  if (typeof value === \"boolean\") {\n    return `${value}`;\n  }\n  if (typeof value === \"string\") {\n    return `\"${value}\"`;\n  }\n  // eslint-disable-next-line no-restricted-syntax\n  throw new Error(`Unsupported literal type`);\n}\n"], "mappings": ";AAAA,SAAS,SAAS;AAClB,SAAS,oBAA2B;AACpC;AAAA,EAEE;AAAA,OACK;AAEA,gBAAS,eACd,WACwB;AACxB,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,SAAO,EAAE,SAAS,eAAe,EAAE,MAAM,KAAK,MAAM,SAAS,CAAC;AAChE;AAEO,gBAAS,gBACd,WACA,WACQ;AACR,MAAI,UAAU,SAAS,QAAQ;AAC7B,WAAO;AAAA,EACT,WAAW,UAAU,SAAS,UAAU;AACtC,WAAO;AAAA,EACT,WAAW,UAAU,SAAS,UAAU;AACtC,WAAO;AAAA,EACT,WAAW,UAAU,SAAS,WAAW;AACvC,WAAO;AAAA,EACT,WAAW,UAAU,SAAS,UAAU;AACtC,WAAO;AAAA,EACT,WAAW,UAAU,SAAS,SAAS;AACrC,WAAO;AAAA,EACT,WAAW,UAAU,SAAS,OAAO;AACnC,WAAO;AAAA,EACT,WAAW,UAAU,SAAS,WAAW;AACvC,UAAM,cAAc,aAAa,UAAU,KAAK;AAChD,WAAO,qBAAqB,WAAW;AAAA,EACzC,WAAW,UAAU,SAAS,MAAM;AAClC,WAAO,YAAY,OAAO,UAAU,SAAS,OAAO;AAAA,EACtD,WAAW,UAAU,SAAS,SAAS;AACrC,WAAO,SAAS,gBAAgB,UAAU,OAAO,SAAS,CAAC;AAAA,EAC7D,WAAW,UAAU,SAAS,UAAU;AACtC,WAAO,UAAU,gBAAgB,UAAU,MAAM,SAAS,CAAC,KAAK,gBAAgB,UAAU,OAAO,WAAW,SAAS,CAAC;AAAA,EACxH,WAAW,UAAU,SAAS,SAAS;AACrC,WAAO,UAAU,MACd,IAAI,CAAC,MAAM,gBAAgB,GAAG,SAAS,CAAC,EACxC,KAAK,KAAK;AAAA,EACf,WAAW,UAAU,SAAS,UAAU;AACtC,WAAO,sBAAsB,UAAU,OAAO,SAAS;AAAA,EACzD,OAAO;AAEL,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AACF;AAEA,SAAS,sBACP,QACA,WACQ;AACR,QAAM,eAAyB,CAAC;AAChC,aAAW,CAAC,WAAW,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACvD,UAAM,YAAY,gBAAgB,MAAM,WAAW,SAAS;AAC5D,iBAAa,KAAK,GAAG,SAAS,GAAG,MAAM,WAAW,MAAM,EAAE,KAAK,SAAS,EAAE;AAAA,EAC5E;AACA,SAAO,KAAK,aAAa,KAAK,IAAI,CAAC;AACrC;AAEA,SAAS,qBAAqB,OAAsB;AAClD,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,GAAG,KAAK;AAAA,EACjB;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,GAAG,KAAK;AAAA,EACjB;AACA,MAAI,OAAO,UAAU,WAAW;AAC9B,WAAO,GAAG,KAAK;AAAA,EACjB;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,IAAI,KAAK;AAAA,EAClB;AAEA,QAAM,IAAI,MAAM,0BAA0B;AAC5C;", "names": []}