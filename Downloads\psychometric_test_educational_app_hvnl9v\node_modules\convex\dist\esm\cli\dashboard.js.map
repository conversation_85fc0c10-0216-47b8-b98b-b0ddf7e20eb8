{"version": 3, "sources": ["../../../src/cli/dashboard.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport chalk from \"chalk\";\nimport open from \"open\";\nimport {\n  Context,\n  logMessage,\n  logOutput,\n  logWarning,\n  oneoffContext,\n} from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\nimport { checkIfDashboardIsRunning } from \"./lib/localDeployment/dashboard.js\";\nimport { getDashboardUrl } from \"./lib/dashboard.js\";\nimport { isAnonymousDeployment } from \"./lib/deployment.js\";\n\nexport const DASHBOARD_HOST = process.env.CONVEX_PROVISION_HOST\n  ? \"http://localhost:6789\"\n  : \"https://dashboard.convex.dev\";\n\nexport const dashboard = new Command(\"dashboard\")\n  .alias(\"dash\")\n  .description(\"Open the dashboard in the browser\")\n  .allowExcessArguments(false)\n  .option(\n    \"--no-open\",\n    \"Don't automatically open the dashboard in the default browser\",\n  )\n  .addDeploymentSelectionOptions(actionDescription(\"Open the dashboard for\"))\n  .showHelpAfterError()\n  .action(async (options) => {\n    const ctx = await oneoffContext(options);\n\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, options);\n    const deploymentSelection = await getDeploymentSelection(ctx, options);\n    const deployment = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      selectionWithinProject,\n      { ensureLocalRunning: false },\n    );\n\n    if (deployment.deploymentFields === null) {\n      const msg = `Self-hosted deployment configured.\\n\\`${chalk.bold(\"npx convex dashboard\")}\\` is not supported for self-hosted deployments.\\nSee self-hosting instructions for how to self-host the dashboard.`;\n      logMessage(ctx, chalk.yellow(msg));\n      return;\n    }\n    const dashboardUrl = getDashboardUrl(ctx, deployment.deploymentFields);\n    if (isAnonymousDeployment(deployment.deploymentFields.deploymentName)) {\n      const warningMessage = `You are not currently running the dashboard locally. Make sure \\`npx convex dev\\` is running and try again.`;\n      if (dashboardUrl === null) {\n        logWarning(ctx, warningMessage);\n        return;\n      }\n      const isLocalDashboardRunning = await checkIfDashboardIsRunning(ctx);\n      if (!isLocalDashboardRunning) {\n        logWarning(ctx, warningMessage);\n        return;\n      }\n      await logOrOpenUrl(ctx, dashboardUrl, options.open);\n      return;\n    }\n\n    await logOrOpenUrl(ctx, dashboardUrl ?? DASHBOARD_HOST, options.open);\n  });\n\nasync function logOrOpenUrl(ctx: Context, url: string, shouldOpen: boolean) {\n  if (shouldOpen) {\n    logMessage(ctx, chalk.gray(`Opening ${url} in the default browser...`));\n    await open(url);\n  } else {\n    logOutput(ctx, url);\n  }\n}\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,OAAO,WAAW;AAClB,OAAO,UAAU;AACjB;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,yBAAyB;AAClC,SAAS,8BAA8B;AACvC,SAAS,iCAAiC;AAC1C,SAAS,uBAAuB;AAChC,SAAS,6BAA6B;AAE/B,aAAM,iBAAiB,QAAQ,IAAI,wBACtC,0BACA;AAEG,aAAM,YAAY,IAAI,QAAQ,WAAW,EAC7C,MAAM,MAAM,EACZ,YAAY,mCAAmC,EAC/C,qBAAqB,KAAK,EAC1B;AAAA,EACC;AAAA,EACA;AACF,EACC,8BAA8B,kBAAkB,wBAAwB,CAAC,EACzE,mBAAmB,EACnB,OAAO,OAAO,YAAY;AACzB,QAAM,MAAM,MAAM,cAAc,OAAO;AAEvC,QAAM,yBACJ,MAAM,4CAA4C,KAAK,OAAO;AAChE,QAAM,sBAAsB,MAAM,uBAAuB,KAAK,OAAO;AACrE,QAAM,aAAa,MAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA,EAAE,oBAAoB,MAAM;AAAA,EAC9B;AAEA,MAAI,WAAW,qBAAqB,MAAM;AACxC,UAAM,MAAM;AAAA,IAAyC,MAAM,KAAK,sBAAsB,CAAC;AAAA;AACvF,eAAW,KAAK,MAAM,OAAO,GAAG,CAAC;AACjC;AAAA,EACF;AACA,QAAM,eAAe,gBAAgB,KAAK,WAAW,gBAAgB;AACrE,MAAI,sBAAsB,WAAW,iBAAiB,cAAc,GAAG;AACrE,UAAM,iBAAiB;AACvB,QAAI,iBAAiB,MAAM;AACzB,iBAAW,KAAK,cAAc;AAC9B;AAAA,IACF;AACA,UAAM,0BAA0B,MAAM,0BAA0B,GAAG;AACnE,QAAI,CAAC,yBAAyB;AAC5B,iBAAW,KAAK,cAAc;AAC9B;AAAA,IACF;AACA,UAAM,aAAa,KAAK,cAAc,QAAQ,IAAI;AAClD;AAAA,EACF;AAEA,QAAM,aAAa,KAAK,gBAAgB,gBAAgB,QAAQ,IAAI;AACtE,CAAC;AAEH,eAAe,aAAa,KAAc,KAAa,YAAqB;AAC1E,MAAI,YAAY;AACd,eAAW,KAAK,MAAM,KAAK,WAAW,GAAG,4BAA4B,CAAC;AACtE,UAAM,KAAK,GAAG;AAAA,EAChB,OAAO;AACL,cAAU,KAAK,GAAG;AAAA,EACpB;AACF;", "names": []}