{"version": 3, "sources": ["../../../src/cli/data.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport { oneoffContext } from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { Command } from \"@commander-js/extra-typings\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { dataInDeployment } from \"./lib/data.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\n\nexport const data = new Command(\"data\")\n  .summary(\"List tables and print data from your database\")\n  .description(\n    \"Inspect your Convex deployment's database.\\n\\n\" +\n      \"  List tables: `npx convex data`\\n\" +\n      \"  List documents in a table: `npx convex data tableName`\\n\\n\" +\n      \"By default, this inspects your dev deployment.\",\n  )\n  .allowExcessArguments(false)\n  .addDataOptions()\n  .addDeploymentSelectionOptions(actionDescription(\"Inspect the database in\"))\n  .showHelpAfterError()\n  .action(async (tableName, options) => {\n    const ctx = await oneoffContext(options);\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, options);\n\n    const deploymentSelection = await getDeploymentSelection(ctx, options);\n    const deployment = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      selectionWithinProject,\n    );\n\n    const deploymentNotice = deployment.deploymentFields?.deploymentName\n      ? `${chalk.bold(deployment.deploymentFields.deploymentName)} deployment's `\n      : \"\";\n\n    await dataInDeployment(ctx, {\n      deploymentUrl: deployment.url,\n      adminKey: deployment.adminKey,\n      deploymentNotice,\n      tableName,\n      ...options,\n    });\n  });\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,SAAS,qBAAqB;AAC9B;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,eAAe;AACxB,SAAS,yBAAyB;AAClC,SAAS,wBAAwB;AACjC,SAAS,8BAA8B;AAEhC,aAAM,OAAO,IAAI,QAAQ,MAAM,EACnC,QAAQ,+CAA+C,EACvD;AAAA,EACC;AAIF,EACC,qBAAqB,KAAK,EAC1B,eAAe,EACf,8BAA8B,kBAAkB,yBAAyB,CAAC,EAC1E,mBAAmB,EACnB,OAAO,OAAO,WAAW,YAAY;AACpC,QAAM,MAAM,MAAM,cAAc,OAAO;AACvC,QAAM,yBACJ,MAAM,4CAA4C,KAAK,OAAO;AAEhE,QAAM,sBAAsB,MAAM,uBAAuB,KAAK,OAAO;AACrE,QAAM,aAAa,MAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,mBAAmB,WAAW,kBAAkB,iBAClD,GAAG,MAAM,KAAK,WAAW,iBAAiB,cAAc,CAAC,mBACzD;AAEJ,QAAM,iBAAiB,KAAK;AAAA,IAC1B,eAAe,WAAW;AAAA,IAC1B,UAAU,WAAW;AAAA,IACrB;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH,CAAC;", "names": []}