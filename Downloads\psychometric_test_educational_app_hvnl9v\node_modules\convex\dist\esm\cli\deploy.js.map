{"version": 3, "sources": ["../../../src/cli/deploy.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport { Command, Option } from \"@commander-js/extra-typings\";\nimport {\n  Context,\n  logFinishedStep,\n  logMessage,\n  oneoffContext,\n  showSpinner,\n} from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport {\n  gitBranchFromEnvironment,\n  isNonProdBuildEnvironment,\n  suggestedEnvVarName,\n} from \"./lib/envvars.js\";\nimport { PushOptions } from \"./lib/push.js\";\nimport {\n  CONVEX_DEPLOY_KEY_ENV_VAR_NAME,\n  CONVEX_SELF_HOSTED_URL_VAR_NAME,\n  CONVEX_DEPLOYMENT_ENV_VAR_NAME,\n  bigBrainAPI,\n} from \"./lib/utils/utils.js\";\nimport { runFunctionAndLog } from \"./lib/run.js\";\nimport { usageStateWarning } from \"./lib/usage.js\";\nimport { getTeamAndProjectFromPreviewAdminKey } from \"./lib/deployment.js\";\nimport { runPush } from \"./lib/components.js\";\nimport { promptYesNo } from \"./lib/utils/prompts.js\";\nimport { deployToDeployment, runCommand } from \"./lib/deploy2.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\nimport { deploymentNameAndTypeFromSelection } from \"./lib/deploymentSelection.js\";\nexport const deploy = new Command(\"deploy\")\n  .summary(\"Deploy to your prod deployment\")\n  .description(\n    \"Deploy to your deployment. By default, this deploys to your prod deployment.\\n\\n\" +\n      `Deploys to a preview deployment if the \\`${CONVEX_DEPLOY_KEY_ENV_VAR_NAME}\\` environment variable is set to a Preview Deploy Key.`,\n  )\n  .allowExcessArguments(false)\n  .addDeployOptions()\n  .addOption(\n    new Option(\n      \"--preview-run <functionName>\",\n      \"Function to run if deploying to a preview deployment. This is ignored if deploying to a production deployment.\",\n    ),\n  )\n  .addOption(\n    new Option(\n      \"--preview-create <name>\",\n      \"The name to associate with this deployment if deploying to a newly created preview deployment. Defaults to the current Git branch name in Vercel, Netlify and GitHub CI. This is ignored if deploying to a production deployment.\",\n    ).conflicts(\"preview-name\"),\n  )\n  .addOption(\n    new Option(\n      \"--check-build-environment <mode>\",\n      \"Whether to check for a non-production build environment before deploying to a production Convex deployment.\",\n    )\n      .choices([\"enable\", \"disable\"] as const)\n      .default(\"enable\" as const)\n      .hideHelp(),\n  )\n  // Hidden options to pass in admin key and url for tests and local development\n  .addOption(new Option(\"--admin-key <adminKey>\").hideHelp())\n  .addOption(new Option(\"--url <url>\").hideHelp())\n  .addOption(\n    new Option(\n      \"--preview-name <name>\",\n      \"[deprecated] Use `--preview-create` instead. The name to associate with this deployment if deploying to a preview deployment.\",\n    )\n      .hideHelp()\n      .conflicts(\"preview-create\"),\n  )\n  .addOption(\n    new Option(\n      \"--env-file <envFile>\",\n      `Path to a custom file of environment variables, for choosing the \\\ndeployment, e.g. ${CONVEX_DEPLOYMENT_ENV_VAR_NAME} or ${CONVEX_SELF_HOSTED_URL_VAR_NAME}. \\\nSame format as .env.local or .env files, and overrides them.`,\n    ),\n  )\n  .addOption(new Option(\"--partition-id <id>\").hideHelp())\n  .showHelpAfterError()\n  .action(async (cmdOptions) => {\n    const ctx = await oneoffContext(cmdOptions);\n\n    const deploymentSelection = await getDeploymentSelection(ctx, cmdOptions);\n    if (\n      cmdOptions.checkBuildEnvironment === \"enable\" &&\n      isNonProdBuildEnvironment() &&\n      deploymentSelection.kind === \"existingDeployment\" &&\n      deploymentSelection.deploymentToActOn.source === \"deployKey\" &&\n      deploymentSelection.deploymentToActOn.deploymentFields?.deploymentType ===\n        \"prod\"\n    ) {\n      await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        printedMessage: `Detected a non-production build environment and \"${CONVEX_DEPLOY_KEY_ENV_VAR_NAME}\" for a production Convex deployment.\\n\n          This is probably unintentional.\n          `,\n      });\n    }\n\n    if (deploymentSelection.kind === \"anonymous\") {\n      logMessage(\n        ctx,\n        \"You are currently developing anonymously with a locally running project.\\n\" +\n          \"To deploy your Convex app to the cloud, log in by running `npx convex login`.\\n\" +\n          \"See https://docs.convex.dev/production for more information on how Convex cloud works and instructions on how to set up hosting.\",\n      );\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: null,\n      });\n    }\n\n    if (deploymentSelection.kind === \"preview\") {\n      // TODO -- add usage state warnings here too once we can do it without a deployment name\n      // await usageStateWarning(ctx);\n      if (cmdOptions.previewName !== undefined) {\n        await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage:\n            \"The `--preview-name` flag has been deprecated in favor of `--preview-create`. Please re-run the command using `--preview-create` instead.\",\n        });\n      }\n\n      const teamAndProjectSlugs = await getTeamAndProjectFromPreviewAdminKey(\n        ctx,\n        deploymentSelection.previewDeployKey,\n      );\n      await deployToNewPreviewDeployment(\n        ctx,\n        {\n          previewDeployKey: deploymentSelection.previewDeployKey,\n          projectSelection: {\n            kind: \"teamAndProjectSlugs\",\n            teamSlug: teamAndProjectSlugs.teamSlug,\n            projectSlug: teamAndProjectSlugs.projectSlug,\n          },\n        },\n        {\n          ...cmdOptions,\n        },\n      );\n    } else {\n      await deployToExistingDeployment(ctx, cmdOptions);\n    }\n  });\n\nasync function deployToNewPreviewDeployment(\n  ctx: Context,\n  deploymentSelection: {\n    previewDeployKey: string;\n    projectSelection: {\n      kind: \"teamAndProjectSlugs\";\n      teamSlug: string;\n      projectSlug: string;\n    };\n  },\n  options: {\n    dryRun?: boolean | undefined;\n    previewCreate?: string | undefined;\n    previewRun?: string | undefined;\n    cmdUrlEnvVarName?: string | undefined;\n    cmd?: string | undefined;\n    verbose?: boolean | undefined;\n    typecheck: \"enable\" | \"try\" | \"disable\";\n    typecheckComponents: boolean;\n    codegen: \"enable\" | \"disable\";\n\n    debug?: boolean | undefined;\n    debugBundlePath?: string | undefined;\n    partitionId?: string | undefined;\n  },\n) {\n  const previewName = options.previewCreate ?? gitBranchFromEnvironment();\n  if (previewName === null) {\n    await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage:\n        \"`npx convex deploy` to a preview deployment could not determine the preview name. Provide one using `--preview-create`\",\n    });\n  }\n\n  if (options.dryRun) {\n    logFinishedStep(\n      ctx,\n      `Would have claimed preview deployment for \"${previewName}\"`,\n    );\n    await runCommand(ctx, {\n      cmdUrlEnvVarName: options.cmdUrlEnvVarName,\n      cmd: options.cmd,\n      dryRun: !!options.dryRun,\n      url: \"https://<PREVIEW DEPLOYMENT>.convex.cloud\",\n      adminKey: \"preview-deployment-admin-key\",\n    });\n    logFinishedStep(\n      ctx,\n      `Would have deployed Convex functions to preview deployment for \"${previewName}\"`,\n    );\n    if (options.previewRun !== undefined) {\n      logMessage(ctx, `Would have run function \"${options.previewRun}\"`);\n    }\n    return;\n  }\n  const data = await bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: \"claim_preview_deployment\",\n    data: {\n      projectSelection: deploymentSelection.projectSelection,\n      identifier: previewName,\n      partitionId: options.partitionId\n        ? parseInt(options.partitionId)\n        : undefined,\n    },\n  });\n\n  const previewAdminKey = data.adminKey;\n  const previewUrl = data.instanceUrl;\n\n  await runCommand(ctx, {\n    ...options,\n    url: previewUrl,\n    adminKey: previewAdminKey,\n  });\n\n  const pushOptions: PushOptions = {\n    deploymentName: data.deploymentName,\n    adminKey: previewAdminKey,\n    verbose: !!options.verbose,\n    dryRun: false,\n    typecheck: options.typecheck,\n    typecheckComponents: options.typecheckComponents,\n    debug: !!options.debug,\n    debugBundlePath: options.debugBundlePath,\n    codegen: options.codegen === \"enable\",\n    url: previewUrl,\n    liveComponentSources: false,\n  };\n  showSpinner(ctx, `Deploying to ${previewUrl}...`);\n  await runPush(ctx, pushOptions);\n  logFinishedStep(ctx, `Deployed Convex functions to ${previewUrl}`);\n\n  if (options.previewRun !== undefined) {\n    await runFunctionAndLog(ctx, {\n      deploymentUrl: previewUrl,\n      adminKey: previewAdminKey,\n      functionName: options.previewRun,\n      argsString: \"{}\",\n      componentPath: undefined,\n      callbacks: {\n        onSuccess: () => {\n          logFinishedStep(\n            ctx,\n            `Finished running function \"${options.previewRun}\"`,\n          );\n        },\n      },\n    });\n  }\n}\n\nasync function deployToExistingDeployment(\n  ctx: Context,\n  options: {\n    verbose?: boolean | undefined;\n    dryRun?: boolean | undefined;\n    yes?: boolean | undefined;\n    typecheck: \"enable\" | \"try\" | \"disable\";\n    typecheckComponents: boolean;\n    codegen: \"enable\" | \"disable\";\n    cmd?: string | undefined;\n    cmdUrlEnvVarName?: string | undefined;\n\n    debugBundlePath?: string | undefined;\n    debug?: boolean | undefined;\n    adminKey?: string | undefined;\n    url?: string | undefined;\n    writePushRequest?: string | undefined;\n    liveComponentSources?: boolean | undefined;\n    partitionId?: string | undefined;\n    envFile?: string | undefined;\n  },\n) {\n  const selectionWithinProject =\n    await deploymentSelectionWithinProjectFromOptions(ctx, {\n      ...options,\n      implicitProd: true,\n    });\n  const deploymentSelection = await getDeploymentSelection(ctx, options);\n  const deploymentToActOn = await loadSelectedDeploymentCredentials(\n    ctx,\n    deploymentSelection,\n    selectionWithinProject,\n  );\n  if (deploymentToActOn.deploymentFields !== null) {\n    await usageStateWarning(\n      ctx,\n      deploymentToActOn.deploymentFields.deploymentName,\n    );\n  }\n  const configuredDeployment =\n    deploymentNameAndTypeFromSelection(deploymentSelection);\n  if (configuredDeployment !== null && configuredDeployment.name !== null) {\n    const shouldPushToProd =\n      configuredDeployment.name ===\n        deploymentToActOn.deploymentFields?.deploymentName ||\n      (options.yes ??\n        (await askToConfirmPush(\n          ctx,\n          {\n            configuredName: configuredDeployment.name,\n            configuredType: configuredDeployment.type,\n            requestedName: deploymentToActOn.deploymentFields?.deploymentName!,\n            requestedType: deploymentToActOn.deploymentFields?.deploymentType!,\n          },\n          deploymentToActOn.url,\n        )));\n    if (!shouldPushToProd) {\n      await ctx.crash({\n        exitCode: 1,\n        printedMessage: null,\n        errorType: \"fatal\",\n      });\n    }\n  }\n\n  await deployToDeployment(\n    ctx,\n    {\n      url: deploymentToActOn.url,\n      adminKey: deploymentToActOn.adminKey,\n      deploymentName:\n        deploymentToActOn.deploymentFields?.deploymentName ?? null,\n    },\n    options,\n  );\n}\n\nasync function askToConfirmPush(\n  ctx: Context,\n  deployment: {\n    configuredName: string;\n    configuredType: string | null;\n    requestedName: string;\n    requestedType: string;\n  },\n  prodUrl: string,\n) {\n  logMessage(\n    ctx,\n    `\\\nYou're currently developing against your ${chalk.bold(\n      deployment.configuredType ?? \"dev\",\n    )} deployment\n\n  ${deployment.configuredName} (set in CONVEX_DEPLOYMENT)\n\nYour ${chalk.bold(deployment.requestedType)} deployment ${chalk.bold(\n      deployment.requestedName,\n    )} serves traffic at:\n\n  ${(await suggestedEnvVarName(ctx)).envVar}=${chalk.bold(prodUrl)}\n\nMake sure that your published client is configured with this URL (for instructions see https://docs.convex.dev/hosting)\\n`,\n  );\n  return promptYesNo(ctx, {\n    message: `Do you want to push your code to your ${deployment.requestedType} deployment ${deployment.requestedName} now?`,\n    default: true,\n  });\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,SAAS,SAAS,cAAc;AAChC;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,yBAAyB;AAClC,SAAS,yBAAyB;AAClC,SAAS,4CAA4C;AACrD,SAAS,eAAe;AACxB,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB,kBAAkB;AAC/C,SAAS,8BAA8B;AACvC,SAAS,0CAA0C;AAC5C,aAAM,SAAS,IAAI,QAAQ,QAAQ,EACvC,QAAQ,gCAAgC,EACxC;AAAA,EACC;AAAA;AAAA,2CAC8C,8BAA8B;AAC9E,EACC,qBAAqB,KAAK,EAC1B,iBAAiB,EACjB;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EAAE,UAAU,cAAc;AAC5B,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,CAAC,UAAU,SAAS,CAAU,EACtC,QAAQ,QAAiB,EACzB,SAAS;AACd,EAEC,UAAU,IAAI,OAAO,wBAAwB,EAAE,SAAS,CAAC,EACzD,UAAU,IAAI,OAAO,aAAa,EAAE,SAAS,CAAC,EAC9C;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,SAAS,EACT,UAAU,gBAAgB;AAC/B,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA,qFACa,8BAA8B,OAAO,+BAA+B;AAAA,EAEnF;AACF,EACC,UAAU,IAAI,OAAO,qBAAqB,EAAE,SAAS,CAAC,EACtD,mBAAmB,EACnB,OAAO,OAAO,eAAe;AAC5B,QAAM,MAAM,MAAM,cAAc,UAAU;AAE1C,QAAM,sBAAsB,MAAM,uBAAuB,KAAK,UAAU;AACxE,MACE,WAAW,0BAA0B,YACrC,0BAA0B,KAC1B,oBAAoB,SAAS,wBAC7B,oBAAoB,kBAAkB,WAAW,eACjD,oBAAoB,kBAAkB,kBAAkB,mBACtD,QACF;AACA,UAAM,IAAI,MAAM;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,oDAAoD,8BAA8B;AAAA;AAAA;AAAA;AAAA,IAGpG,CAAC;AAAA,EACH;AAEA,MAAI,oBAAoB,SAAS,aAAa;AAC5C;AAAA,MACE;AAAA,MACA;AAAA,IAGF;AACA,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,MAAI,oBAAoB,SAAS,WAAW;AAG1C,QAAI,WAAW,gBAAgB,QAAW;AACxC,YAAM,IAAI,MAAM;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,IACH;AAEA,UAAM,sBAAsB,MAAM;AAAA,MAChC;AAAA,MACA,oBAAoB;AAAA,IACtB;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,QACE,kBAAkB,oBAAoB;AAAA,QACtC,kBAAkB;AAAA,UAChB,MAAM;AAAA,UACN,UAAU,oBAAoB;AAAA,UAC9B,aAAa,oBAAoB;AAAA,QACnC;AAAA,MACF;AAAA,MACA;AAAA,QACE,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,2BAA2B,KAAK,UAAU;AAAA,EAClD;AACF,CAAC;AAEH,eAAe,6BACb,KACA,qBAQA,SAeA;AACA,QAAM,cAAc,QAAQ,iBAAiB,yBAAyB;AACtE,MAAI,gBAAgB,MAAM;AACxB,UAAM,IAAI,MAAM;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IACJ,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ,QAAQ;AAClB;AAAA,MACE;AAAA,MACA,8CAA8C,WAAW;AAAA,IAC3D;AACA,UAAM,WAAW,KAAK;AAAA,MACpB,kBAAkB,QAAQ;AAAA,MAC1B,KAAK,QAAQ;AAAA,MACb,QAAQ,CAAC,CAAC,QAAQ;AAAA,MAClB,KAAK;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AACD;AAAA,MACE;AAAA,MACA,mEAAmE,WAAW;AAAA,IAChF;AACA,QAAI,QAAQ,eAAe,QAAW;AACpC,iBAAW,KAAK,4BAA4B,QAAQ,UAAU,GAAG;AAAA,IACnE;AACA;AAAA,EACF;AACA,QAAM,OAAO,MAAM,YAAY;AAAA,IAC7B;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,MACJ,kBAAkB,oBAAoB;AAAA,MACtC,YAAY;AAAA,MACZ,aAAa,QAAQ,cACjB,SAAS,QAAQ,WAAW,IAC5B;AAAA,IACN;AAAA,EACF,CAAC;AAED,QAAM,kBAAkB,KAAK;AAC7B,QAAM,aAAa,KAAK;AAExB,QAAM,WAAW,KAAK;AAAA,IACpB,GAAG;AAAA,IACH,KAAK;AAAA,IACL,UAAU;AAAA,EACZ,CAAC;AAED,QAAM,cAA2B;AAAA,IAC/B,gBAAgB,KAAK;AAAA,IACrB,UAAU;AAAA,IACV,SAAS,CAAC,CAAC,QAAQ;AAAA,IACnB,QAAQ;AAAA,IACR,WAAW,QAAQ;AAAA,IACnB,qBAAqB,QAAQ;AAAA,IAC7B,OAAO,CAAC,CAAC,QAAQ;AAAA,IACjB,iBAAiB,QAAQ;AAAA,IACzB,SAAS,QAAQ,YAAY;AAAA,IAC7B,KAAK;AAAA,IACL,sBAAsB;AAAA,EACxB;AACA,cAAY,KAAK,gBAAgB,UAAU,KAAK;AAChD,QAAM,QAAQ,KAAK,WAAW;AAC9B,kBAAgB,KAAK,gCAAgC,UAAU,EAAE;AAEjE,MAAI,QAAQ,eAAe,QAAW;AACpC,UAAM,kBAAkB,KAAK;AAAA,MAC3B,eAAe;AAAA,MACf,UAAU;AAAA,MACV,cAAc,QAAQ;AAAA,MACtB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,WAAW;AAAA,QACT,WAAW,MAAM;AACf;AAAA,YACE;AAAA,YACA,8BAA8B,QAAQ,UAAU;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,eAAe,2BACb,KACA,SAmBA;AACA,QAAM,yBACJ,MAAM,4CAA4C,KAAK;AAAA,IACrD,GAAG;AAAA,IACH,cAAc;AAAA,EAChB,CAAC;AACH,QAAM,sBAAsB,MAAM,uBAAuB,KAAK,OAAO;AACrE,QAAM,oBAAoB,MAAM;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,kBAAkB,qBAAqB,MAAM;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA,kBAAkB,iBAAiB;AAAA,IACrC;AAAA,EACF;AACA,QAAM,uBACJ,mCAAmC,mBAAmB;AACxD,MAAI,yBAAyB,QAAQ,qBAAqB,SAAS,MAAM;AACvE,UAAM,mBACJ,qBAAqB,SACnB,kBAAkB,kBAAkB,mBACrC,QAAQ,OACN,MAAM;AAAA,MACL;AAAA,MACA;AAAA,QACE,gBAAgB,qBAAqB;AAAA,QACrC,gBAAgB,qBAAqB;AAAA,QACrC,eAAe,kBAAkB,kBAAkB;AAAA,QACnD,eAAe,kBAAkB,kBAAkB;AAAA,MACrD;AAAA,MACA,kBAAkB;AAAA,IACpB;AACJ,QAAI,CAAC,kBAAkB;AACrB,YAAM,IAAI,MAAM;AAAA,QACd,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,MACE,KAAK,kBAAkB;AAAA,MACvB,UAAU,kBAAkB;AAAA,MAC5B,gBACE,kBAAkB,kBAAkB,kBAAkB;AAAA,IAC1D;AAAA,IACA;AAAA,EACF;AACF;AAEA,eAAe,iBACb,KACA,YAMA,SACA;AACA;AAAA,IACE;AAAA,IACA,4CACuC,MAAM;AAAA,MAC3C,WAAW,kBAAkB;AAAA,IAC/B,CAAC;AAAA;AAAA,IAED,WAAW,cAAc;AAAA;AAAA,OAEtB,MAAM,KAAK,WAAW,aAAa,CAAC,eAAe,MAAM;AAAA,MAC1D,WAAW;AAAA,IACb,CAAC;AAAA;AAAA,KAEA,MAAM,oBAAoB,GAAG,GAAG,MAAM,IAAI,MAAM,KAAK,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,EAGhE;AACA,SAAO,YAAY,KAAK;AAAA,IACtB,SAAS,yCAAyC,WAAW,aAAa,eAAe,WAAW,aAAa;AAAA,IACjH,SAAS;AAAA,EACX,CAAC;AACH;", "names": []}