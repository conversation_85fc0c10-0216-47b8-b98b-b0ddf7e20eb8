{"version": 3, "sources": ["../../../src/cli/dev.ts"], "sourcesContent": ["import { Command, Option } from \"@commander-js/extra-typings\";\nimport { logV<PERSON><PERSON><PERSON>, oneoffContext } from \"../bundler/context.js\";\nimport { deploymentCredentialsOrConfigure } from \"./configure.js\";\nimport { usageStateWarning } from \"./lib/usage.js\";\nimport { normalizeDevOptions } from \"./lib/command.js\";\nimport { devAgainstDeployment } from \"./lib/dev.js\";\nimport { deploymentSelectionWithinProjectFromOptions } from \"./lib/api.js\";\nimport {\n  CONVEX_DEPLOYMENT_ENV_VAR_NAME,\n  CONVEX_SELF_HOSTED_URL_VAR_NAME,\n} from \"./lib/utils/utils.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\n\nexport const dev = new Command(\"dev\")\n  .summary(\"Develop against a dev deployment, watching for changes\")\n  .description(\n    \"Develop against a dev deployment, watching for changes\\n\\n\" +\n      \"  1. Configures a new or existing project (if needed)\\n\" +\n      \"  2. Updates generated types and pushes code to the configured dev deployment\\n\" +\n      \"  3. Runs the provided command (if `--run` or `--run-sh` is used)\\n\" +\n      \"  4. Watches for file changes, and repeats step 2\\n\",\n  )\n  .allowExcessArguments(false)\n  .option(\"-v, --verbose\", \"Show full listing of changes\")\n  .addOption(\n    new Option(\n      \"--typecheck <mode>\",\n      `Check TypeScript files with \\`tsc --noEmit\\`.`,\n    )\n      .choices([\"enable\", \"try\", \"disable\"] as const)\n      .default(\"try\" as const),\n  )\n  .option(\n    \"--typecheck-components\",\n    \"Check TypeScript files within component implementations with `tsc --noEmit`.\",\n    false,\n  )\n  .addOption(\n    new Option(\"--codegen <mode>\", \"Regenerate code in `convex/_generated/`\")\n      .choices([\"enable\", \"disable\"] as const)\n      .default(\"enable\" as const),\n  )\n  .option(\n    \"--once\",\n    \"Execute only the first 3 steps, stop on any failure\",\n    false,\n  )\n  .option(\n    \"--until-success\",\n    \"Execute only the first 3 steps, on failure watch for local and remote changes and retry steps 2 and 3\",\n    false,\n  )\n  .addOption(\n    new Option(\n      \"--run <functionName>\",\n      \"The identifier of the function to run in step 3, \" +\n        \"like `api.init.createData` or `myDir/myFile:myFunction`\",\n    ).conflicts([\"--run-sh\"]),\n  )\n  .option(\n    \"--run-component <functionName>\",\n    \"If --run is used and the function is in a component, the path the component tree defined in convex.config.ts. \" +\n      \"Components are a beta feature. This flag is unstable and may change in subsequent releases.\",\n  )\n  .addOption(\n    new Option(\n      \"--run-sh <command>\",\n      \"A shell command to run in step 3, like `node myScript.js`. \" +\n        \"If you just want to run a Convex function, use `--run` instead.\",\n    ).conflicts([\"--run\"]),\n  )\n  .addOption(\n    new Option(\n      \"--tail-logs [mode]\",\n      \"Choose whether to tail Convex function logs in this terminal\",\n    )\n      .choices([\"always\", \"pause-on-deploy\", \"disable\"] as const)\n      .default(\"pause-on-deploy\"),\n  )\n  .addOption(new Option(\"--trace-events\").default(false).hideHelp())\n  .addOption(new Option(\"--debug-bundle-path <path>\").hideHelp())\n  .addOption(new Option(\"--live-component-sources\").hideHelp())\n  .addOption(\n    new Option(\n      \"--configure [choice]\",\n      \"Ignore existing configuration and configure new or existing project, interactively or set by --team <team_slug>, --project <project_slug>, and --dev-deployment local|cloud\",\n    )\n      .choices([\"new\", \"existing\"] as const)\n      .conflicts([\"--local\", \"--cloud\"]),\n  )\n  .addOption(\n    new Option(\n      \"--team <team_slug>\",\n      \"The team you'd like to use for this project\",\n    ).hideHelp(),\n  )\n  .addOption(\n    new Option(\n      \"--project <project_slug>\",\n      \"The name of the project you'd like to configure\",\n    ).hideHelp(),\n  )\n  .addOption(\n    new Option(\n      \"--dev-deployment <mode>\",\n      \"Use a local or cloud deployment for dev for this project\",\n    )\n      .choices([\"cloud\", \"local\"] as const)\n      .conflicts([\"--prod\"])\n      .hideHelp(),\n  )\n  .addOption(\n    new Option(\n      \"--prod\",\n      \"Develop live against this project's production deployment.\",\n    )\n      .default(false)\n      .hideHelp(),\n  )\n  .addOption(\n    new Option(\n      \"--env-file <envFile>\",\n      `Path to a custom file of environment variables, for choosing the \\\ndeployment, e.g. ${CONVEX_DEPLOYMENT_ENV_VAR_NAME} or ${CONVEX_SELF_HOSTED_URL_VAR_NAME}. \\\nSame format as .env.local or .env files, and overrides them.`,\n    ),\n  )\n  .addOption(new Option(\"--skip-push\").default(false).hideHelp())\n  .addOption(new Option(\"--admin-key <adminKey>\").hideHelp())\n  .addOption(new Option(\"--url <url>\").hideHelp())\n  // Options for testing\n  .addOption(new Option(\"--override-auth-url <url>\").hideHelp())\n  .addOption(new Option(\"--override-auth-client <id>\").hideHelp())\n  .addOption(new Option(\"--override-auth-username <username>\").hideHelp())\n  .addOption(new Option(\"--override-auth-password <password>\").hideHelp())\n  .addOption(new Option(\"--local-cloud-port <port>\").hideHelp())\n  .addOption(new Option(\"--local-site-port <port>\").hideHelp())\n  .addOption(new Option(\"--local-backend-version <version>\").hideHelp())\n  .addOption(new Option(\"--local-force-upgrade\").default(false).hideHelp())\n  .addOption(new Option(\"--partition-id <id>\").hideHelp())\n  .addOption(\n    new Option(\n      \"--local\",\n      \"Use local deployment regardless of last used backend. DB data will not be downloaded from any cloud deployment.\",\n    )\n      .default(false)\n      .conflicts([\"--prod\", \"--url\", \"--admin-key\", \"--cloud\"])\n      .hideHelp(),\n  )\n  .addOption(\n    new Option(\n      \"--cloud\",\n      \"Use cloud deployment regardles of last used backend. DB data will not be uploaded from local.\",\n    )\n      .default(false)\n      .conflicts([\"--prod\", \"--url\", \"--admin-key\", \"--local\"])\n      .hideHelp(),\n  )\n  .showHelpAfterError()\n  .action(async (cmdOptions) => {\n    const ctx = await oneoffContext(cmdOptions);\n    process.on(\"SIGINT\", async () => {\n      logVerbose(ctx, \"Received SIGINT, cleaning up...\");\n      await ctx.flushAndExit(-2);\n    });\n\n    const devOptions = await normalizeDevOptions(ctx, cmdOptions);\n\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, cmdOptions);\n\n    if (cmdOptions.configure === undefined) {\n      if (cmdOptions.team || cmdOptions.project || cmdOptions.devDeployment)\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage:\n            \"`--team, --project, and --dev-deployment can can only be used with `--configure`.\",\n        });\n    }\n\n    const localOptions: {\n      ports?: { cloud: number; site: number };\n      backendVersion?: string | undefined;\n      forceUpgrade: boolean;\n    } = { forceUpgrade: false };\n    if (!cmdOptions.local && cmdOptions.devDeployment !== \"local\") {\n      if (\n        cmdOptions.localCloudPort !== undefined ||\n        cmdOptions.localSitePort !== undefined ||\n        cmdOptions.localBackendVersion !== undefined ||\n        cmdOptions.localForceUpgrade === true\n      ) {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage:\n            \"`--local-*` options can only be used with `--configure --dev-deployment local` or `--local`.\",\n        });\n      }\n    } else {\n      if (cmdOptions.localCloudPort !== undefined) {\n        if (cmdOptions.localSitePort === undefined) {\n          return await ctx.crash({\n            exitCode: 1,\n            errorType: \"fatal\",\n            printedMessage:\n              \"`--local-cloud-port` requires `--local-site-port` to be set.\",\n          });\n        }\n        localOptions[\"ports\"] = {\n          cloud: parseInt(cmdOptions.localCloudPort),\n          site: parseInt(cmdOptions.localSitePort),\n        };\n      }\n      localOptions[\"backendVersion\"] = cmdOptions.localBackendVersion;\n      localOptions[\"forceUpgrade\"] = cmdOptions.localForceUpgrade;\n    }\n\n    const partitionId = cmdOptions.partitionId\n      ? parseInt(cmdOptions.partitionId)\n      : undefined;\n    const configure =\n      cmdOptions.configure === true ? \"ask\" : (cmdOptions.configure ?? null);\n    const deploymentSelection = await getDeploymentSelection(ctx, cmdOptions);\n    const credentials = await deploymentCredentialsOrConfigure(\n      ctx,\n      deploymentSelection,\n      configure,\n      {\n        ...cmdOptions,\n        localOptions,\n        selectionWithinProject,\n      },\n      partitionId,\n    );\n\n    if (credentials.deploymentFields !== null) {\n      await usageStateWarning(ctx, credentials.deploymentFields.deploymentName);\n    }\n\n    if (cmdOptions.skipPush) {\n      return;\n    }\n\n    await devAgainstDeployment(\n      ctx,\n      {\n        url: credentials.url,\n        adminKey: credentials.adminKey,\n        deploymentName: credentials.deploymentFields?.deploymentName ?? null,\n      },\n      devOptions,\n    );\n  });\n"], "mappings": ";AAAA,SAAS,SAAS,cAAc;AAChC,SAAS,YAAY,qBAAqB;AAC1C,SAAS,wCAAwC;AACjD,SAAS,yBAAyB;AAClC,SAAS,2BAA2B;AACpC,SAAS,4BAA4B;AACrC,SAAS,mDAAmD;AAC5D;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,8BAA8B;AAEhC,aAAM,MAAM,IAAI,QAAQ,KAAK,EACjC,QAAQ,wDAAwD,EAChE;AAAA,EACC;AAKF,EACC,qBAAqB,KAAK,EAC1B,OAAO,iBAAiB,8BAA8B,EACtD;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,CAAC,UAAU,OAAO,SAAS,CAAU,EAC7C,QAAQ,KAAc;AAC3B,EACC;AAAA,EACC;AAAA,EACA;AAAA,EACA;AACF,EACC;AAAA,EACC,IAAI,OAAO,oBAAoB,yCAAyC,EACrE,QAAQ,CAAC,UAAU,SAAS,CAAU,EACtC,QAAQ,QAAiB;AAC9B,EACC;AAAA,EACC;AAAA,EACA;AAAA,EACA;AACF,EACC;AAAA,EACC;AAAA,EACA;AAAA,EACA;AACF,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EAEF,EAAE,UAAU,CAAC,UAAU,CAAC;AAC1B,EACC;AAAA,EACC;AAAA,EACA;AAEF,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EAEF,EAAE,UAAU,CAAC,OAAO,CAAC;AACvB,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,CAAC,UAAU,mBAAmB,SAAS,CAAU,EACzD,QAAQ,iBAAiB;AAC9B,EACC,UAAU,IAAI,OAAO,gBAAgB,EAAE,QAAQ,KAAK,EAAE,SAAS,CAAC,EAChE,UAAU,IAAI,OAAO,4BAA4B,EAAE,SAAS,CAAC,EAC7D,UAAU,IAAI,OAAO,0BAA0B,EAAE,SAAS,CAAC,EAC3D;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,CAAC,OAAO,UAAU,CAAU,EACpC,UAAU,CAAC,WAAW,SAAS,CAAC;AACrC,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EAAE,SAAS;AACb,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EAAE,SAAS;AACb,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,CAAC,SAAS,OAAO,CAAU,EACnC,UAAU,CAAC,QAAQ,CAAC,EACpB,SAAS;AACd,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,KAAK,EACb,SAAS;AACd,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA,qFACa,8BAA8B,OAAO,+BAA+B;AAAA,EAEnF;AACF,EACC,UAAU,IAAI,OAAO,aAAa,EAAE,QAAQ,KAAK,EAAE,SAAS,CAAC,EAC7D,UAAU,IAAI,OAAO,wBAAwB,EAAE,SAAS,CAAC,EACzD,UAAU,IAAI,OAAO,aAAa,EAAE,SAAS,CAAC,EAE9C,UAAU,IAAI,OAAO,2BAA2B,EAAE,SAAS,CAAC,EAC5D,UAAU,IAAI,OAAO,6BAA6B,EAAE,SAAS,CAAC,EAC9D,UAAU,IAAI,OAAO,qCAAqC,EAAE,SAAS,CAAC,EACtE,UAAU,IAAI,OAAO,qCAAqC,EAAE,SAAS,CAAC,EACtE,UAAU,IAAI,OAAO,2BAA2B,EAAE,SAAS,CAAC,EAC5D,UAAU,IAAI,OAAO,0BAA0B,EAAE,SAAS,CAAC,EAC3D,UAAU,IAAI,OAAO,mCAAmC,EAAE,SAAS,CAAC,EACpE,UAAU,IAAI,OAAO,uBAAuB,EAAE,QAAQ,KAAK,EAAE,SAAS,CAAC,EACvE,UAAU,IAAI,OAAO,qBAAqB,EAAE,SAAS,CAAC,EACtD;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,KAAK,EACb,UAAU,CAAC,UAAU,SAAS,eAAe,SAAS,CAAC,EACvD,SAAS;AACd,EACC;AAAA,EACC,IAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,EACG,QAAQ,KAAK,EACb,UAAU,CAAC,UAAU,SAAS,eAAe,SAAS,CAAC,EACvD,SAAS;AACd,EACC,mBAAmB,EACnB,OAAO,OAAO,eAAe;AAC5B,QAAM,MAAM,MAAM,cAAc,UAAU;AAC1C,UAAQ,GAAG,UAAU,YAAY;AAC/B,eAAW,KAAK,iCAAiC;AACjD,UAAM,IAAI,aAAa,EAAE;AAAA,EAC3B,CAAC;AAED,QAAM,aAAa,MAAM,oBAAoB,KAAK,UAAU;AAE5D,QAAM,yBACJ,MAAM,4CAA4C,KAAK,UAAU;AAEnE,MAAI,WAAW,cAAc,QAAW;AACtC,QAAI,WAAW,QAAQ,WAAW,WAAW,WAAW;AACtD,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,EACL;AAEA,QAAM,eAIF,EAAE,cAAc,MAAM;AAC1B,MAAI,CAAC,WAAW,SAAS,WAAW,kBAAkB,SAAS;AAC7D,QACE,WAAW,mBAAmB,UAC9B,WAAW,kBAAkB,UAC7B,WAAW,wBAAwB,UACnC,WAAW,sBAAsB,MACjC;AACA,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF,OAAO;AACL,QAAI,WAAW,mBAAmB,QAAW;AAC3C,UAAI,WAAW,kBAAkB,QAAW;AAC1C,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBACE;AAAA,QACJ,CAAC;AAAA,MACH;AACA,mBAAa,OAAO,IAAI;AAAA,QACtB,OAAO,SAAS,WAAW,cAAc;AAAA,QACzC,MAAM,SAAS,WAAW,aAAa;AAAA,MACzC;AAAA,IACF;AACA,iBAAa,gBAAgB,IAAI,WAAW;AAC5C,iBAAa,cAAc,IAAI,WAAW;AAAA,EAC5C;AAEA,QAAM,cAAc,WAAW,cAC3B,SAAS,WAAW,WAAW,IAC/B;AACJ,QAAM,YACJ,WAAW,cAAc,OAAO,QAAS,WAAW,aAAa;AACnE,QAAM,sBAAsB,MAAM,uBAAuB,KAAK,UAAU;AACxE,QAAM,cAAc,MAAM;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF;AAEA,MAAI,YAAY,qBAAqB,MAAM;AACzC,UAAM,kBAAkB,KAAK,YAAY,iBAAiB,cAAc;AAAA,EAC1E;AAEA,MAAI,WAAW,UAAU;AACvB;AAAA,EACF;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,MACE,KAAK,YAAY;AAAA,MACjB,UAAU,YAAY;AAAA,MACtB,gBAAgB,YAAY,kBAAkB,kBAAkB;AAAA,IAClE;AAAA,IACA;AAAA,EACF;AACF,CAAC;", "names": []}