{"version": 3, "sources": ["../../../src/cli/functionSpec.ts"], "sourcesContent": ["import { oneoffContext } from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { Command, Option } from \"@commander-js/extra-typings\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { functionSpecForDeployment } from \"./lib/functionSpec.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\nexport const functionSpec = new Command(\"function-spec\")\n  .summary(\"List function metadata from your deployment\")\n  .description(\n    \"List argument and return values to your Convex functions.\\n\\n\" +\n      \"By default, this inspects your dev deployment.\",\n  )\n  .allowExcessArguments(false)\n  .addOption(new Option(\"--file\", \"Output as JSON to a file.\"))\n  .addDeploymentSelectionOptions(\n    actionDescription(\"Read function metadata from\"),\n  )\n  .showHelpAfterError()\n  .action(async (options) => {\n    const ctx = await oneoffContext(options);\n    const deploymentSelection = await getDeploymentSelection(ctx, options);\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, options);\n    const { adminKey, url: deploymentUrl } =\n      await loadSelectedDeploymentCredentials(\n        ctx,\n        deploymentSelection,\n        selectionWithinProject,\n      );\n\n    await functionSpecForDeployment(ctx, {\n      deploymentUrl,\n      adminKey,\n      file: !!options.file,\n    });\n  });\n"], "mappings": ";AAAA,SAAS,qBAAqB;AAC9B;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,SAAS,cAAc;AAChC,SAAS,yBAAyB;AAClC,SAAS,iCAAiC;AAC1C,SAAS,8BAA8B;AAChC,aAAM,eAAe,IAAI,QAAQ,eAAe,EACpD,QAAQ,6CAA6C,EACrD;AAAA,EACC;AAEF,EACC,qBAAqB,KAAK,EAC1B,UAAU,IAAI,OAAO,UAAU,2BAA2B,CAAC,EAC3D;AAAA,EACC,kBAAkB,6BAA6B;AACjD,EACC,mBAAmB,EACnB,OAAO,OAAO,YAAY;AACzB,QAAM,MAAM,MAAM,cAAc,OAAO;AACvC,QAAM,sBAAsB,MAAM,uBAAuB,KAAK,OAAO;AACrE,QAAM,yBACJ,MAAM,4CAA4C,KAAK,OAAO;AAChE,QAAM,EAAE,UAAU,KAAK,cAAc,IACnC,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEF,QAAM,0BAA0B,KAAK;AAAA,IACnC;AAAA,IACA;AAAA,IACA,MAAM,CAAC,CAAC,QAAQ;AAAA,EAClB,CAAC;AACH,CAAC;", "names": []}