{"version": 3, "sources": ["../../../../../../src/cli/lib/components/definition/directoryStructure.ts"], "sourcesContent": ["import path from \"path\";\nimport { Context } from \"../../../../bundler/context.js\";\nimport {\n  DEFINITION_FILENAME_JS,\n  DEFINITION_FILENAME_TS,\n} from \"../constants.js\";\nimport { getFunctionsDirectoryPath } from \"../../config.js\";\n\n/**\n * A component definition's location on the local filesystem using absolute paths.\n *\n * For module resolution it would be useful to avoid resolving any symlinks:\n * node modules are often symlinked by e.g. pnpm but relative paths should generally be\n * understood from their symlink location. We don't currently do this though, it made\n * Windows harder to support.\n *\n * None of these properties are the import string, which might have been an unqualifed import\n * (e.g. 'convex-waitlist' instead of '../node_modules/convex-waitlist/convex.config.ts')\n */\nexport type ComponentDirectory = {\n  /**\n   * Is this component directory for the root component?\n   */\n  isRoot: boolean;\n\n  /**\n   * Absolute local filesystem path to the component definition's directory.\n   */\n  path: string;\n\n  /**\n   * Absolute local filesystem path to the `convex.config.{ts,js}` file within the component definition.\n   */\n  definitionPath: string;\n};\n\n/**\n * Qualify (ensure a leading dot) a path and make it relative to a working dir.\n * Qualifying a path clarifies to esbuild that it represents a local file system\n * path, not a remote path on the npm registry.\n *\n * If this path were made relative without resolving symlinks it would be a\n * prettier identifier for the component directory, but instead symlinks are\n * always resolved.\n */\nexport function qualifiedDefinitionPath(\n  directory: ComponentDirectory,\n  workingDir = \".\",\n) {\n  const definitionPath = path.relative(workingDir, directory.definitionPath);\n  return `./${definitionPath}`;\n}\n\n// NB: The process cwd will be used to resolve the directory specified in the constructor.\nexport function isComponentDirectory(\n  ctx: Context,\n  directory: string,\n  isRoot: boolean,\n):\n  | { kind: \"ok\"; component: ComponentDirectory }\n  | { kind: \"err\"; why: string } {\n  if (!ctx.fs.exists(directory)) {\n    return { kind: \"err\", why: `Directory doesn't exist` };\n  }\n  const dirStat = ctx.fs.stat(directory);\n  if (!dirStat.isDirectory()) {\n    return { kind: \"err\", why: `Not a directory` };\n  }\n\n  // Check that we have a definition file, defaulting to `.ts` but falling back to `.js`.\n  let filename = DEFINITION_FILENAME_TS;\n  let definitionPath = path.resolve(path.join(directory, filename));\n  if (!ctx.fs.exists(definitionPath)) {\n    filename = DEFINITION_FILENAME_JS;\n    definitionPath = path.resolve(path.join(directory, filename));\n  }\n  if (!ctx.fs.exists(definitionPath)) {\n    return {\n      kind: \"err\",\n      why: `Directory doesn't contain a ${filename} file`,\n    };\n  }\n  const definitionStat = ctx.fs.stat(definitionPath);\n  if (!definitionStat.isFile()) {\n    return {\n      kind: \"err\",\n      why: `Component definition ${filename} isn't a file`,\n    };\n  }\n  return {\n    kind: \"ok\",\n    component: {\n      isRoot,\n      path: path.resolve(directory),\n      definitionPath: definitionPath,\n    },\n  };\n}\n\nexport async function buildComponentDirectory(\n  ctx: Context,\n  definitionPath: string,\n): Promise<ComponentDirectory> {\n  const convexDir = path.resolve(await getFunctionsDirectoryPath(ctx));\n  const isRoot = path.dirname(path.resolve(definitionPath)) === convexDir;\n  const isComponent = isComponentDirectory(\n    ctx,\n    path.dirname(definitionPath),\n    isRoot,\n  );\n  if (isComponent.kind === \"err\") {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      printedMessage: `Invalid component directory (${isComponent.why}): ${path.dirname(definitionPath)}`,\n    });\n  }\n  return isComponent.component;\n}\n\n/**\n * ComponentPath is the local path identifying a\n * component definition. It is the unqualified (it never starts with \"./\")\n * relative path from the convex directory of the app (root component)\n * to the directory where a component definition lives.\n *\n * Note the convex/ directory of the root component is not necessarily\n * the working directory. It is currently never the same as the working\n * directory since `npx convex` must be invoked from the package root instead.\n */\nexport type ComponentDefinitionPath = string & {\n  __brand: \"ComponentDefinitionPath\";\n};\n\nexport function toComponentDefinitionPath(\n  rootComponent: ComponentDirectory,\n  component: ComponentDirectory,\n): ComponentDefinitionPath {\n  // First, compute a file system relative path.\n  const relativePath: string = path.relative(\n    rootComponent.path,\n    component.path,\n  );\n\n  // Then, convert it to a ComponentDefinitionPath, which always uses POSIX conventions.\n  const definitionPath = relativePath.split(path.sep).join(path.posix.sep);\n\n  return definitionPath as ComponentDefinitionPath;\n}\n\nexport function toAbsolutePath(\n  rootComponent: ComponentDirectory,\n  componentDefinitionPath: ComponentDefinitionPath,\n) {\n  // Repeat the process from `toComponentDefinitionPath` in reverse: First\n  // convert to a relative local filesystem path, and then join it to\n  // the root component's absolute path.\n  const relativePath = componentDefinitionPath\n    .split(path.posix.sep)\n    .join(path.sep);\n  return path.normalize(path.join(rootComponent.path, relativePath));\n}\n"], "mappings": ";AAAA,OAAO,UAAU;AAEjB;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,iCAAiC;AAuCnC,gBAAS,wBACd,WACA,aAAa,KACb;AACA,QAAM,iBAAiB,KAAK,SAAS,YAAY,UAAU,cAAc;AACzE,SAAO,KAAK,cAAc;AAC5B;AAGO,gBAAS,qBACd,KACA,WACA,QAG+B;AAC/B,MAAI,CAAC,IAAI,GAAG,OAAO,SAAS,GAAG;AAC7B,WAAO,EAAE,MAAM,OAAO,KAAK,0BAA0B;AAAA,EACvD;AACA,QAAM,UAAU,IAAI,GAAG,KAAK,SAAS;AACrC,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,WAAO,EAAE,MAAM,OAAO,KAAK,kBAAkB;AAAA,EAC/C;AAGA,MAAI,WAAW;AACf,MAAI,iBAAiB,KAAK,QAAQ,KAAK,KAAK,WAAW,QAAQ,CAAC;AAChE,MAAI,CAAC,IAAI,GAAG,OAAO,cAAc,GAAG;AAClC,eAAW;AACX,qBAAiB,KAAK,QAAQ,KAAK,KAAK,WAAW,QAAQ,CAAC;AAAA,EAC9D;AACA,MAAI,CAAC,IAAI,GAAG,OAAO,cAAc,GAAG;AAClC,WAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAK,+BAA+B,QAAQ;AAAA,IAC9C;AAAA,EACF;AACA,QAAM,iBAAiB,IAAI,GAAG,KAAK,cAAc;AACjD,MAAI,CAAC,eAAe,OAAO,GAAG;AAC5B,WAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAK,wBAAwB,QAAQ;AAAA,IACvC;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,MACT;AAAA,MACA,MAAM,KAAK,QAAQ,SAAS;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACF;AAEA,sBAAsB,wBACpB,KACA,gBAC6B;AAC7B,QAAM,YAAY,KAAK,QAAQ,MAAM,0BAA0B,GAAG,CAAC;AACnE,QAAM,SAAS,KAAK,QAAQ,KAAK,QAAQ,cAAc,CAAC,MAAM;AAC9D,QAAM,cAAc;AAAA,IAClB;AAAA,IACA,KAAK,QAAQ,cAAc;AAAA,IAC3B;AAAA,EACF;AACA,MAAI,YAAY,SAAS,OAAO;AAC9B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,gCAAgC,YAAY,GAAG,MAAM,KAAK,QAAQ,cAAc,CAAC;AAAA,IACnG,CAAC;AAAA,EACH;AACA,SAAO,YAAY;AACrB;AAgBO,gBAAS,0BACd,eACA,WACyB;AAEzB,QAAM,eAAuB,KAAK;AAAA,IAChC,cAAc;AAAA,IACd,UAAU;AAAA,EACZ;AAGA,QAAM,iBAAiB,aAAa,MAAM,KAAK,GAAG,EAAE,KAAK,KAAK,MAAM,GAAG;AAEvE,SAAO;AACT;AAEO,gBAAS,eACd,eACA,yBACA;AAIA,QAAM,eAAe,wBAClB,MAAM,KAAK,MAAM,GAAG,EACpB,KAAK,KAAK,GAAG;AAChB,SAAO,KAAK,UAAU,KAAK,KAAK,cAAc,MAAM,YAAY,CAAC;AACnE;", "names": []}