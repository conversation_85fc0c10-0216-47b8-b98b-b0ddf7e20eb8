{"version": 3, "sources": ["../../../../src/cli/lib/convexExport.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport {\n  waitUntilCalled,\n  deploymentFetch,\n  logAndHandleFetchError,\n} from \"./utils/utils.js\";\nimport {\n  logFailure,\n  Context,\n  showSpinner,\n  logFinishedStep,\n  logError,\n  stopSpinner,\n  changeSpinner,\n} from \"../../bundler/context.js\";\nimport { subscribe } from \"./run.js\";\nimport { nodeFs } from \"../../bundler/fs.js\";\nimport path from \"path\";\nimport { Readable } from \"stream\";\n\nexport async function exportFromDeployment(\n  ctx: Context,\n  options: {\n    deploymentUrl: string;\n    adminKey: string;\n    path: string;\n    includeFileStorage?: boolean;\n    deploymentNotice: string;\n    snapshotExportDashboardLink: string | undefined;\n  },\n) {\n  const includeStorage = !!options.includeFileStorage;\n  const {\n    deploymentUrl,\n    adminKey,\n    path: inputPath,\n    deploymentNotice,\n    snapshotExportDashboardLink,\n  } = options;\n\n  showSpinner(ctx, `Creating snapshot export${deploymentNotice}`);\n\n  const snapshotExportState = await startSnapshotExport(ctx, {\n    includeStorage,\n    inputPath,\n    adminKey,\n    deploymentUrl,\n  });\n\n  switch (snapshotExportState.state) {\n    case \"completed\":\n      stopSpinner(ctx);\n      logFinishedStep(\n        ctx,\n        `Created snapshot export at timestamp ${snapshotExportState.start_ts}`,\n      );\n      if (snapshotExportDashboardLink !== undefined) {\n        logFinishedStep(\n          ctx,\n          `Export is available at ${snapshotExportDashboardLink}`,\n        );\n      }\n      break;\n    case \"requested\":\n    case \"in_progress\": {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `WARNING: Export is continuing to run on the server.`,\n      });\n    }\n    default: {\n      const _: never = snapshotExportState;\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `unknown error: unexpected state ${snapshotExportState as any}`,\n        errForSentry: `unexpected snapshot export state ${(snapshotExportState as any).state}`,\n      });\n    }\n  }\n\n  showSpinner(ctx, `Downloading snapshot export to ${chalk.bold(inputPath)}`);\n  const { filePath } = await downloadSnapshotExport(ctx, {\n    snapshotExportTs: snapshotExportState.start_ts,\n    inputPath,\n    adminKey,\n    deploymentUrl,\n  });\n  stopSpinner(ctx);\n  logFinishedStep(ctx, `Downloaded snapshot export to ${chalk.bold(filePath)}`);\n}\n\ntype SnapshotExportState =\n  | { state: \"requested\" }\n  | { state: \"in_progress\" }\n  | {\n      state: \"completed\";\n      complete_ts: bigint;\n      start_ts: bigint;\n      zip_object_key: string;\n    };\n\nasync function waitForStableExportState(\n  ctx: Context,\n  deploymentUrl: string,\n  adminKey: string,\n): Promise<SnapshotExportState> {\n  const [donePromise, onDone] = waitUntilCalled();\n  let snapshotExportState: SnapshotExportState;\n  await subscribe(ctx, {\n    deploymentUrl,\n    adminKey,\n    parsedFunctionName: \"_system/cli/exports:getLatest\",\n    parsedFunctionArgs: {},\n    componentPath: undefined,\n    until: donePromise,\n    callbacks: {\n      onChange: (value: any) => {\n        // NOTE: `value` would only be `null` if there has never been an export\n        // requested.\n        snapshotExportState = value;\n        switch (snapshotExportState.state) {\n          case \"requested\":\n          case \"in_progress\":\n            // Not a stable state.\n            break;\n          case \"completed\":\n            onDone();\n            break;\n          default: {\n            const _: never = snapshotExportState;\n            onDone();\n          }\n        }\n      },\n    },\n  });\n  return snapshotExportState!;\n}\n\nexport async function startSnapshotExport(\n  ctx: Context,\n  args: {\n    includeStorage: boolean;\n    inputPath: string;\n    adminKey: string;\n    deploymentUrl: string;\n  },\n) {\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: args.deploymentUrl,\n    adminKey: args.adminKey,\n  });\n  try {\n    await fetch(\n      `/api/export/request/zip?includeStorage=${args.includeStorage}`,\n      {\n        method: \"POST\",\n      },\n    );\n  } catch (e) {\n    return await logAndHandleFetchError(ctx, e);\n  }\n\n  const snapshotExportState = await waitForStableExportState(\n    ctx,\n    args.deploymentUrl,\n    args.adminKey,\n  );\n  return snapshotExportState;\n}\n\nexport async function downloadSnapshotExport(\n  ctx: Context,\n  args: {\n    snapshotExportTs: bigint;\n    inputPath: string;\n    adminKey: string;\n    deploymentUrl: string;\n  },\n): Promise<{ filePath: string }> {\n  const inputPath = args.inputPath;\n  const exportUrl = `/api/export/zip/${args.snapshotExportTs.toString()}`;\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: args.deploymentUrl,\n    adminKey: args.adminKey,\n  });\n  let response: Response;\n  try {\n    response = await fetch(exportUrl, {\n      method: \"GET\",\n    });\n  } catch (e) {\n    return await logAndHandleFetchError(ctx, e);\n  }\n\n  let filePath;\n  if (ctx.fs.exists(inputPath)) {\n    const st = ctx.fs.stat(inputPath);\n    if (st.isDirectory()) {\n      const contentDisposition =\n        response.headers.get(\"content-disposition\") ?? \"\";\n      let filename = `snapshot_${args.snapshotExportTs.toString()}.zip`;\n      if (contentDisposition.startsWith(\"attachment; filename=\")) {\n        filename = contentDisposition.slice(\"attachment; filename=\".length);\n      }\n      filePath = path.join(inputPath, filename);\n    } else {\n      // TODO(sarah) -- if this is called elsewhere, I'd like to catch the error + potentially\n      // have different logging\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"invalid filesystem data\",\n        printedMessage: `Error: Path ${chalk.bold(inputPath)} already exists.`,\n      });\n    }\n  } else {\n    filePath = inputPath;\n  }\n  changeSpinner(ctx, `Downloading snapshot export to ${chalk.bold(filePath)}`);\n\n  try {\n    await nodeFs.writeFileStream(\n      filePath,\n      Readable.fromWeb(response.body! as any),\n    );\n  } catch (e) {\n    logFailure(ctx, `Exporting data failed`);\n    logError(ctx, chalk.red(e));\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Exporting data failed: ${chalk.red(e)}`,\n    });\n  }\n  return { filePath };\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,iBAAiB;AAC1B,SAAS,cAAc;AACvB,OAAO,UAAU;AACjB,SAAS,gBAAgB;AAEzB,sBAAsB,qBACpB,KACA,SAQA;AACA,QAAM,iBAAiB,CAAC,CAAC,QAAQ;AACjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,cAAY,KAAK,2BAA2B,gBAAgB,EAAE;AAE9D,QAAM,sBAAsB,MAAM,oBAAoB,KAAK;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,UAAQ,oBAAoB,OAAO;AAAA,IACjC,KAAK;AACH,kBAAY,GAAG;AACf;AAAA,QACE;AAAA,QACA,wCAAwC,oBAAoB,QAAQ;AAAA,MACtE;AACA,UAAI,gCAAgC,QAAW;AAC7C;AAAA,UACE;AAAA,UACA,0BAA0B,2BAA2B;AAAA,QACvD;AAAA,MACF;AACA;AAAA,IACF,KAAK;AAAA,IACL,KAAK,eAAe;AAClB,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,IACA,SAAS;AACP,YAAM,IAAW;AACjB,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,mCAAmC,mBAA0B;AAAA,QAC7E,cAAc,oCAAqC,oBAA4B,KAAK;AAAA,MACtF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,cAAY,KAAK,kCAAkC,MAAM,KAAK,SAAS,CAAC,EAAE;AAC1E,QAAM,EAAE,SAAS,IAAI,MAAM,uBAAuB,KAAK;AAAA,IACrD,kBAAkB,oBAAoB;AAAA,IACtC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,cAAY,GAAG;AACf,kBAAgB,KAAK,iCAAiC,MAAM,KAAK,QAAQ,CAAC,EAAE;AAC9E;AAYA,eAAe,yBACb,KACA,eACA,UAC8B;AAC9B,QAAM,CAAC,aAAa,MAAM,IAAI,gBAAgB;AAC9C,MAAI;AACJ,QAAM,UAAU,KAAK;AAAA,IACnB;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB,oBAAoB,CAAC;AAAA,IACrB,eAAe;AAAA,IACf,OAAO;AAAA,IACP,WAAW;AAAA,MACT,UAAU,CAAC,UAAe;AAGxB,8BAAsB;AACtB,gBAAQ,oBAAoB,OAAO;AAAA,UACjC,KAAK;AAAA,UACL,KAAK;AAEH;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,SAAS;AACP,kBAAM,IAAW;AACjB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,sBAAsB,oBACpB,KACA,MAMA;AACA,QAAM,QAAQ,gBAAgB,KAAK;AAAA,IACjC,eAAe,KAAK;AAAA,IACpB,UAAU,KAAK;AAAA,EACjB,CAAC;AACD,MAAI;AACF,UAAM;AAAA,MACJ,0CAA0C,KAAK,cAAc;AAAA,MAC7D;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,WAAO,MAAM,uBAAuB,KAAK,CAAC;AAAA,EAC5C;AAEA,QAAM,sBAAsB,MAAM;AAAA,IAChC;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACA,SAAO;AACT;AAEA,sBAAsB,uBACpB,KACA,MAM+B;AAC/B,QAAM,YAAY,KAAK;AACvB,QAAM,YAAY,mBAAmB,KAAK,iBAAiB,SAAS,CAAC;AACrE,QAAM,QAAQ,gBAAgB,KAAK;AAAA,IACjC,eAAe,KAAK;AAAA,IACpB,UAAU,KAAK;AAAA,EACjB,CAAC;AACD,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,MAAM,WAAW;AAAA,MAChC,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,SAAS,GAAG;AACV,WAAO,MAAM,uBAAuB,KAAK,CAAC;AAAA,EAC5C;AAEA,MAAI;AACJ,MAAI,IAAI,GAAG,OAAO,SAAS,GAAG;AAC5B,UAAM,KAAK,IAAI,GAAG,KAAK,SAAS;AAChC,QAAI,GAAG,YAAY,GAAG;AACpB,YAAM,qBACJ,SAAS,QAAQ,IAAI,qBAAqB,KAAK;AACjD,UAAI,WAAW,YAAY,KAAK,iBAAiB,SAAS,CAAC;AAC3D,UAAI,mBAAmB,WAAW,uBAAuB,GAAG;AAC1D,mBAAW,mBAAmB,MAAM,wBAAwB,MAAM;AAAA,MACpE;AACA,iBAAW,KAAK,KAAK,WAAW,QAAQ;AAAA,IAC1C,OAAO;AAGL,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,eAAe,MAAM,KAAK,SAAS,CAAC;AAAA,MACtD,CAAC;AAAA,IACH;AAAA,EACF,OAAO;AACL,eAAW;AAAA,EACb;AACA,gBAAc,KAAK,kCAAkC,MAAM,KAAK,QAAQ,CAAC,EAAE;AAE3E,MAAI;AACF,UAAM,OAAO;AAAA,MACX;AAAA,MACA,SAAS,QAAQ,SAAS,IAAY;AAAA,IACxC;AAAA,EACF,SAAS,GAAG;AACV,eAAW,KAAK,uBAAuB;AACvC,aAAS,KAAK,MAAM,IAAI,CAAC,CAAC;AAC1B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,0BAA0B,MAAM,IAAI,CAAC,CAAC;AAAA,IACxD,CAAC;AAAA,EACH;AACA,SAAO,EAAE,SAAS;AACpB;", "names": []}