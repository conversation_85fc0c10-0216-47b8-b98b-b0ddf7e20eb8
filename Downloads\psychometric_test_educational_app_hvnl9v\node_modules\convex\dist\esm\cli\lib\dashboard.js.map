{"version": 3, "sources": ["../../../../src/cli/lib/dashboard.ts"], "sourcesContent": ["import { Context } from \"../../bundler/context.js\";\nimport { DeploymentType } from \"./api.js\";\nimport { dashboardUrl as localDashboardUrl } from \"./localDeployment/dashboard.js\";\n\nexport const DASHBOARD_HOST = process.env.CONVEX_PROVISION_HOST\n  ? \"http://localhost:6789\"\n  : \"https://dashboard.convex.dev\";\n\nexport function getDashboardUrl(\n  ctx: Context,\n  {\n    deploymentName,\n    deploymentType,\n  }: {\n    deploymentName: string;\n    deploymentType: DeploymentType;\n  },\n): string | null {\n  switch (deploymentType) {\n    case \"anonymous\": {\n      return localDashboardUrl(ctx, deploymentName);\n    }\n    case \"local\":\n    case \"dev\":\n    case \"prod\":\n    case \"preview\":\n      return deploymentDashboardUrlPage(deploymentName, \"\");\n    default: {\n      const _exhaustiveCheck: never = deploymentType;\n      return _exhaustiveCheck;\n    }\n  }\n}\n\nexport function deploymentDashboardUrlPage(\n  configuredDeployment: string | null,\n  page: string,\n): string {\n  return `${DASHBOARD_HOST}/d/${configuredDeployment}${page}`;\n}\n\nexport function deploymentDashboardUrl(\n  team: string,\n  project: string,\n  deploymentName: string,\n) {\n  return `${projectDashboardUrl(team, project)}/${deploymentName}`;\n}\n\nexport function projectDashboardUrl(team: string, project: string) {\n  return `${teamDashboardUrl(team)}/${project}`;\n}\n\nexport function teamDashboardUrl(team: string) {\n  return `${DASHBOARD_HOST}/t/${team}`;\n}\n"], "mappings": ";AAEA,SAAS,gBAAgB,yBAAyB;AAE3C,aAAM,iBAAiB,QAAQ,IAAI,wBACtC,0BACA;AAEG,gBAAS,gBACd,KACA;AAAA,EACE;AAAA,EACA;AACF,GAIe;AACf,UAAQ,gBAAgB;AAAA,IACtB,KAAK,aAAa;AAChB,aAAO,kBAAkB,KAAK,cAAc;AAAA,IAC9C;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,2BAA2B,gBAAgB,EAAE;AAAA,IACtD,SAAS;AACP,YAAM,mBAA0B;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEO,gBAAS,2BACd,sBACA,MACQ;AACR,SAAO,GAAG,cAAc,MAAM,oBAAoB,GAAG,IAAI;AAC3D;AAEO,gBAAS,uBACd,MACA,SACA,gBACA;AACA,SAAO,GAAG,oBAAoB,MAAM,OAAO,CAAC,IAAI,cAAc;AAChE;AAEO,gBAAS,oBAAoB,MAAc,SAAiB;AACjE,SAAO,GAAG,iBAAiB,IAAI,CAAC,IAAI,OAAO;AAC7C;AAEO,gBAAS,iBAAiB,MAAc;AAC7C,SAAO,GAAG,cAAc,MAAM,IAAI;AACpC;", "names": []}