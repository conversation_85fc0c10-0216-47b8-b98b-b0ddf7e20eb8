{"version": 3, "sources": ["../../../../src/cli/lib/data.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport {\n  Context,\n  logError,\n  logOutput,\n  logWarning,\n} from \"../../bundler/context.js\";\nimport { Base64 } from \"../../values/index.js\";\nimport { Value } from \"../../values/value.js\";\nimport { runSystemPaginatedQuery } from \"./run.js\";\n\nexport async function dataInDeployment(\n  ctx: Context,\n  options: {\n    deploymentUrl: string;\n    adminKey: string;\n    deploymentNotice: string;\n    tableName?: string;\n    limit: number;\n    order: \"asc\" | \"desc\";\n    component?: string;\n  },\n) {\n  if (options.tableName !== undefined) {\n    await listDocuments(\n      ctx,\n      options.deploymentUrl,\n      options.adminKey,\n      options.tableName,\n      {\n        limit: options.limit,\n        order: options.order as \"asc\" | \"desc\",\n        componentPath: options.component ?? \"\",\n      },\n    );\n  } else {\n    await listTables(\n      ctx,\n      options.deploymentUrl,\n      options.adminKey,\n      options.deploymentNotice,\n      options.component ?? \"\",\n    );\n  }\n}\n\nasync function listTables(\n  ctx: Context,\n  deploymentUrl: string,\n  adminKey: string,\n  deploymentNotice: string,\n  componentPath: string,\n) {\n  const tables = (await runSystemPaginatedQuery(ctx, {\n    deploymentUrl,\n    adminKey,\n    functionName: \"_system/cli/tables\",\n    componentPath,\n    args: {},\n  })) as { name: string }[];\n  if (tables.length === 0) {\n    logError(ctx, `There are no tables in the ${deploymentNotice}database.`);\n    return;\n  }\n  const tableNames = tables.map((table) => table.name);\n  tableNames.sort();\n  logOutput(ctx, tableNames.join(\"\\n\"));\n}\n\nasync function listDocuments(\n  ctx: Context,\n  deploymentUrl: string,\n  adminKey: string,\n  tableName: string,\n  options: {\n    limit: number;\n    order: \"asc\" | \"desc\";\n    componentPath: string;\n  },\n) {\n  const data = (await runSystemPaginatedQuery(ctx, {\n    deploymentUrl,\n    adminKey,\n    functionName: \"_system/cli/tableData\",\n    componentPath: options.componentPath,\n    args: {\n      table: tableName,\n      order: options.order ?? \"desc\",\n    },\n    limit: options.limit + 1,\n  })) as Record<string, Value>[];\n\n  if (data.length === 0) {\n    logError(ctx, \"There are no documents in this table.\");\n    return;\n  }\n\n  logDocumentsTable(\n    ctx,\n    data.slice(0, options.limit).map((document) => {\n      const printed: Record<string, string> = {};\n      for (const key in document) {\n        printed[key] = stringify(document[key]);\n      }\n      return printed;\n    }),\n  );\n  if (data.length > options.limit) {\n    logWarning(\n      ctx,\n      chalk.yellow(\n        `Showing the ${options.limit} ${\n          options.order === \"desc\" ? \"most recently\" : \"oldest\"\n        } created document${\n          options.limit > 1 ? \"s\" : \"\"\n        }. Use the --limit option to see more.`,\n      ),\n    );\n  }\n}\n\nfunction logDocumentsTable(ctx: Context, rows: Record<string, string>[]) {\n  const columnsToWidths: Record<string, number> = {};\n  for (const row of rows) {\n    for (const column in row) {\n      const value = row[column];\n      columnsToWidths[column] = Math.max(\n        value.length,\n        columnsToWidths[column] ?? 0,\n      );\n    }\n  }\n  const unsortedFields = Object.keys(columnsToWidths);\n  unsortedFields.sort();\n  const fields = Array.from(\n    new Set([\"_id\", \"_creationTime\", ...unsortedFields]),\n  );\n  const columnWidths = fields.map((field) => columnsToWidths[field]);\n  const lineLimit = process.stdout.isTTY ? process.stdout.columns : undefined;\n\n  let didTruncate = false;\n\n  function limitLine(line: string, limit: number | undefined) {\n    if (limit === undefined) {\n      return line;\n    }\n    const limitWithBufferForUnicode = limit - 10;\n    if (line.length > limitWithBufferForUnicode) {\n      didTruncate = true;\n    }\n    return line.slice(0, limitWithBufferForUnicode);\n  }\n\n  logOutput(\n    ctx,\n    limitLine(\n      fields.map((field, i) => field.padEnd(columnWidths[i])).join(\" | \"),\n      lineLimit,\n    ),\n  );\n  logOutput(\n    ctx,\n    limitLine(\n      columnWidths.map((width) => \"-\".repeat(width)).join(\"-|-\"),\n      lineLimit,\n    ),\n  );\n  for (const row of rows) {\n    logOutput(\n      ctx,\n      limitLine(\n        fields\n          .map((field, i) => (row[field] ?? \"\").padEnd(columnWidths[i]))\n          .join(\" | \"),\n        lineLimit,\n      ),\n    );\n  }\n  if (didTruncate) {\n    logWarning(\n      ctx,\n      chalk.yellow(\n        \"Lines were truncated to fit the terminal width. Pipe the command to see \" +\n          \"the full output, such as:\\n  `npx convex data tableName | less -S`\",\n      ),\n    );\n  }\n}\n\nfunction stringify(value: Value): string {\n  if (value === null) {\n    return \"null\";\n  }\n  if (typeof value === \"bigint\") {\n    return `${value.toString()}n`;\n  }\n  if (typeof value === \"number\") {\n    return value.toString();\n  }\n  if (typeof value === \"boolean\") {\n    return value.toString();\n  }\n  if (typeof value === \"string\") {\n    return JSON.stringify(value);\n  }\n  if (value instanceof ArrayBuffer) {\n    const base64Encoded = Base64.fromByteArray(new Uint8Array(value));\n    return `Bytes(\"${base64Encoded}\")`;\n  }\n  if (value instanceof Array) {\n    return `[${value.map(stringify).join(\", \")}]`;\n  }\n  const pairs = Object.entries(value)\n    .map(([k, v]) => `\"${k}\": ${stringify(v!)}`)\n    .join(\", \");\n  return `{ ${pairs} }`;\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,cAAc;AAEvB,SAAS,+BAA+B;AAExC,sBAAsB,iBACpB,KACA,SASA;AACA,MAAI,QAAQ,cAAc,QAAW;AACnC,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR;AAAA,QACE,OAAO,QAAQ;AAAA,QACf,OAAO,QAAQ;AAAA,QACf,eAAe,QAAQ,aAAa;AAAA,MACtC;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ,aAAa;AAAA,IACvB;AAAA,EACF;AACF;AAEA,eAAe,WACb,KACA,eACA,UACA,kBACA,eACA;AACA,QAAM,SAAU,MAAM,wBAAwB,KAAK;AAAA,IACjD;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA,MAAM,CAAC;AAAA,EACT,CAAC;AACD,MAAI,OAAO,WAAW,GAAG;AACvB,aAAS,KAAK,8BAA8B,gBAAgB,WAAW;AACvE;AAAA,EACF;AACA,QAAM,aAAa,OAAO,IAAI,CAAC,UAAU,MAAM,IAAI;AACnD,aAAW,KAAK;AAChB,YAAU,KAAK,WAAW,KAAK,IAAI,CAAC;AACtC;AAEA,eAAe,cACb,KACA,eACA,UACA,WACA,SAKA;AACA,QAAM,OAAQ,MAAM,wBAAwB,KAAK;AAAA,IAC/C;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,eAAe,QAAQ;AAAA,IACvB,MAAM;AAAA,MACJ,OAAO;AAAA,MACP,OAAO,QAAQ,SAAS;AAAA,IAC1B;AAAA,IACA,OAAO,QAAQ,QAAQ;AAAA,EACzB,CAAC;AAED,MAAI,KAAK,WAAW,GAAG;AACrB,aAAS,KAAK,uCAAuC;AACrD;AAAA,EACF;AAEA;AAAA,IACE;AAAA,IACA,KAAK,MAAM,GAAG,QAAQ,KAAK,EAAE,IAAI,CAAC,aAAa;AAC7C,YAAM,UAAkC,CAAC;AACzC,iBAAW,OAAO,UAAU;AAC1B,gBAAQ,GAAG,IAAI,UAAU,SAAS,GAAG,CAAC;AAAA,MACxC;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,KAAK,SAAS,QAAQ,OAAO;AAC/B;AAAA,MACE;AAAA,MACA,MAAM;AAAA,QACJ,eAAe,QAAQ,KAAK,IAC1B,QAAQ,UAAU,SAAS,kBAAkB,QAC/C,oBACE,QAAQ,QAAQ,IAAI,MAAM,EAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,KAAc,MAAgC;AACvE,QAAM,kBAA0C,CAAC;AACjD,aAAW,OAAO,MAAM;AACtB,eAAW,UAAU,KAAK;AACxB,YAAM,QAAQ,IAAI,MAAM;AACxB,sBAAgB,MAAM,IAAI,KAAK;AAAA,QAC7B,MAAM;AAAA,QACN,gBAAgB,MAAM,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,OAAO,KAAK,eAAe;AAClD,iBAAe,KAAK;AACpB,QAAM,SAAS,MAAM;AAAA,IACnB,oBAAI,IAAI,CAAC,OAAO,iBAAiB,GAAG,cAAc,CAAC;AAAA,EACrD;AACA,QAAM,eAAe,OAAO,IAAI,CAAC,UAAU,gBAAgB,KAAK,CAAC;AACjE,QAAM,YAAY,QAAQ,OAAO,QAAQ,QAAQ,OAAO,UAAU;AAElE,MAAI,cAAc;AAElB,WAAS,UAAU,MAAc,OAA2B;AAC1D,QAAI,UAAU,QAAW;AACvB,aAAO;AAAA,IACT;AACA,UAAM,4BAA4B,QAAQ;AAC1C,QAAI,KAAK,SAAS,2BAA2B;AAC3C,oBAAc;AAAA,IAChB;AACA,WAAO,KAAK,MAAM,GAAG,yBAAyB;AAAA,EAChD;AAEA;AAAA,IACE;AAAA,IACA;AAAA,MACE,OAAO,IAAI,CAAC,OAAO,MAAM,MAAM,OAAO,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,MACE,aAAa,IAAI,CAAC,UAAU,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,KAAK;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AACA,aAAW,OAAO,MAAM;AACtB;AAAA,MACE;AAAA,MACA;AAAA,QACE,OACG,IAAI,CAAC,OAAO,OAAO,IAAI,KAAK,KAAK,IAAI,OAAO,aAAa,CAAC,CAAC,CAAC,EAC5D,KAAK,KAAK;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa;AACf;AAAA,MACE;AAAA,MACA,MAAM;AAAA,QACJ;AAAA,MAEF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,UAAU,OAAsB;AACvC,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,GAAG,MAAM,SAAS,CAAC;AAAA,EAC5B;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,MAAM,SAAS;AAAA,EACxB;AACA,MAAI,OAAO,UAAU,WAAW;AAC9B,WAAO,MAAM,SAAS;AAAA,EACxB;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,KAAK,UAAU,KAAK;AAAA,EAC7B;AACA,MAAI,iBAAiB,aAAa;AAChC,UAAM,gBAAgB,OAAO,cAAc,IAAI,WAAW,KAAK,CAAC;AAChE,WAAO,UAAU,aAAa;AAAA,EAChC;AACA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,IAAI,MAAM,IAAI,SAAS,EAAE,KAAK,IAAI,CAAC;AAAA,EAC5C;AACA,QAAM,QAAQ,OAAO,QAAQ,KAAK,EAC/B,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,UAAU,CAAE,CAAC,EAAE,EAC1C,KAAK,IAAI;AACZ,SAAO,KAAK,KAAK;AACnB;", "names": []}