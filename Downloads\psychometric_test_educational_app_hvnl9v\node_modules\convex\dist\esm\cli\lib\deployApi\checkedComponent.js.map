{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/checkedComponent.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport {\n  componentDefinitionPath,\n  componentFunctionPath,\n  ComponentDefinitionPath,\n  ComponentPath,\n  componentPath,\n} from \"./paths.js\";\nimport { Identifier, identifier } from \"./types.js\";\nimport { looseObject } from \"./utils.js\";\n\nexport const resource = z.union([\n  looseObject({ type: z.literal(\"value\"), value: z.string() }),\n  looseObject({\n    type: z.literal(\"function\"),\n    path: componentFunctionPath,\n  }),\n]);\nexport type Resource = z.infer<typeof resource>;\n\nexport type CheckedExport =\n  | { type: \"branch\"; children: Record<Identifier, CheckedExport> }\n  | { type: \"leaf\"; resource: Resource };\nexport const checkedExport: z.ZodType<CheckedExport> = z.lazy(() =>\n  z.union([\n    looseObject({\n      type: z.literal(\"branch\"),\n      children: z.record(identifier, checkedExport),\n    }),\n    looseObject({\n      type: z.literal(\"leaf\"),\n      resource,\n    }),\n  ]),\n);\n\nexport const httpActionRoute = looseObject({\n  method: z.string(),\n  path: z.string(),\n});\n\nexport const checkedHttpRoutes = looseObject({\n  httpModuleRoutes: z.nullable(z.array(httpActionRoute)),\n  mounts: z.array(z.string()),\n});\nexport type CheckedHttpRoutes = z.infer<typeof checkedHttpRoutes>;\n\nexport type CheckedComponent = {\n  definitionPath: ComponentDefinitionPath;\n  componentPath: ComponentPath;\n  args: Record<Identifier, Resource>;\n  childComponents: Record<Identifier, CheckedComponent>;\n};\nexport const checkedComponent: z.ZodType<CheckedComponent> = z.lazy(() =>\n  looseObject({\n    definitionPath: componentDefinitionPath,\n    componentPath,\n    args: z.record(identifier, resource),\n    childComponents: z.record(identifier, checkedComponent),\n    httpRoutes: checkedHttpRoutes,\n    exports: z.record(identifier, checkedExport),\n  }),\n);\n"], "mappings": ";AAAA,SAAS,SAAS;AAClB;AAAA,EACE;AAAA,EACA;AAAA,EAGA;AAAA,OACK;AACP,SAAqB,kBAAkB;AACvC,SAAS,mBAAmB;AAErB,aAAM,WAAW,EAAE,MAAM;AAAA,EAC9B,YAAY,EAAE,MAAM,EAAE,QAAQ,OAAO,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC;AAAA,EAC3D,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,UAAU;AAAA,IAC1B,MAAM;AAAA,EACR,CAAC;AACH,CAAC;AAMM,aAAM,gBAA0C,EAAE;AAAA,EAAK,MAC5D,EAAE,MAAM;AAAA,IACN,YAAY;AAAA,MACV,MAAM,EAAE,QAAQ,QAAQ;AAAA,MACxB,UAAU,EAAE,OAAO,YAAY,aAAa;AAAA,IAC9C,CAAC;AAAA,IACD,YAAY;AAAA,MACV,MAAM,EAAE,QAAQ,MAAM;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEO,aAAM,kBAAkB,YAAY;AAAA,EACzC,QAAQ,EAAE,OAAO;AAAA,EACjB,MAAM,EAAE,OAAO;AACjB,CAAC;AAEM,aAAM,oBAAoB,YAAY;AAAA,EAC3C,kBAAkB,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAAA,EACrD,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5B,CAAC;AASM,aAAM,mBAAgD,EAAE;AAAA,EAAK,MAClE,YAAY;AAAA,IACV,gBAAgB;AAAA,IAChB;AAAA,IACA,MAAM,EAAE,OAAO,YAAY,QAAQ;AAAA,IACnC,iBAAiB,EAAE,OAAO,YAAY,gBAAgB;AAAA,IACtD,YAAY;AAAA,IACZ,SAAS,EAAE,OAAO,YAAY,aAAa;AAAA,EAC7C,CAAC;AACH;", "names": []}