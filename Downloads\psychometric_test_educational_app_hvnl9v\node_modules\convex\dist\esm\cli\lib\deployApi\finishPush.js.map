{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/finishPush.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { looseObject } from \"./utils.js\";\n\nexport const authDiff = looseObject({\n  added: z.array(z.string()),\n  removed: z.array(z.string()),\n});\nexport type AuthDiff = z.infer<typeof authDiff>;\n\nexport const componentDefinitionDiff = looseObject({});\nexport type ComponentDefinitionDiff = z.infer<typeof componentDefinitionDiff>;\n\nexport const componentDiffType = z.discriminatedUnion(\"type\", [\n  looseObject({\n    type: z.literal(\"create\"),\n  }),\n  looseObject({\n    type: z.literal(\"modify\"),\n  }),\n  looseObject({\n    type: z.literal(\"unmount\"),\n  }),\n  looseObject({\n    type: z.literal(\"remount\"),\n  }),\n]);\nexport type ComponentDiffType = z.infer<typeof componentDiffType>;\n\nexport const moduleDiff = looseObject({\n  added: z.array(z.string()),\n  removed: z.array(z.string()),\n});\nexport type ModuleDiff = z.infer<typeof moduleDiff>;\n\nexport const udfConfigDiff = looseObject({\n  previous_version: z.string(),\n  next_version: z.string(),\n});\nexport type UdfConfigDiff = z.infer<typeof udfConfigDiff>;\n\nexport const cronDiff = looseObject({\n  added: z.array(z.string()),\n  updated: z.array(z.string()),\n  deleted: z.array(z.string()),\n});\nexport type CronDiff = z.infer<typeof cronDiff>;\n\nconst developerIndexConfig = z.discriminatedUnion(\"type\", [\n  looseObject({\n    name: z.string(),\n    type: z.literal(\"database\"),\n    fields: z.array(z.string()),\n  }),\n  looseObject({\n    name: z.string(),\n    type: z.literal(\"search\"),\n    searchField: z.string(),\n    filterFields: z.array(z.string()),\n  }),\n  looseObject({\n    name: z.string(),\n    type: z.literal(\"vector\"),\n    dimensions: z.number(),\n    vectorField: z.string(),\n    filterFields: z.array(z.string()),\n  }),\n]);\nexport type DeveloperIndexConfig = z.infer<typeof developerIndexConfig>;\n\nexport const indexDiff = looseObject({\n  added_indexes: z.array(developerIndexConfig),\n  removed_indexes: z.array(developerIndexConfig),\n});\nexport type IndexDiff = z.infer<typeof indexDiff>;\n\nexport const schemaDiff = looseObject({\n  previous_schema: z.nullable(z.string()),\n  next_schema: z.nullable(z.string()),\n});\nexport type SchemaDiff = z.infer<typeof schemaDiff>;\n\nexport const componentDiff = looseObject({\n  diffType: componentDiffType,\n  moduleDiff,\n  udfConfigDiff: z.nullable(udfConfigDiff),\n  cronDiff,\n  indexDiff,\n  schemaDiff: z.nullable(schemaDiff),\n});\nexport type ComponentDiff = z.infer<typeof componentDiff>;\n\nexport const finishPushDiff = looseObject({\n  authDiff,\n  definitionDiffs: z.record(z.string(), componentDefinitionDiff),\n  componentDiffs: z.record(z.string(), componentDiff),\n});\nexport type FinishPushDiff = z.infer<typeof finishPushDiff>;\n"], "mappings": ";AAAA,SAAS,SAAS;AAClB,SAAS,mBAAmB;AAErB,aAAM,WAAW,YAAY;AAAA,EAClC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EACzB,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AAC7B,CAAC;AAGM,aAAM,0BAA0B,YAAY,CAAC,CAAC;AAG9C,aAAM,oBAAoB,EAAE,mBAAmB,QAAQ;AAAA,EAC5D,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,QAAQ;AAAA,EAC1B,CAAC;AAAA,EACD,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,QAAQ;AAAA,EAC1B,CAAC;AAAA,EACD,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,SAAS;AAAA,EAC3B,CAAC;AAAA,EACD,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,SAAS;AAAA,EAC3B,CAAC;AACH,CAAC;AAGM,aAAM,aAAa,YAAY;AAAA,EACpC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EACzB,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AAC7B,CAAC;AAGM,aAAM,gBAAgB,YAAY;AAAA,EACvC,kBAAkB,EAAE,OAAO;AAAA,EAC3B,cAAc,EAAE,OAAO;AACzB,CAAC;AAGM,aAAM,WAAW,YAAY;AAAA,EAClC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EACzB,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EAC3B,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AAC7B,CAAC;AAGD,MAAM,uBAAuB,EAAE,mBAAmB,QAAQ;AAAA,EACxD,YAAY;AAAA,IACV,MAAM,EAAE,OAAO;AAAA,IACf,MAAM,EAAE,QAAQ,UAAU;AAAA,IAC1B,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EAC5B,CAAC;AAAA,EACD,YAAY;AAAA,IACV,MAAM,EAAE,OAAO;AAAA,IACf,MAAM,EAAE,QAAQ,QAAQ;AAAA,IACxB,aAAa,EAAE,OAAO;AAAA,IACtB,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EAClC,CAAC;AAAA,EACD,YAAY;AAAA,IACV,MAAM,EAAE,OAAO;AAAA,IACf,MAAM,EAAE,QAAQ,QAAQ;AAAA,IACxB,YAAY,EAAE,OAAO;AAAA,IACrB,aAAa,EAAE,OAAO;AAAA,IACtB,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EAClC,CAAC;AACH,CAAC;AAGM,aAAM,YAAY,YAAY;AAAA,EACnC,eAAe,EAAE,MAAM,oBAAoB;AAAA,EAC3C,iBAAiB,EAAE,MAAM,oBAAoB;AAC/C,CAAC;AAGM,aAAM,aAAa,YAAY;AAAA,EACpC,iBAAiB,EAAE,SAAS,EAAE,OAAO,CAAC;AAAA,EACtC,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC;AACpC,CAAC;AAGM,aAAM,gBAAgB,YAAY;AAAA,EACvC,UAAU;AAAA,EACV;AAAA,EACA,eAAe,EAAE,SAAS,aAAa;AAAA,EACvC;AAAA,EACA;AAAA,EACA,YAAY,EAAE,SAAS,UAAU;AACnC,CAAC;AAGM,aAAM,iBAAiB,YAAY;AAAA,EACxC;AAAA,EACA,iBAAiB,EAAE,OAAO,EAAE,OAAO,GAAG,uBAAuB;AAAA,EAC7D,gBAAgB,EAAE,OAAO,EAAE,OAAO,GAAG,aAAa;AACpD,CAAC;", "names": []}