{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/modules.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { looseObject } from \"./utils.js\";\n\nexport const moduleEnvironment = z.union([\n  z.literal(\"isolate\"),\n  z.literal(\"node\"),\n]);\nexport type ModuleEnvironment = z.infer<typeof moduleEnvironment>;\n\nexport const moduleConfig = looseObject({\n  path: z.string(),\n  source: z.string(),\n  sourceMap: z.optional(z.string()),\n  environment: moduleEnvironment,\n});\nexport type ModuleConfig = z.infer<typeof moduleConfig>;\n\nexport const nodeDependency = looseObject({\n  name: z.string(),\n  version: z.string(),\n});\nexport type NodeDependency = z.infer<typeof nodeDependency>;\n\nexport const udfConfig = looseObject({\n  serverVersion: z.string(),\n  // RNG seed encoded as Convex bytes in JSON.\n  importPhaseRngSeed: z.any(),\n  // Timestamp encoded as a Convex Int64 in JSON.\n  importPhaseUnixTimestamp: z.any(),\n});\nexport type UdfConfig = z.infer<typeof udfConfig>;\n\nexport const sourcePackage = z.any();\nexport type SourcePackage = z.infer<typeof sourcePackage>;\n\nexport const visibility = z.union([\n  looseObject({ kind: z.literal(\"public\") }),\n  looseObject({ kind: z.literal(\"internal\") }),\n]);\nexport type Visibility = z.infer<typeof visibility>;\n\nexport const analyzedFunction = looseObject({\n  name: z.string(),\n  pos: z.any(),\n  udfType: z.union([\n    z.literal(\"Query\"),\n    z.literal(\"Mutation\"),\n    z.literal(\"Action\"),\n  ]),\n  visibility: z.nullable(visibility),\n  args: z.nullable(z.string()),\n  returns: z.nullable(z.string()),\n});\nexport type AnalyzedFunction = z.infer<typeof analyzedFunction>;\n\nexport const analyzedModule = looseObject({\n  functions: z.array(analyzedFunction),\n  httpRoutes: z.any(),\n  cronSpecs: z.any(),\n  sourceMapped: z.any(),\n});\nexport type AnalyzedModule = z.infer<typeof analyzedModule>;\n"], "mappings": ";AAAA,SAAS,SAAS;AAClB,SAAS,mBAAmB;AAErB,aAAM,oBAAoB,EAAE,MAAM;AAAA,EACvC,EAAE,QAAQ,SAAS;AAAA,EACnB,EAAE,QAAQ,MAAM;AAClB,CAAC;AAGM,aAAM,eAAe,YAAY;AAAA,EACtC,MAAM,EAAE,OAAO;AAAA,EACf,QAAQ,EAAE,OAAO;AAAA,EACjB,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;AAAA,EAChC,aAAa;AACf,CAAC;AAGM,aAAM,iBAAiB,YAAY;AAAA,EACxC,MAAM,EAAE,OAAO;AAAA,EACf,SAAS,EAAE,OAAO;AACpB,CAAC;AAGM,aAAM,YAAY,YAAY;AAAA,EACnC,eAAe,EAAE,OAAO;AAAA;AAAA,EAExB,oBAAoB,EAAE,IAAI;AAAA;AAAA,EAE1B,0BAA0B,EAAE,IAAI;AAClC,CAAC;AAGM,aAAM,gBAAgB,EAAE,IAAI;AAG5B,aAAM,aAAa,EAAE,MAAM;AAAA,EAChC,YAAY,EAAE,MAAM,EAAE,QAAQ,QAAQ,EAAE,CAAC;AAAA,EACzC,YAAY,EAAE,MAAM,EAAE,QAAQ,UAAU,EAAE,CAAC;AAC7C,CAAC;AAGM,aAAM,mBAAmB,YAAY;AAAA,EAC1C,MAAM,EAAE,OAAO;AAAA,EACf,KAAK,EAAE,IAAI;AAAA,EACX,SAAS,EAAE,MAAM;AAAA,IACf,EAAE,QAAQ,OAAO;AAAA,IACjB,EAAE,QAAQ,UAAU;AAAA,IACpB,EAAE,QAAQ,QAAQ;AAAA,EACpB,CAAC;AAAA,EACD,YAAY,EAAE,SAAS,UAAU;AAAA,EACjC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;AAAA,EAC3B,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;AAChC,CAAC;AAGM,aAAM,iBAAiB,YAAY;AAAA,EACxC,WAAW,EAAE,MAAM,gBAAgB;AAAA,EACnC,YAAY,EAAE,IAAI;AAAA,EAClB,WAAW,EAAE,IAAI;AAAA,EACjB,cAAc,EAAE,IAAI;AACtB,CAAC;", "names": []}