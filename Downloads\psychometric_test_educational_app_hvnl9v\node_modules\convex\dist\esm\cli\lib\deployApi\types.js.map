{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/types.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const reference = z.string();\nexport type Reference = z.infer<typeof reference>;\n\n// passthrough for backward compat, auth was passthrough before custom JWTs were added\nconst Oidc = z\n  .object({\n    applicationID: z.string(),\n    domain: z.string(),\n  })\n  .passthrough();\n\nconst CustomJwt = z.object({\n  type: z.literal(\"customJwt\"),\n  applicationID: z.optional(z.string()),\n  issuer: z.string(),\n  jwks: z.string(),\n  algorithm: z.string(),\n});\n\nexport const authInfo = z.union([Oidc, CustomJwt]);\n\nexport type AuthInfo = z.infer<typeof authInfo>;\n\nexport const identifier = z.string();\nexport type Identifier = z.infer<typeof identifier>;\n"], "mappings": ";AAAA,SAAS,SAAS;AAEX,aAAM,YAAY,EAAE,OAAO;AAIlC,MAAM,OAAO,EACV,OAAO;AAAA,EACN,eAAe,EAAE,OAAO;AAAA,EACxB,QAAQ,EAAE,OAAO;AACnB,CAAC,EACA,YAAY;AAEf,MAAM,YAAY,EAAE,OAAO;AAAA,EACzB,MAAM,EAAE,QAAQ,WAAW;AAAA,EAC3B,eAAe,EAAE,SAAS,EAAE,OAAO,CAAC;AAAA,EACpC,QAAQ,EAAE,OAAO;AAAA,EACjB,MAAM,EAAE,OAAO;AAAA,EACf,WAAW,EAAE,OAAO;AACtB,CAAC;AAEM,aAAM,WAAW,EAAE,MAAM,CAAC,MAAM,SAAS,CAAC;AAI1C,aAAM,aAAa,EAAE,OAAO;", "names": []}