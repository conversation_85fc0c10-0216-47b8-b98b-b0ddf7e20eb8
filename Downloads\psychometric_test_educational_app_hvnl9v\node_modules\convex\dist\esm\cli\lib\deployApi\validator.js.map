{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/validator.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { looseObject } from \"./utils.js\";\n\nconst baseConvexValidator = z.discriminatedUnion(\"type\", [\n  looseObject({ type: z.literal(\"null\") }),\n  looseObject({ type: z.literal(\"number\") }),\n  looseObject({ type: z.literal(\"bigint\") }),\n  looseObject({ type: z.literal(\"boolean\") }),\n  looseObject({ type: z.literal(\"string\") }),\n  looseObject({ type: z.literal(\"bytes\") }),\n  looseObject({ type: z.literal(\"any\") }),\n  looseObject({ type: z.literal(\"literal\"), value: z.any() }),\n  looseObject({ type: z.literal(\"id\"), tableName: z.string() }),\n]);\nexport type ConvexValidator =\n  | z.infer<typeof baseConvexValidator>\n  | { type: \"array\"; value: ConvexValidator }\n  | {\n      type: \"record\";\n      keys: ConvexValidator;\n      values: { fieldType: ConvexValidator; optional: false };\n    }\n  | { type: \"union\"; value: ConvexValidator[] }\n  | {\n      type: \"object\";\n      value: Record<string, { fieldType: ConvexValidator; optional: boolean }>;\n    };\nexport const convexValidator: z.ZodType<ConvexValidator> = z.lazy(() =>\n  z.union([\n    baseConvexValidator,\n    looseObject({ type: z.literal(\"array\"), value: convexValidator }),\n    looseObject({\n      type: z.literal(\"record\"),\n      keys: convexValidator,\n      values: z.object({\n        fieldType: convexValidator,\n        optional: z.literal(false),\n      }),\n    }),\n    looseObject({\n      type: z.literal(\"union\"),\n      value: z.array(convexValidator),\n    }),\n    looseObject({\n      type: z.literal(\"object\"),\n      value: z.record(\n        looseObject({\n          fieldType: convexValidator,\n          optional: z.boolean(),\n        }),\n      ),\n    }),\n  ]),\n);\n"], "mappings": ";AAAA,SAAS,SAAS;AAClB,SAAS,mBAAmB;AAE5B,MAAM,sBAAsB,EAAE,mBAAmB,QAAQ;AAAA,EACvD,YAAY,EAAE,MAAM,EAAE,QAAQ,MAAM,EAAE,CAAC;AAAA,EACvC,YAAY,EAAE,MAAM,EAAE,QAAQ,QAAQ,EAAE,CAAC;AAAA,EACzC,YAAY,EAAE,MAAM,EAAE,QAAQ,QAAQ,EAAE,CAAC;AAAA,EACzC,YAAY,EAAE,MAAM,EAAE,QAAQ,SAAS,EAAE,CAAC;AAAA,EAC1C,YAAY,EAAE,MAAM,EAAE,QAAQ,QAAQ,EAAE,CAAC;AAAA,EACzC,YAAY,EAAE,MAAM,EAAE,QAAQ,OAAO,EAAE,CAAC;AAAA,EACxC,YAAY,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,EACtC,YAAY,EAAE,MAAM,EAAE,QAAQ,SAAS,GAAG,OAAO,EAAE,IAAI,EAAE,CAAC;AAAA,EAC1D,YAAY,EAAE,MAAM,EAAE,QAAQ,IAAI,GAAG,WAAW,EAAE,OAAO,EAAE,CAAC;AAC9D,CAAC;AAcM,aAAM,kBAA8C,EAAE;AAAA,EAAK,MAChE,EAAE,MAAM;AAAA,IACN;AAAA,IACA,YAAY,EAAE,MAAM,EAAE,QAAQ,OAAO,GAAG,OAAO,gBAAgB,CAAC;AAAA,IAChE,YAAY;AAAA,MACV,MAAM,EAAE,QAAQ,QAAQ;AAAA,MACxB,MAAM;AAAA,MACN,QAAQ,EAAE,OAAO;AAAA,QACf,WAAW;AAAA,QACX,UAAU,EAAE,QAAQ,KAAK;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY;AAAA,MACV,MAAM,EAAE,QAAQ,OAAO;AAAA,MACvB,OAAO,EAAE,MAAM,eAAe;AAAA,IAChC,CAAC;AAAA,IACD,YAAY;AAAA,MACV,MAAM,EAAE,QAAQ,QAAQ;AAAA,MACxB,OAAO,EAAE;AAAA,QACP,YAAY;AAAA,UACV,WAAW;AAAA,UACX,UAAU,EAAE,QAAQ;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;", "names": []}