{"version": 3, "sources": ["../../../../src/cli/lib/env.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport {\n  Context,\n  logFailure,\n  logFinishedStep,\n  logMessage,\n  logOutput,\n} from \"../../bundler/context.js\";\nimport { runSystemQuery } from \"./run.js\";\nimport { deploymentFetch, logAndHandleFetchError } from \"./utils/utils.js\";\n\nexport async function envSetInDeployment(\n  ctx: Context,\n  deployment: {\n    deploymentUrl: string;\n    adminKey: string;\n    deploymentNotice: string;\n  },\n  originalName: string,\n  originalValue: string | undefined,\n) {\n  const [name, value] = await allowEqualsSyntax(\n    ctx,\n    originalName,\n    originalValue,\n  );\n  await callUpdateEnvironmentVariables(ctx, deployment, [{ name, value }]);\n  const formatted = /\\s/.test(value) ? `\"${value}\"` : value;\n  logFinishedStep(\n    ctx,\n    `Successfully set ${chalk.bold(name)} to ${chalk.bold(formatted)}${deployment.deploymentNotice}`,\n  );\n}\n\nasync function allowEqualsSyntax(\n  ctx: Context,\n  name: string,\n  value: string | undefined,\n) {\n  if (value === undefined) {\n    if (/^[a-zA-Z][a-zA-Z0-9_]+=/.test(name)) {\n      return name.split(\"=\", 2);\n    } else {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: \"error: missing required argument 'value'\",\n      });\n    }\n  }\n  return [name, value];\n}\n\nexport async function envGetInDeployment(\n  ctx: Context,\n  deployment: {\n    deploymentUrl: string;\n    adminKey: string;\n  },\n  name: string,\n) {\n  const envVar = (await runSystemQuery(ctx, {\n    ...deployment,\n    functionName: \"_system/cli/queryEnvironmentVariables:get\",\n    componentPath: undefined,\n    args: { name },\n  })) as EnvVar | null;\n  if (envVar === null) {\n    logFailure(ctx, `Environment variable \"${name}\" not found.`);\n    return;\n  }\n  logOutput(ctx, `${envVar.value}`);\n}\n\nexport async function envRemoveInDeployment(\n  ctx: Context,\n  deployment: {\n    deploymentUrl: string;\n    adminKey: string;\n    deploymentNotice: string;\n  },\n  name: string,\n) {\n  await callUpdateEnvironmentVariables(ctx, deployment, [{ name }]);\n  logFinishedStep(\n    ctx,\n    `Successfully unset ${chalk.bold(name)}${deployment.deploymentNotice}`,\n  );\n}\n\nexport async function envListInDeployment(\n  ctx: Context,\n  deployment: {\n    deploymentUrl: string;\n    adminKey: string;\n  },\n) {\n  const envs = (await runSystemQuery(ctx, {\n    ...deployment,\n    functionName: \"_system/cli/queryEnvironmentVariables\",\n    componentPath: undefined,\n    args: {},\n  })) as EnvVar[];\n  if (envs.length === 0) {\n    logMessage(ctx, \"No environment variables set.\");\n    return;\n  }\n  for (const { name, value } of envs) {\n    logOutput(ctx, `${name}=${value}`);\n  }\n}\n\nexport type EnvVarChange = {\n  name: string;\n  value?: string;\n};\n\nexport type EnvVar = {\n  name: string;\n  value: string;\n};\n\nasync function callUpdateEnvironmentVariables(\n  ctx: Context,\n  deployment: {\n    deploymentUrl: string;\n    adminKey: string;\n    deploymentNotice: string;\n  },\n  changes: EnvVarChange[],\n) {\n  const fetch = deploymentFetch(ctx, deployment);\n  try {\n    await fetch(\"/api/update_environment_variables\", {\n      body: JSON.stringify({ changes }),\n      method: \"POST\",\n    });\n  } catch (e) {\n    return await logAndHandleFetchError(ctx, e);\n  }\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,sBAAsB;AAC/B,SAAS,iBAAiB,8BAA8B;AAExD,sBAAsB,mBACpB,KACA,YAKA,cACA,eACA;AACA,QAAM,CAAC,MAAM,KAAK,IAAI,MAAM;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,+BAA+B,KAAK,YAAY,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC;AACvE,QAAM,YAAY,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,MAAM;AACpD;AAAA,IACE;AAAA,IACA,oBAAoB,MAAM,KAAK,IAAI,CAAC,OAAO,MAAM,KAAK,SAAS,CAAC,GAAG,WAAW,gBAAgB;AAAA,EAChG;AACF;AAEA,eAAe,kBACb,KACA,MACA,OACA;AACA,MAAI,UAAU,QAAW;AACvB,QAAI,0BAA0B,KAAK,IAAI,GAAG;AACxC,aAAO,KAAK,MAAM,KAAK,CAAC;AAAA,IAC1B,OAAO;AACL,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,CAAC,MAAM,KAAK;AACrB;AAEA,sBAAsB,mBACpB,KACA,YAIA,MACA;AACA,QAAM,SAAU,MAAM,eAAe,KAAK;AAAA,IACxC,GAAG;AAAA,IACH,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM,EAAE,KAAK;AAAA,EACf,CAAC;AACD,MAAI,WAAW,MAAM;AACnB,eAAW,KAAK,yBAAyB,IAAI,cAAc;AAC3D;AAAA,EACF;AACA,YAAU,KAAK,GAAG,OAAO,KAAK,EAAE;AAClC;AAEA,sBAAsB,sBACpB,KACA,YAKA,MACA;AACA,QAAM,+BAA+B,KAAK,YAAY,CAAC,EAAE,KAAK,CAAC,CAAC;AAChE;AAAA,IACE;AAAA,IACA,sBAAsB,MAAM,KAAK,IAAI,CAAC,GAAG,WAAW,gBAAgB;AAAA,EACtE;AACF;AAEA,sBAAsB,oBACpB,KACA,YAIA;AACA,QAAM,OAAQ,MAAM,eAAe,KAAK;AAAA,IACtC,GAAG;AAAA,IACH,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM,CAAC;AAAA,EACT,CAAC;AACD,MAAI,KAAK,WAAW,GAAG;AACrB,eAAW,KAAK,+BAA+B;AAC/C;AAAA,EACF;AACA,aAAW,EAAE,MAAM,MAAM,KAAK,MAAM;AAClC,cAAU,KAAK,GAAG,IAAI,IAAI,KAAK,EAAE;AAAA,EACnC;AACF;AAYA,eAAe,+BACb,KACA,YAKA,SACA;AACA,QAAM,QAAQ,gBAAgB,KAAK,UAAU;AAC7C,MAAI;AACF,UAAM,MAAM,qCAAqC;AAAA,MAC/C,MAAM,KAAK,UAAU,EAAE,QAAQ,CAAC;AAAA,MAChC,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,SAAS,GAAG;AACV,WAAO,MAAM,uBAAuB,KAAK,CAAC;AAAA,EAC5C;AACF;", "names": []}