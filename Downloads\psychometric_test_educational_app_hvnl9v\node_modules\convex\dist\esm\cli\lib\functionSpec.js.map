{"version": 3, "sources": ["../../../../src/cli/lib/functionSpec.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport { logOutput } from \"../../bundler/context.js\";\nimport { runSystemQuery } from \"./run.js\";\nimport { Context } from \"../../bundler/context.js\";\n\nexport async function functionSpecForDeployment(\n  ctx: Context,\n  options: {\n    deploymentUrl: string;\n    adminKey: string;\n    file: boolean;\n  },\n) {\n  const functions = (await runSystemQuery(ctx, {\n    deploymentUrl: options.deploymentUrl,\n    adminKey: options.adminKey,\n    functionName: \"_system/cli/modules:apiSpec\",\n    componentPath: undefined,\n    args: {},\n  })) as any[];\n  const url = (await runSystemQuery(ctx, {\n    deploymentUrl: options.deploymentUrl,\n    adminKey: options.adminKey,\n    functionName: \"_system/cli/convexUrl:cloudUrl\",\n    componentPath: undefined,\n    args: {},\n  })) as string;\n\n  const output = JSON.stringify({ url, functions }, null, 2);\n\n  if (options.file) {\n    const fileName = `function_spec_${Date.now().valueOf()}.json`;\n    ctx.fs.writeUtf8File(fileName, output);\n    logOutput(ctx, chalk.green(`Wrote function spec to ${fileName}`));\n  } else {\n    logOutput(ctx, output);\n  }\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAG/B,sBAAsB,0BACpB,KACA,SAKA;AACA,QAAM,YAAa,MAAM,eAAe,KAAK;AAAA,IAC3C,eAAe,QAAQ;AAAA,IACvB,UAAU,QAAQ;AAAA,IAClB,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM,CAAC;AAAA,EACT,CAAC;AACD,QAAM,MAAO,MAAM,eAAe,KAAK;AAAA,IACrC,eAAe,QAAQ;AAAA,IACvB,UAAU,QAAQ;AAAA,IAClB,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM,CAAC;AAAA,EACT,CAAC;AAED,QAAM,SAAS,KAAK,UAAU,EAAE,KAAK,UAAU,GAAG,MAAM,CAAC;AAEzD,MAAI,QAAQ,MAAM;AAChB,UAAM,WAAW,iBAAiB,KAAK,IAAI,EAAE,QAAQ,CAAC;AACtD,QAAI,GAAG,cAAc,UAAU,MAAM;AACrC,cAAU,KAAK,MAAM,MAAM,0BAA0B,QAAQ,EAAE,CAAC;AAAA,EAClE,OAAO;AACL,cAAU,KAAK,MAAM;AAAA,EACvB;AACF;", "names": []}