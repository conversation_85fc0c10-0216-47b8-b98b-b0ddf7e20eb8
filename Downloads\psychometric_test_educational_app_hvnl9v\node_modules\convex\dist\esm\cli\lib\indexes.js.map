{"version": 3, "sources": ["../../../../src/cli/lib/indexes.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport path from \"path\";\nimport { bundleSchema } from \"../../bundler/index.js\";\nimport {\n  Context,\n  changeSpinner,\n  logFailure,\n  logFinishedStep,\n  logError,\n} from \"../../bundler/context.js\";\nimport {\n  poll,\n  logAndHandleFetchError,\n  deploymentFetch,\n  deprecationCheckWarning,\n} from \"./utils/utils.js\";\n\ntype IndexMetadata = {\n  table: string;\n  name: string;\n  fields:\n    | string[]\n    | {\n        searchField: string;\n        filterFields: string[];\n      };\n  backfill: {\n    state: \"in_progress\" | \"done\";\n  };\n};\n\ntype SchemaState =\n  | { state: \"pending\" }\n  | { state: \"validated\" }\n  | { state: \"active\" }\n  | { state: \"overwritten\" }\n  | { state: \"failed\"; error: string; tableName?: string };\n\ntype SchemaStateResponse = {\n  indexes: IndexMetadata[];\n  schemaState: SchemaState;\n};\ntype PrepareSchemaResponse = {\n  added: IndexMetadata[];\n  dropped: IndexMetadata[];\n  schemaId: string;\n};\n\nexport async function pushSchema(\n  ctx: Context,\n  origin: string,\n  adminKey: string,\n  schemaDir: string,\n  dryRun: boolean,\n): Promise<{ schemaId?: string; schemaState?: SchemaState }> {\n  if (\n    !ctx.fs.exists(path.resolve(schemaDir, \"schema.ts\")) &&\n    !ctx.fs.exists(path.resolve(schemaDir, \"schema.js\"))\n  ) {\n    // Don't do anything.\n    return {};\n  }\n  const bundles = await bundleSchema(ctx, schemaDir, []);\n\n  changeSpinner(ctx, \"Checking for index or schema changes...\");\n\n  let data: PrepareSchemaResponse;\n  const fetch = deploymentFetch(ctx, {\n    deploymentUrl: origin,\n    adminKey,\n  });\n  try {\n    const res = await fetch(\"/api/prepare_schema\", {\n      method: \"POST\",\n      body: JSON.stringify({\n        bundle: bundles[0],\n        adminKey,\n        dryRun,\n      }),\n    });\n    deprecationCheckWarning(ctx, res);\n    data = await res.json();\n  } catch (err: unknown) {\n    logFailure(ctx, `Error: Unable to run schema validation on ${origin}`);\n    return await logAndHandleFetchError(ctx, err);\n  }\n\n  const schemaId = data.schemaId;\n\n  const schemaState = await waitForReadySchema(ctx, origin, adminKey, schemaId);\n  logIndexChanges(ctx, data, dryRun);\n  return { schemaId, schemaState };\n}\n\n/// Wait for indexes to build and schema to be validated.\nasync function waitForReadySchema(\n  ctx: Context,\n  origin: string,\n  adminKey: string,\n  schemaId: string,\n): Promise<SchemaState> {\n  const path = `api/schema_state/${schemaId}`;\n  const depFetch = deploymentFetch(ctx, {\n    deploymentUrl: origin,\n    adminKey,\n  });\n  const fetch = async () => {\n    try {\n      const resp = await depFetch(path, { method: \"GET\" });\n      const data: SchemaStateResponse = await resp.json();\n      return data;\n    } catch (err: unknown) {\n      logFailure(\n        ctx,\n        `Error: Unable to build indexes and run schema validation on ${origin}`,\n      );\n      return await logAndHandleFetchError(ctx, err);\n    }\n  };\n\n  // Set the spinner to the default progress message before the first `fetch` call returns.\n  setSchemaProgressSpinner(ctx, null);\n\n  const data = await poll(fetch, (data: SchemaStateResponse) => {\n    setSchemaProgressSpinner(ctx, data);\n    return (\n      data.indexes.every((index) => index.backfill.state === \"done\") &&\n      data.schemaState.state !== \"pending\"\n    );\n  });\n\n  switch (data.schemaState.state) {\n    case \"failed\":\n      // Schema validation failed. This could be either because the data\n      // is bad or the schema is wrong. Classify this as a filesystem error\n      // because adjusting `schema.ts` is the most normal next step.\n      logFailure(ctx, \"Schema validation failed\");\n      logError(ctx, chalk.red(`${data.schemaState.error}`));\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: {\n          \"invalid filesystem or db data\": data.schemaState.tableName\n            ? {\n                tableName: data.schemaState.tableName,\n              }\n            : null,\n        },\n        printedMessage: null, // TODO - move logging into here\n      });\n\n    case \"overwritten\":\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Schema was overwritten by another push.`,\n      });\n    case \"validated\":\n      logFinishedStep(ctx, \"Schema validation complete.\");\n      break;\n    case \"active\":\n      break;\n  }\n  return data.schemaState;\n}\n\nfunction setSchemaProgressSpinner(\n  ctx: Context,\n  data: SchemaStateResponse | null,\n) {\n  if (!data) {\n    changeSpinner(\n      ctx,\n      \"Backfilling indexes and checking that documents match your schema...\",\n    );\n    return;\n  }\n  const indexesCompleted = data.indexes.filter(\n    (index) => index.backfill.state === \"done\",\n  ).length;\n  const numIndexes = data.indexes.length;\n\n  const indexesDone = indexesCompleted === numIndexes;\n  const schemaDone = data.schemaState.state !== \"pending\";\n\n  if (indexesDone && schemaDone) {\n    return;\n  }\n\n  let msg: string;\n  if (!indexesDone && !schemaDone) {\n    msg = `Backfilling indexes (${indexesCompleted}/${numIndexes} ready) and checking that documents match your schema...`;\n  } else if (!indexesDone) {\n    msg = `Backfilling indexes (${indexesCompleted}/${numIndexes} ready)...`;\n  } else {\n    msg = \"Checking that documents match your schema...\";\n  }\n  changeSpinner(ctx, msg);\n}\n\nfunction logIndexChanges(\n  ctx: Context,\n  indexes: {\n    added: IndexMetadata[];\n    dropped: IndexMetadata[];\n  },\n  dryRun: boolean,\n) {\n  if (indexes.dropped.length > 0) {\n    let indexDiff = \"\";\n    for (const index of indexes.dropped) {\n      indexDiff += `  [-] ${stringifyIndex(index)}\\n`;\n    }\n    // strip last new line\n    indexDiff = indexDiff.slice(0, -1);\n    logFinishedStep(\n      ctx,\n      `${dryRun ? \"Would delete\" : \"Deleted\"} table indexes:\\n${indexDiff}`,\n    );\n  }\n  if (indexes.added.length > 0) {\n    let indexDiff = \"\";\n    for (const index of indexes.added) {\n      indexDiff += `  [+] ${stringifyIndex(index)}\\n`;\n    }\n    // strip last new line\n    indexDiff = indexDiff.slice(0, -1);\n    logFinishedStep(\n      ctx,\n      `${dryRun ? \"Would add\" : \"Added\"} table indexes:\\n${indexDiff}`,\n    );\n  }\n}\n\nfunction stringifyIndex(index: IndexMetadata) {\n  return `${index.table}.${index.name} ${JSON.stringify(index.fields)}`;\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,OAAO,UAAU;AACjB,SAAS,oBAAoB;AAC7B;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAiCP,sBAAsB,WACpB,KACA,QACA,UACA,WACA,QAC2D;AAC3D,MACE,CAAC,IAAI,GAAG,OAAO,KAAK,QAAQ,WAAW,WAAW,CAAC,KACnD,CAAC,IAAI,GAAG,OAAO,KAAK,QAAQ,WAAW,WAAW,CAAC,GACnD;AAEA,WAAO,CAAC;AAAA,EACV;AACA,QAAM,UAAU,MAAM,aAAa,KAAK,WAAW,CAAC,CAAC;AAErD,gBAAc,KAAK,yCAAyC;AAE5D,MAAI;AACJ,QAAM,QAAQ,gBAAgB,KAAK;AAAA,IACjC,eAAe;AAAA,IACf;AAAA,EACF,CAAC;AACD,MAAI;AACF,UAAM,MAAM,MAAM,MAAM,uBAAuB;AAAA,MAC7C,QAAQ;AAAA,MACR,MAAM,KAAK,UAAU;AAAA,QACnB,QAAQ,QAAQ,CAAC;AAAA,QACjB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,4BAAwB,KAAK,GAAG;AAChC,WAAO,MAAM,IAAI,KAAK;AAAA,EACxB,SAAS,KAAc;AACrB,eAAW,KAAK,6CAA6C,MAAM,EAAE;AACrE,WAAO,MAAM,uBAAuB,KAAK,GAAG;AAAA,EAC9C;AAEA,QAAM,WAAW,KAAK;AAEtB,QAAM,cAAc,MAAM,mBAAmB,KAAK,QAAQ,UAAU,QAAQ;AAC5E,kBAAgB,KAAK,MAAM,MAAM;AACjC,SAAO,EAAE,UAAU,YAAY;AACjC;AAGA,eAAe,mBACb,KACA,QACA,UACA,UACsB;AACtB,QAAMA,QAAO,oBAAoB,QAAQ;AACzC,QAAM,WAAW,gBAAgB,KAAK;AAAA,IACpC,eAAe;AAAA,IACf;AAAA,EACF,CAAC;AACD,QAAM,QAAQ,YAAY;AACxB,QAAI;AACF,YAAM,OAAO,MAAM,SAASA,OAAM,EAAE,QAAQ,MAAM,CAAC;AACnD,YAAMC,QAA4B,MAAM,KAAK,KAAK;AAClD,aAAOA;AAAA,IACT,SAAS,KAAc;AACrB;AAAA,QACE;AAAA,QACA,+DAA+D,MAAM;AAAA,MACvE;AACA,aAAO,MAAM,uBAAuB,KAAK,GAAG;AAAA,IAC9C;AAAA,EACF;AAGA,2BAAyB,KAAK,IAAI;AAElC,QAAM,OAAO,MAAM,KAAK,OAAO,CAACA,UAA8B;AAC5D,6BAAyB,KAAKA,KAAI;AAClC,WACEA,MAAK,QAAQ,MAAM,CAAC,UAAU,MAAM,SAAS,UAAU,MAAM,KAC7DA,MAAK,YAAY,UAAU;AAAA,EAE/B,CAAC;AAED,UAAQ,KAAK,YAAY,OAAO;AAAA,IAC9B,KAAK;AAIH,iBAAW,KAAK,0BAA0B;AAC1C,eAAS,KAAK,MAAM,IAAI,GAAG,KAAK,YAAY,KAAK,EAAE,CAAC;AACpD,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,UACT,iCAAiC,KAAK,YAAY,YAC9C;AAAA,YACE,WAAW,KAAK,YAAY;AAAA,UAC9B,IACA;AAAA,QACN;AAAA,QACA,gBAAgB;AAAA;AAAA,MAClB,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,KAAK;AACH,sBAAgB,KAAK,6BAA6B;AAClD;AAAA,IACF,KAAK;AACH;AAAA,EACJ;AACA,SAAO,KAAK;AACd;AAEA,SAAS,yBACP,KACA,MACA;AACA,MAAI,CAAC,MAAM;AACT;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA;AAAA,EACF;AACA,QAAM,mBAAmB,KAAK,QAAQ;AAAA,IACpC,CAAC,UAAU,MAAM,SAAS,UAAU;AAAA,EACtC,EAAE;AACF,QAAM,aAAa,KAAK,QAAQ;AAEhC,QAAM,cAAc,qBAAqB;AACzC,QAAM,aAAa,KAAK,YAAY,UAAU;AAE9C,MAAI,eAAe,YAAY;AAC7B;AAAA,EACF;AAEA,MAAI;AACJ,MAAI,CAAC,eAAe,CAAC,YAAY;AAC/B,UAAM,wBAAwB,gBAAgB,IAAI,UAAU;AAAA,EAC9D,WAAW,CAAC,aAAa;AACvB,UAAM,wBAAwB,gBAAgB,IAAI,UAAU;AAAA,EAC9D,OAAO;AACL,UAAM;AAAA,EACR;AACA,gBAAc,KAAK,GAAG;AACxB;AAEA,SAAS,gBACP,KACA,SAIA,QACA;AACA,MAAI,QAAQ,QAAQ,SAAS,GAAG;AAC9B,QAAI,YAAY;AAChB,eAAW,SAAS,QAAQ,SAAS;AACnC,mBAAa,SAAS,eAAe,KAAK,CAAC;AAAA;AAAA,IAC7C;AAEA,gBAAY,UAAU,MAAM,GAAG,EAAE;AACjC;AAAA,MACE;AAAA,MACA,GAAG,SAAS,iBAAiB,SAAS;AAAA,EAAoB,SAAS;AAAA,IACrE;AAAA,EACF;AACA,MAAI,QAAQ,MAAM,SAAS,GAAG;AAC5B,QAAI,YAAY;AAChB,eAAW,SAAS,QAAQ,OAAO;AACjC,mBAAa,SAAS,eAAe,KAAK,CAAC;AAAA;AAAA,IAC7C;AAEA,gBAAY,UAAU,MAAM,GAAG,EAAE;AACjC;AAAA,MACE;AAAA,MACA,GAAG,SAAS,cAAc,OAAO;AAAA,EAAoB,SAAS;AAAA,IAChE;AAAA,EACF;AACF;AAEA,SAAS,eAAe,OAAsB;AAC5C,SAAO,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK,UAAU,MAAM,MAAM,CAAC;AACrE;", "names": ["path", "data"]}