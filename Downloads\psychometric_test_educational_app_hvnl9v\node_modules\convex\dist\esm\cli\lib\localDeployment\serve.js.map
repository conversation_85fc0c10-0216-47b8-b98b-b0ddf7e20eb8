{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/serve.ts"], "sourcesContent": ["import http from \"node:http\";\nimport { Context } from \"../../../bundler/context.js\";\nimport { logVerbose } from \"../../../bundler/context.js\";\n\n// The below is adapted from https://github.com/vercel/serve/blob/main/source/utilities/server.ts\n// MIT License -- https://github.com/vercel/serve/blob/main/license.md\n// Copyright (c) 2023 Vercel, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// This has been pared down to only support running locally. It removed options\n// we're not using, and added Convex-CLI specific cleanup handling.\nexport const startServer = async (\n  ctx: Context,\n  port: number,\n  handler: (\n    request: http.IncomingMessage,\n    response: http.ServerResponse,\n  ) => Promise<void>,\n  options: {\n    cors?: boolean;\n  },\n): Promise<{ cleanupHandle: string }> => {\n  // Define the request handler for the server.\n  const serverHandler = (request: any, response: any): void => {\n    // We can't return a promise in a HTTP request handler, so we run our code\n    // inside an async function instead.\n    const run = async () => {\n      if (options.cors) {\n        response.setHeader(\"Access-Control-Allow-Origin\", \"*\");\n        response.setHeader(\"Access-Control-Allow-Headers\", \"*\");\n        response.setHeader(\"Access-Control-Allow-Credentials\", \"true\");\n        response.setHeader(\"Access-Control-Allow-Private-Network\", \"true\");\n      }\n      // TODO -- consider adding support for compression\n      // if (!args['--no-compression'])\n      //   await compress(request as ExpressRequest, response as ExpressResponse);\n\n      await handler(request, response);\n    };\n\n    // Then we run the async function, and log any errors.\n    // TODO: consider adding a `onError` callback in case we want different error\n    // handling.\n    run().catch((error: Error) => {\n      logVerbose(\n        ctx,\n        `Failed to serve: ${error.stack?.toString() ?? error.message}`,\n      );\n    });\n  };\n\n  const server = http.createServer(serverHandler);\n  const cleanupHandle = ctx.registerCleanup(async () => {\n    logVerbose(ctx, `Stopping server on port ${port}`);\n    await server.close();\n  });\n\n  // Listen for any error that occurs while serving, and throw an error\n  // if any errors are received.\n  server.on(\"error\", (error) => {\n    logVerbose(\n      ctx,\n      `Failed to serve: ${error.stack?.toString() ?? error.message}`,\n    );\n  });\n\n  // Finally, start the server -- this promise resolves once the server has started.\n  await new Promise((resolve, _reject) => {\n    server.listen(port, `127.0.0.1`, () => resolve(`http://127.0.0.1:${port}`));\n  });\n  return { cleanupHandle };\n};\n"], "mappings": ";AAAA,OAAO,UAAU;AAEjB,SAAS,kBAAkB;AAcpB,aAAM,cAAc,OACzB,KACA,MACA,SAIA,YAGuC;AAEvC,QAAM,gBAAgB,CAAC,SAAc,aAAwB;AAG3D,UAAM,MAAM,YAAY;AACtB,UAAI,QAAQ,MAAM;AAChB,iBAAS,UAAU,+BAA+B,GAAG;AACrD,iBAAS,UAAU,gCAAgC,GAAG;AACtD,iBAAS,UAAU,oCAAoC,MAAM;AAC7D,iBAAS,UAAU,wCAAwC,MAAM;AAAA,MACnE;AAKA,YAAM,QAAQ,SAAS,QAAQ;AAAA,IACjC;AAKA,QAAI,EAAE,MAAM,CAAC,UAAiB;AAC5B;AAAA,QACE;AAAA,QACA,oBAAoB,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO;AAAA,MAC9D;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,SAAS,KAAK,aAAa,aAAa;AAC9C,QAAM,gBAAgB,IAAI,gBAAgB,YAAY;AACpD,eAAW,KAAK,2BAA2B,IAAI,EAAE;AACjD,UAAM,OAAO,MAAM;AAAA,EACrB,CAAC;AAID,SAAO,GAAG,SAAS,CAAC,UAAU;AAC5B;AAAA,MACE;AAAA,MACA,oBAAoB,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO;AAAA,IAC9D;AAAA,EACF,CAAC;AAGD,QAAM,IAAI,QAAQ,CAAC,SAAS,YAAY;AACtC,WAAO,OAAO,MAAM,aAAa,MAAM,QAAQ,oBAAoB,IAAI,EAAE,CAAC;AAAA,EAC5E,CAAC;AACD,SAAO,EAAE,cAAc;AACzB;", "names": []}