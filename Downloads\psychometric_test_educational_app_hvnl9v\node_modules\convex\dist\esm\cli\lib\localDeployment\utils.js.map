{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/utils.ts"], "sourcesContent": ["import { Context, logMessage } from \"../../../bundler/context.js\";\nimport { detect } from \"detect-port\";\nimport crypto from \"crypto\";\nimport chalk from \"chalk\";\n\nexport async function choosePorts(\n  ctx: Context,\n  {\n    count,\n    requestedPorts,\n    startPort,\n  }: {\n    count: number;\n    requestedPorts?: Array<number | null>;\n    startPort: number;\n  },\n): Promise<Array<number>> {\n  const ports: Array<number> = [];\n  for (let i = 0; i < count; i++) {\n    const requestedPort = requestedPorts?.[i];\n    if (requestedPort !== null) {\n      const port = await detect(requestedPort);\n      if (port !== requestedPort) {\n        return ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: \"Requested port is not available\",\n        });\n      }\n      ports.push(port);\n    } else {\n      const portToTry =\n        ports.length > 0 ? ports[ports.length - 1] + 1 : startPort;\n      const port = await detect(portToTry);\n      ports.push(port);\n    }\n  }\n  return ports;\n}\n\nexport async function isOffline(): Promise<boolean> {\n  // TODO(ENG-7080) -- implement this for real\n  return false;\n}\n\nexport function printLocalDeploymentWelcomeMessage(ctx: Context) {\n  logMessage(\n    ctx,\n    chalk.cyan(\"You're trying out the beta local deployment feature!\"),\n  );\n  logMessage(\n    ctx,\n    chalk.cyan(\n      \"To learn more, read the docs: https://docs.convex.dev/cli/local-deployments\",\n    ),\n  );\n  logMessage(\n    ctx,\n    chalk.cyan(\n      \"To opt out at any time, run `npx convex disable-local-deployments`\",\n    ),\n  );\n}\n\nexport function generateInstanceSecret(): string {\n  return crypto.randomBytes(32).toString(\"hex\");\n}\n\nexport const LOCAL_BACKEND_INSTANCE_SECRET =\n  \"4361726e697461732c206c69746572616c6c79206d65616e696e6720226c6974\";\n"], "mappings": ";AAAA,SAAkB,kBAAkB;AACpC,SAAS,cAAc;AACvB,OAAO,YAAY;AACnB,OAAO,WAAW;AAElB,sBAAsB,YACpB,KACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AACF,GAKwB;AACxB,QAAM,QAAuB,CAAC;AAC9B,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,UAAM,gBAAgB,iBAAiB,CAAC;AACxC,QAAI,kBAAkB,MAAM;AAC1B,YAAM,OAAO,MAAM,OAAO,aAAa;AACvC,UAAI,SAAS,eAAe;AAC1B,eAAO,IAAI,MAAM;AAAA,UACf,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AACA,YAAM,KAAK,IAAI;AAAA,IACjB,OAAO;AACL,YAAM,YACJ,MAAM,SAAS,IAAI,MAAM,MAAM,SAAS,CAAC,IAAI,IAAI;AACnD,YAAM,OAAO,MAAM,OAAO,SAAS;AACnC,YAAM,KAAK,IAAI;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AAEA,sBAAsB,YAA8B;AAElD,SAAO;AACT;AAEO,gBAAS,mCAAmC,KAAc;AAC/D;AAAA,IACE;AAAA,IACA,MAAM,KAAK,sDAAsD;AAAA,EACnE;AACA;AAAA,IACE;AAAA,IACA,MAAM;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA,MAAM;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAEO,gBAAS,yBAAiC;AAC/C,SAAO,OAAO,YAAY,EAAE,EAAE,SAAS,KAAK;AAC9C;AAEO,aAAM,gCACX;", "names": []}