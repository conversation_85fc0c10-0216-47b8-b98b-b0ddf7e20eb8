{"version": 3, "sources": ["../../../../src/cli/lib/login.ts"], "sourcesContent": ["import { errors, BaseClient, custom } from \"openid-client\";\nimport {\n  bigBrainAPI,\n  logAndHandleFetchError,\n  throwingFetch,\n  isWebContainer,\n} from \"./utils/utils.js\";\nimport open from \"open\";\nimport chalk from \"chalk\";\nimport { provisionHost } from \"./config.js\";\nimport { version } from \"../version.js\";\nimport {\n  Context,\n  changeSpinner,\n  logError,\n  logFailure,\n  logFinishedStep,\n  logMessage,\n  logOutput,\n  logVerbose,\n  showSpinner,\n} from \"../../bundler/context.js\";\nimport { Issuer } from \"openid-client\";\nimport { hostname } from \"os\";\nimport { execSync } from \"child_process\";\nimport { promptString, promptYesNo } from \"./utils/prompts.js\";\nimport {\n  formatPathForPrinting,\n  globalConfigPath,\n  modifyGlobalConfig,\n} from \"./utils/globalConfig.js\";\nimport { updateBigBrainAuthAfterLogin } from \"./deploymentSelection.js\";\n\nconst SCOPE = \"openid email profile\";\n/// This value was created long ago, and cannot be changed easily.\n/// It's just a fixed string used for identifying the Auth0 token, so it's fine\n/// and not user-facing.\nconst AUDIENCE = \"https://console.convex.dev/api/\";\n\n// Per https://github.com/panva/node-openid-client/tree/main/docs#customizing\ncustom.setHttpOptionsDefaults({\n  timeout: parseInt(process.env.OPENID_CLIENT_TIMEOUT || \"10000\"),\n});\n\ninterface AuthorizeArgs {\n  authnToken: string;\n  deviceName: string;\n  anonymousId?: string;\n}\n\nexport async function checkAuthorization(\n  ctx: Context,\n  acceptOptIns: boolean,\n): Promise<boolean> {\n  const header = ctx.bigBrainAuth()?.header ?? null;\n  if (header === null) {\n    return false;\n  }\n  try {\n    const resp = await fetch(`${provisionHost}/api/authorize`, {\n      method: \"HEAD\",\n      headers: {\n        Authorization: header,\n        \"Convex-Client\": `npm-cli-${version}`,\n      },\n    });\n    // Don't throw an error if this request returns a non-200 status.\n    // Big Brain responds with a variety of error codes -- 401 if the token is correctly-formed but not valid, and either 400 or 500 if the token is ill-formed.\n    // We only care if this check returns a 200 code (so we can skip logging in again) -- any other errors should be silently skipped and we'll run the whole login flow again.\n    if (resp.status !== 200) {\n      return false;\n    }\n  } catch (e: any) {\n    // This `catch` block should only be hit if a network error was encountered\n    logError(\n      ctx,\n      `Unexpected error when authorizing - are you connected to the internet?`,\n    );\n    return await logAndHandleFetchError(ctx, e);\n  }\n\n  // Check that we have optin as well\n  const shouldContinue = await optins(ctx, acceptOptIns);\n  if (!shouldContinue) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: null,\n    });\n  }\n  return true;\n}\n\nasync function performDeviceAuthorization(\n  ctx: Context,\n  auth0Client: BaseClient,\n  shouldOpen: boolean,\n): Promise<string> {\n  // Device authorization flow follows this guide: https://github.com/auth0/auth0-device-flow-cli-sample/blob/9f0f3b76a6cd56ea8d99e76769187ea5102d519d/cli.js\n  // License: MIT License\n  // Copyright (c) 2019 Auth0 Samples\n  /*\n  The MIT License (MIT)\n\n  Copyright (c) 2019 Auth0 Samples\n\n  Permission is hereby granted, free of charge, to any person obtaining a copy\n  of this software and associated documentation files (the \"Software\"), to deal\n  in the Software without restriction, including without limitation the rights\n  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n  copies of the Software, and to permit persons to whom the Software is\n  furnished to do so, subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be included in all\n  copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n  */\n\n  // Device Authorization Request - https://tools.ietf.org/html/rfc8628#section-3.1\n  // Get authentication URL\n  let handle;\n  try {\n    handle = await auth0Client.deviceAuthorization({\n      scope: SCOPE,\n      audience: AUDIENCE,\n    });\n  } catch {\n    // We couldn't get verification URL from Auth0, proceed with manual auth\n    return promptString(ctx, {\n      message:\n        \"Open https://dashboard.convex.dev/auth, log in and paste the token here:\",\n    });\n  }\n\n  // Device Authorization Response - https://tools.ietf.org/html/rfc8628#section-3.2\n  // Open authentication URL\n  const { verification_uri_complete, user_code, expires_in } = handle;\n  logMessage(\n    ctx,\n    `Visit ${verification_uri_complete} to finish logging in.\\n` +\n      `You should see the following code which expires in ${\n        expires_in % 60 === 0\n          ? `${expires_in / 60} minutes`\n          : `${expires_in} seconds`\n      }: ${user_code}`,\n  );\n  if (shouldOpen) {\n    shouldOpen = await promptYesNo(ctx, {\n      message: `Open the browser?`,\n      default: true,\n    });\n  }\n\n  if (shouldOpen) {\n    showSpinner(\n      ctx,\n      `Opening ${verification_uri_complete} in your browser to log in...\\n`,\n    );\n    try {\n      const p = await open(verification_uri_complete);\n      p.once(\"error\", () => {\n        changeSpinner(\n          ctx,\n          `Manually open ${verification_uri_complete} in your browser to log in.`,\n        );\n      });\n      changeSpinner(ctx, \"Waiting for the confirmation...\");\n    } catch {\n      logError(ctx, chalk.red(`Unable to open browser.`));\n      changeSpinner(\n        ctx,\n        `Manually open ${verification_uri_complete} in your browser to log in.`,\n      );\n    }\n  } else {\n    showSpinner(\n      ctx,\n      `Open ${verification_uri_complete} in your browser to log in.`,\n    );\n  }\n\n  // Device Access Token Request - https://tools.ietf.org/html/rfc8628#section-3.4\n  // Device Access Token Response - https://tools.ietf.org/html/rfc8628#section-3.5\n  try {\n    const tokens = await handle.poll();\n    if (typeof tokens.access_token === \"string\") {\n      return tokens.access_token;\n    } else {\n      // Unexpected error\n      // eslint-disable-next-line no-restricted-syntax\n      throw Error(\"Access token is missing\");\n    }\n  } catch (err: any) {\n    switch (err.error) {\n      case \"access_denied\": // end-user declined the device confirmation prompt, consent or rules failed\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: \"Access denied.\",\n          errForSentry: err,\n        });\n      case \"expired_token\": // end-user did not complete the interaction in time\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: \"Device flow expired.\",\n          errForSentry: err,\n        });\n      default: {\n        const message =\n          err instanceof errors.OPError\n            ? `Error = ${err.error}; error_description = ${err.error_description}`\n            : `Login failed with error: ${err}`;\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: message,\n          errForSentry: err,\n        });\n      }\n    }\n  }\n}\n\nasync function performPasswordAuthentication(\n  ctx: Context,\n  issuer: string,\n  clientId: string,\n  username: string,\n  password: string,\n): Promise<string> {\n  // Unfortunately, `openid-client` doesn't support the resource owner password credentials flow so we need to manually send the requests.\n  const options: Parameters<typeof throwingFetch>[1] = {\n    method: \"POST\",\n    headers: { \"Content-Type\": \"application/x-www-form-urlencoded\" },\n    body: new URLSearchParams({\n      grant_type: \"password\",\n      username: username,\n      password: password,\n      scope: SCOPE,\n      client_id: clientId,\n      audience: AUDIENCE,\n      // Note that there is no client secret provided, as Auth0 refuses to require it for untrusted apps.\n    }),\n  };\n\n  try {\n    const response = await throwingFetch(\n      new URL(\"/oauth/token\", issuer).href,\n      options,\n    );\n    const data = await response.json();\n    if (typeof data.access_token === \"string\") {\n      return data.access_token;\n    } else {\n      // Unexpected error\n      // eslint-disable-next-line no-restricted-syntax\n      throw Error(\"Access token is missing\");\n    }\n  } catch (err: any) {\n    logFailure(ctx, `Password flow failed: ${err}`);\n    if (err.response) {\n      logError(ctx, chalk.red(`${JSON.stringify(err.response.data)}`));\n    }\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      errForSentry: err,\n      printedMessage: null,\n    });\n  }\n}\n\nexport async function performLogin(\n  ctx: Context,\n  {\n    overrideAuthUrl,\n    overrideAuthClient,\n    overrideAuthUsername,\n    overrideAuthPassword,\n    overrideAccessToken,\n    loginFlow,\n    open,\n    acceptOptIns,\n    dumpAccessToken,\n    deviceName: deviceNameOverride,\n    anonymousId,\n  }: {\n    overrideAuthUrl?: string;\n    overrideAuthClient?: string;\n    overrideAuthUsername?: string;\n    overrideAuthPassword?: string;\n    overrideAccessToken?: string;\n    loginFlow?: \"auto\" | \"paste\" | \"poll\";\n    // default `true`\n    open?: boolean;\n    // default `false`\n    acceptOptIns?: boolean;\n    dumpAccessToken?: boolean;\n    deviceName?: string;\n    anonymousId?: string;\n  } = {},\n) {\n  loginFlow = loginFlow || \"auto\";\n  // Get access token from big-brain\n  // Default the device name to the hostname, but allow the user to change this if the terminal is interactive.\n  // On Macs, the `hostname()` may be a weirdly-truncated form of the computer name. Attempt to read the \"real\" name before falling back to hostname.\n  let deviceName = deviceNameOverride ?? \"\";\n  if (!deviceName && process.platform === \"darwin\") {\n    try {\n      deviceName = execSync(\"scutil --get ComputerName\").toString().trim();\n    } catch {\n      // Just fall back to the hostname default below.\n    }\n  }\n  if (!deviceName) {\n    deviceName = hostname();\n  }\n  if (!deviceNameOverride) {\n    logMessage(\n      ctx,\n      chalk.bold(`Welcome to developing with Convex, let's get you logged in.`),\n    );\n    deviceName = await promptString(ctx, {\n      message: \"Device name:\",\n      default: deviceName,\n    });\n  }\n\n  const issuer = overrideAuthUrl ?? \"https://auth.convex.dev\";\n  let auth0;\n  let accessToken: string;\n\n  if (loginFlow === \"paste\" || (loginFlow === \"auto\" && isWebContainer())) {\n    accessToken = await promptString(ctx, {\n      message:\n        \"Open https://dashboard.convex.dev/auth, log in and paste the token here:\",\n    });\n  } else {\n    try {\n      auth0 = await Issuer.discover(issuer);\n    } catch {\n      // Couldn't contact https://auth.convex.dev/.well-known/openid-configuration,\n      // proceed with manual auth.\n      accessToken = await promptString(ctx, {\n        message:\n          \"Open https://dashboard.convex.dev/auth, log in and paste the token here:\",\n      });\n    }\n  }\n\n  // typical path\n  if (auth0) {\n    const clientId = overrideAuthClient ?? \"HFtA247jp9iNs08NTLIB7JsNPMmRIyfi\";\n    const auth0Client = new auth0.Client({\n      client_id: clientId,\n      token_endpoint_auth_method: \"none\",\n      id_token_signed_response_alg: \"RS256\",\n    });\n\n    if (overrideAccessToken) {\n      accessToken = overrideAccessToken;\n    } else if (overrideAuthUsername && overrideAuthPassword) {\n      accessToken = await performPasswordAuthentication(\n        ctx,\n        issuer,\n        clientId,\n        overrideAuthUsername,\n        overrideAuthPassword,\n      );\n    } else {\n      accessToken = await performDeviceAuthorization(\n        ctx,\n        auth0Client,\n        open ?? true,\n      );\n    }\n  }\n\n  if (dumpAccessToken) {\n    logOutput(ctx, `${accessToken!}`);\n    return await ctx.crash({\n      exitCode: 0,\n      errorType: \"fatal\",\n      printedMessage: null,\n    });\n  }\n\n  const authorizeArgs: AuthorizeArgs = {\n    authnToken: accessToken!,\n    deviceName: deviceName,\n    anonymousId: anonymousId,\n  };\n  const data = await bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: \"authorize\",\n    data: authorizeArgs,\n  });\n  const globalConfig = { accessToken: data.accessToken };\n  try {\n    await modifyGlobalConfig(ctx, globalConfig);\n    const path = globalConfigPath();\n    logFinishedStep(ctx, `Saved credentials to ${formatPathForPrinting(path)}`);\n  } catch (err: unknown) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"invalid filesystem data\",\n      errForSentry: err,\n      printedMessage: null,\n    });\n  }\n\n  logVerbose(ctx, `performLogin: updating big brain auth after login`);\n  await updateBigBrainAuthAfterLogin(ctx, data.accessToken);\n\n  logVerbose(\n    ctx,\n    `performLogin: checking opt ins, acceptOptIns: ${acceptOptIns}`,\n  );\n  // Do opt in to TOS and Privacy Policy stuff\n  const shouldContinue = await optins(ctx, acceptOptIns ?? false);\n  if (!shouldContinue) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: null,\n    });\n  }\n}\n\n/// There are fields like version, but we keep them opaque\ntype OptIn = Record<string, unknown>;\n\ntype OptInToAccept = {\n  optIn: OptIn;\n  message: string;\n};\n\ntype AcceptOptInsArgs = {\n  optInsAccepted: OptIn[];\n};\n\n// Returns whether we can proceed or not.\nasync function optins(ctx: Context, acceptOptIns: boolean): Promise<boolean> {\n  const bbAuth = ctx.bigBrainAuth();\n  if (bbAuth === null) {\n    // This should never happen, but if we're not even logged in, we can't proceed.\n    return false;\n  }\n  switch (bbAuth.kind) {\n    case \"accessToken\":\n      break;\n    case \"projectKey\":\n    case \"previewDeployKey\":\n      // If we have a key configured as auth, we do not need to check opt ins.\n      return true;\n    default: {\n      const _exhaustivenessCheck: never = bbAuth;\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        errForSentry: `Unexpected auth kind ${(bbAuth as any).kind}`,\n        printedMessage: \"Hit an unexpected error while logging in.\",\n      });\n    }\n  }\n  const data = await bigBrainAPI({\n    ctx,\n    method: \"POST\",\n    url: \"check_opt_ins\",\n  });\n  if (data.optInsToAccept.length === 0) {\n    return true;\n  }\n  for (const optInToAccept of data.optInsToAccept) {\n    const confirmed =\n      acceptOptIns ||\n      (await promptYesNo(ctx, {\n        message: optInToAccept.message,\n      }));\n    if (!confirmed) {\n      logFailure(ctx, \"Please accept the Terms of Service to use Convex.\");\n      return Promise.resolve(false);\n    }\n  }\n\n  const optInsAccepted = data.optInsToAccept.map((o: OptInToAccept) => o.optIn);\n  const args: AcceptOptInsArgs = { optInsAccepted };\n  await bigBrainAPI({ ctx, method: \"POST\", url: \"accept_opt_ins\", data: args });\n  return true;\n}\n\nexport async function ensureLoggedIn(\n  ctx: Context,\n  options?: {\n    message?: string;\n    overrideAuthUrl?: string;\n    overrideAuthClient?: string;\n    overrideAuthUsername?: string;\n    overrideAuthPassword?: string;\n  },\n) {\n  const isLoggedIn = await checkAuthorization(ctx, false);\n  if (!isLoggedIn) {\n    if (options?.message) {\n      logMessage(ctx, options.message);\n    }\n    await performLogin(ctx, {\n      acceptOptIns: false,\n      overrideAuthUrl: options?.overrideAuthUrl,\n      overrideAuthClient: options?.overrideAuthClient,\n      overrideAuthUsername: options?.overrideAuthUsername,\n      overrideAuthPassword: options?.overrideAuthPassword,\n    });\n  }\n}\n"], "mappings": ";AAAA,SAAS,QAAoB,cAAc;AAC3C;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,OAAO,UAAU;AACjB,OAAO,WAAW;AAClB,SAAS,qBAAqB;AAC9B,SAAS,eAAe;AACxB;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,cAAc;AACvB,SAAS,gBAAgB;AACzB,SAAS,gBAAgB;AACzB,SAAS,cAAc,mBAAmB;AAC1C;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,oCAAoC;AAE7C,MAAM,QAAQ;AAId,MAAM,WAAW;AAGjB,OAAO,uBAAuB;AAAA,EAC5B,SAAS,SAAS,QAAQ,IAAI,yBAAyB,OAAO;AAChE,CAAC;AAQD,sBAAsB,mBACpB,KACA,cACkB;AAClB,QAAM,SAAS,IAAI,aAAa,GAAG,UAAU;AAC7C,MAAI,WAAW,MAAM;AACnB,WAAO;AAAA,EACT;AACA,MAAI;AACF,UAAM,OAAO,MAAM,MAAM,GAAG,aAAa,kBAAkB;AAAA,MACzD,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,eAAe;AAAA,QACf,iBAAiB,WAAW,OAAO;AAAA,MACrC;AAAA,IACF,CAAC;AAID,QAAI,KAAK,WAAW,KAAK;AACvB,aAAO;AAAA,IACT;AAAA,EACF,SAAS,GAAQ;AAEf;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,uBAAuB,KAAK,CAAC;AAAA,EAC5C;AAGA,QAAM,iBAAiB,MAAM,OAAO,KAAK,YAAY;AACrD,MAAI,CAAC,gBAAgB;AACnB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,eAAe,2BACb,KACA,aACA,YACiB;AA8BjB,MAAI;AACJ,MAAI;AACF,aAAS,MAAM,YAAY,oBAAoB;AAAA,MAC7C,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,QAAQ;AAEN,WAAO,aAAa,KAAK;AAAA,MACvB,SACE;AAAA,IACJ,CAAC;AAAA,EACH;AAIA,QAAM,EAAE,2BAA2B,WAAW,WAAW,IAAI;AAC7D;AAAA,IACE;AAAA,IACA,SAAS,yBAAyB;AAAA,qDAE9B,aAAa,OAAO,IAChB,GAAG,aAAa,EAAE,aAClB,GAAG,UAAU,UACnB,KAAK,SAAS;AAAA,EAClB;AACA,MAAI,YAAY;AACd,iBAAa,MAAM,YAAY,KAAK;AAAA,MAClC,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,MAAI,YAAY;AACd;AAAA,MACE;AAAA,MACA,WAAW,yBAAyB;AAAA;AAAA,IACtC;AACA,QAAI;AACF,YAAM,IAAI,MAAM,KAAK,yBAAyB;AAC9C,QAAE,KAAK,SAAS,MAAM;AACpB;AAAA,UACE;AAAA,UACA,iBAAiB,yBAAyB;AAAA,QAC5C;AAAA,MACF,CAAC;AACD,oBAAc,KAAK,iCAAiC;AAAA,IACtD,QAAQ;AACN,eAAS,KAAK,MAAM,IAAI,yBAAyB,CAAC;AAClD;AAAA,QACE;AAAA,QACA,iBAAiB,yBAAyB;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,OAAO;AACL;AAAA,MACE;AAAA,MACA,QAAQ,yBAAyB;AAAA,IACnC;AAAA,EACF;AAIA,MAAI;AACF,UAAM,SAAS,MAAM,OAAO,KAAK;AACjC,QAAI,OAAO,OAAO,iBAAiB,UAAU;AAC3C,aAAO,OAAO;AAAA,IAChB,OAAO;AAGL,YAAM,MAAM,yBAAyB;AAAA,IACvC;AAAA,EACF,SAAS,KAAU;AACjB,YAAQ,IAAI,OAAO;AAAA,MACjB,KAAK;AACH,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB,CAAC;AAAA,MACH,KAAK;AACH,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB,CAAC;AAAA,MACH,SAAS;AACP,cAAM,UACJ,eAAe,OAAO,UAClB,WAAW,IAAI,KAAK,yBAAyB,IAAI,iBAAiB,KAClE,4BAA4B,GAAG;AACrC,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAe,8BACb,KACA,QACA,UACA,UACA,UACiB;AAEjB,QAAM,UAA+C;AAAA,IACnD,QAAQ;AAAA,IACR,SAAS,EAAE,gBAAgB,oCAAoC;AAAA,IAC/D,MAAM,IAAI,gBAAgB;AAAA,MACxB,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA;AAAA,IAEZ,CAAC;AAAA,EACH;AAEA,MAAI;AACF,UAAM,WAAW,MAAM;AAAA,MACrB,IAAI,IAAI,gBAAgB,MAAM,EAAE;AAAA,MAChC;AAAA,IACF;AACA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,QAAI,OAAO,KAAK,iBAAiB,UAAU;AACzC,aAAO,KAAK;AAAA,IACd,OAAO;AAGL,YAAM,MAAM,yBAAyB;AAAA,IACvC;AAAA,EACF,SAAS,KAAU;AACjB,eAAW,KAAK,yBAAyB,GAAG,EAAE;AAC9C,QAAI,IAAI,UAAU;AAChB,eAAS,KAAK,MAAM,IAAI,GAAG,KAAK,UAAU,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;AAAA,IACjE;AACA,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACF;AAEA,sBAAsB,aACpB,KACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ;AACF,IAcI,CAAC,GACL;AACA,cAAY,aAAa;AAIzB,MAAI,aAAa,sBAAsB;AACvC,MAAI,CAAC,cAAc,QAAQ,aAAa,UAAU;AAChD,QAAI;AACF,mBAAa,SAAS,2BAA2B,EAAE,SAAS,EAAE,KAAK;AAAA,IACrE,QAAQ;AAAA,IAER;AAAA,EACF;AACA,MAAI,CAAC,YAAY;AACf,iBAAa,SAAS;AAAA,EACxB;AACA,MAAI,CAAC,oBAAoB;AACvB;AAAA,MACE;AAAA,MACA,MAAM,KAAK,6DAA6D;AAAA,IAC1E;AACA,iBAAa,MAAM,aAAa,KAAK;AAAA,MACnC,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,QAAM,SAAS,mBAAmB;AAClC,MAAI;AACJ,MAAI;AAEJ,MAAI,cAAc,WAAY,cAAc,UAAU,eAAe,GAAI;AACvE,kBAAc,MAAM,aAAa,KAAK;AAAA,MACpC,SACE;AAAA,IACJ,CAAC;AAAA,EACH,OAAO;AACL,QAAI;AACF,cAAQ,MAAM,OAAO,SAAS,MAAM;AAAA,IACtC,QAAQ;AAGN,oBAAc,MAAM,aAAa,KAAK;AAAA,QACpC,SACE;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AAGA,MAAI,OAAO;AACT,UAAM,WAAW,sBAAsB;AACvC,UAAM,cAAc,IAAI,MAAM,OAAO;AAAA,MACnC,WAAW;AAAA,MACX,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,IAChC,CAAC;AAED,QAAI,qBAAqB;AACvB,oBAAc;AAAA,IAChB,WAAW,wBAAwB,sBAAsB;AACvD,oBAAc,MAAM;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,oBAAc,MAAM;AAAA,QAClB;AAAA,QACA;AAAA,QACAA,SAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAEA,MAAI,iBAAiB;AACnB,cAAU,KAAK,GAAG,WAAY,EAAE;AAChC,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,QAAM,gBAA+B;AAAA,IACnC,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACA,QAAM,OAAO,MAAM,YAAY;AAAA,IAC7B;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AACD,QAAM,eAAe,EAAE,aAAa,KAAK,YAAY;AACrD,MAAI;AACF,UAAM,mBAAmB,KAAK,YAAY;AAC1C,UAAM,OAAO,iBAAiB;AAC9B,oBAAgB,KAAK,wBAAwB,sBAAsB,IAAI,CAAC,EAAE;AAAA,EAC5E,SAAS,KAAc;AACrB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,aAAW,KAAK,mDAAmD;AACnE,QAAM,6BAA6B,KAAK,KAAK,WAAW;AAExD;AAAA,IACE;AAAA,IACA,iDAAiD,YAAY;AAAA,EAC/D;AAEA,QAAM,iBAAiB,MAAM,OAAO,KAAK,gBAAgB,KAAK;AAC9D,MAAI,CAAC,gBAAgB;AACnB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACF;AAeA,eAAe,OAAO,KAAc,cAAyC;AAC3E,QAAM,SAAS,IAAI,aAAa;AAChC,MAAI,WAAW,MAAM;AAEnB,WAAO;AAAA,EACT;AACA,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK;AACH;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAEH,aAAO;AAAA,IACT,SAAS;AACP,YAAM,uBAA8B;AACpC,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,cAAc,wBAAyB,OAAe,IAAI;AAAA,QAC1D,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,OAAO,MAAM,YAAY;AAAA,IAC7B;AAAA,IACA,QAAQ;AAAA,IACR,KAAK;AAAA,EACP,CAAC;AACD,MAAI,KAAK,eAAe,WAAW,GAAG;AACpC,WAAO;AAAA,EACT;AACA,aAAW,iBAAiB,KAAK,gBAAgB;AAC/C,UAAM,YACJ,gBACC,MAAM,YAAY,KAAK;AAAA,MACtB,SAAS,cAAc;AAAA,IACzB,CAAC;AACH,QAAI,CAAC,WAAW;AACd,iBAAW,KAAK,mDAAmD;AACnE,aAAO,QAAQ,QAAQ,KAAK;AAAA,IAC9B;AAAA,EACF;AAEA,QAAM,iBAAiB,KAAK,eAAe,IAAI,CAAC,MAAqB,EAAE,KAAK;AAC5E,QAAM,OAAyB,EAAE,eAAe;AAChD,QAAM,YAAY,EAAE,KAAK,QAAQ,QAAQ,KAAK,kBAAkB,MAAM,KAAK,CAAC;AAC5E,SAAO;AACT;AAEA,sBAAsB,eACpB,KACA,SAOA;AACA,QAAM,aAAa,MAAM,mBAAmB,KAAK,KAAK;AACtD,MAAI,CAAC,YAAY;AACf,QAAI,SAAS,SAAS;AACpB,iBAAW,KAAK,QAAQ,OAAO;AAAA,IACjC;AACA,UAAM,aAAa,KAAK;AAAA,MACtB,cAAc;AAAA,MACd,iBAAiB,SAAS;AAAA,MAC1B,oBAAoB,SAAS;AAAA,MAC7B,sBAAsB,SAAS;AAAA,MAC/B,sBAAsB,SAAS;AAAA,IACjC,CAAC;AAAA,EACH;AACF;", "names": ["open"]}