{"version": 3, "sources": ["../../../../../../src/cli/lib/mcp/tools/env.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { ConvexTool } from \"./index.js\";\nimport { loadSelectedDeploymentCredentials } from \"../../api.js\";\nimport {\n  envSetInDeployment,\n  envRemoveInDeployment,\n  EnvVar,\n} from \"../../env.js\";\nimport { runSystemQuery } from \"../../run.js\";\nimport { getDeploymentSelection } from \"../../deploymentSelection.js\";\n\n// List Environment Variables\nconst envListInputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\n      \"Deployment selector (from the status tool) to list environment variables from.\",\n    ),\n});\n\nconst envListOutputSchema = z.object({\n  variables: z.array(\n    z.object({\n      name: z.string(),\n      value: z.string(),\n    }),\n  ),\n});\n\nexport const EnvListTool: ConvexTool<\n  typeof envListInputSchema,\n  typeof envListOutputSchema\n> = {\n  name: \"envList\",\n  description: \"List all environment variables in your Convex deployment.\",\n  inputSchema: envListInputSchema,\n  outputSchema: envListOutputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      deployment,\n    );\n    const variables = (await runSystemQuery(ctx, {\n      deploymentUrl: credentials.url,\n      adminKey: credentials.adminKey,\n      functionName: \"_system/cli/queryEnvironmentVariables\",\n      componentPath: undefined,\n      args: {},\n    })) as EnvVar[];\n    return { variables };\n  },\n};\n\n// Get Environment Variable\nconst envGetInputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\n      \"Deployment selector (from the status tool) to get environment variable from.\",\n    ),\n  name: z\n    .string()\n    .describe(\"The name of the environment variable to retrieve.\"),\n});\n\nconst envGetOutputSchema = z.object({\n  value: z.union([z.string(), z.null()]),\n});\n\nexport const EnvGetTool: ConvexTool<\n  typeof envGetInputSchema,\n  typeof envGetOutputSchema\n> = {\n  name: \"envGet\",\n  description:\n    \"Get a specific environment variable from your Convex deployment.\",\n  inputSchema: envGetInputSchema,\n  outputSchema: envGetOutputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      deployment,\n    );\n    const envVar = (await runSystemQuery(ctx, {\n      deploymentUrl: credentials.url,\n      adminKey: credentials.adminKey,\n      functionName: \"_system/cli/queryEnvironmentVariables:get\",\n      componentPath: undefined,\n      args: { name: args.name },\n    })) as { name: string; value: string } | null;\n    return { value: envVar?.value ?? null };\n  },\n};\n\n// Set Environment Variable\nconst envSetInputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\n      \"Deployment selector (from the status tool) to set environment variable on.\",\n    ),\n  name: z.string().describe(\"The name of the environment variable to set.\"),\n  value: z.string().describe(\"The value to set for the environment variable.\"),\n});\n\nconst envSetOutputSchema = z.object({\n  success: z.boolean(),\n});\n\nexport const EnvSetTool: ConvexTool<\n  typeof envSetInputSchema,\n  typeof envSetOutputSchema\n> = {\n  name: \"envSet\",\n  description: \"Set an environment variable in your Convex deployment.\",\n  inputSchema: envSetInputSchema,\n  outputSchema: envSetOutputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      deployment,\n    );\n    const deploymentInfo = {\n      deploymentUrl: credentials.url,\n      adminKey: credentials.adminKey,\n      deploymentNotice: \"\",\n    };\n    await envSetInDeployment(ctx, deploymentInfo, args.name, args.value);\n    return { success: true };\n  },\n};\n\n// Remove Environment Variable\nconst envRemoveInputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\n      \"Deployment selector (from the status tool) to remove environment variable from.\",\n    ),\n  name: z.string().describe(\"The name of the environment variable to remove.\"),\n});\n\nconst envRemoveOutputSchema = z.object({\n  success: z.boolean(),\n});\n\nexport const EnvRemoveTool: ConvexTool<\n  typeof envRemoveInputSchema,\n  typeof envRemoveOutputSchema\n> = {\n  name: \"envRemove\",\n  description: \"Remove an environment variable from your Convex deployment.\",\n  inputSchema: envRemoveInputSchema,\n  outputSchema: envRemoveOutputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      deployment,\n    );\n    const deploymentInfo = {\n      deploymentUrl: credentials.url,\n      adminKey: credentials.adminKey,\n      deploymentNotice: \"\",\n    };\n    await envRemoveInDeployment(ctx, deploymentInfo, args.name);\n    return { success: true };\n  },\n};\n"], "mappings": ";AAAA,SAAS,SAAS;AAElB,SAAS,yCAAyC;AAClD;AAAA,EACE;AAAA,EACA;AAAA,OAEK;AACP,SAAS,sBAAsB;AAC/B,SAAS,8BAA8B;AAGvC,MAAM,qBAAqB,EAAE,OAAO;AAAA,EAClC,oBAAoB,EACjB,OAAO,EACP;AAAA,IACC;AAAA,EACF;AACJ,CAAC;AAED,MAAM,sBAAsB,EAAE,OAAO;AAAA,EACnC,WAAW,EAAE;AAAA,IACX,EAAE,OAAO;AAAA,MACP,MAAM,EAAE,OAAO;AAAA,MACf,OAAO,EAAE,OAAO;AAAA,IAClB,CAAC;AAAA,EACH;AACF,CAAC;AAEM,aAAM,cAGT;AAAA,EACF,MAAM;AAAA,EACN,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,sBAAsB,MAAM,uBAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,YAAa,MAAM,eAAe,KAAK;AAAA,MAC3C,eAAe,YAAY;AAAA,MAC3B,UAAU,YAAY;AAAA,MACtB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,MAAM,CAAC;AAAA,IACT,CAAC;AACD,WAAO,EAAE,UAAU;AAAA,EACrB;AACF;AAGA,MAAM,oBAAoB,EAAE,OAAO;AAAA,EACjC,oBAAoB,EACjB,OAAO,EACP;AAAA,IACC;AAAA,EACF;AAAA,EACF,MAAM,EACH,OAAO,EACP,SAAS,mDAAmD;AACjE,CAAC;AAED,MAAM,qBAAqB,EAAE,OAAO;AAAA,EAClC,OAAO,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,CAAC;AAEM,aAAM,aAGT;AAAA,EACF,MAAM;AAAA,EACN,aACE;AAAA,EACF,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,sBAAsB,MAAM,uBAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,SAAU,MAAM,eAAe,KAAK;AAAA,MACxC,eAAe,YAAY;AAAA,MAC3B,UAAU,YAAY;AAAA,MACtB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,MAAM,EAAE,MAAM,KAAK,KAAK;AAAA,IAC1B,CAAC;AACD,WAAO,EAAE,OAAO,QAAQ,SAAS,KAAK;AAAA,EACxC;AACF;AAGA,MAAM,oBAAoB,EAAE,OAAO;AAAA,EACjC,oBAAoB,EACjB,OAAO,EACP;AAAA,IACC;AAAA,EACF;AAAA,EACF,MAAM,EAAE,OAAO,EAAE,SAAS,8CAA8C;AAAA,EACxE,OAAO,EAAE,OAAO,EAAE,SAAS,gDAAgD;AAC7E,CAAC;AAED,MAAM,qBAAqB,EAAE,OAAO;AAAA,EAClC,SAAS,EAAE,QAAQ;AACrB,CAAC;AAEM,aAAM,aAGT;AAAA,EACF,MAAM;AAAA,EACN,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,sBAAsB,MAAM,uBAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,iBAAiB;AAAA,MACrB,eAAe,YAAY;AAAA,MAC3B,UAAU,YAAY;AAAA,MACtB,kBAAkB;AAAA,IACpB;AACA,UAAM,mBAAmB,KAAK,gBAAgB,KAAK,MAAM,KAAK,KAAK;AACnE,WAAO,EAAE,SAAS,KAAK;AAAA,EACzB;AACF;AAGA,MAAM,uBAAuB,EAAE,OAAO;AAAA,EACpC,oBAAoB,EACjB,OAAO,EACP;AAAA,IACC;AAAA,EACF;AAAA,EACF,MAAM,EAAE,OAAO,EAAE,SAAS,iDAAiD;AAC7E,CAAC;AAED,MAAM,wBAAwB,EAAE,OAAO;AAAA,EACrC,SAAS,EAAE,QAAQ;AACrB,CAAC;AAEM,aAAM,gBAGT;AAAA,EACF,MAAM;AAAA,EACN,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,sBAAsB,MAAM,uBAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,iBAAiB;AAAA,MACrB,eAAe,YAAY;AAAA,MAC3B,UAAU,YAAY;AAAA,MACtB,kBAAkB;AAAA,IACpB;AACA,UAAM,sBAAsB,KAAK,gBAAgB,KAAK,IAAI;AAC1D,WAAO,EAAE,SAAS,KAAK;AAAA,EACzB;AACF;", "names": []}