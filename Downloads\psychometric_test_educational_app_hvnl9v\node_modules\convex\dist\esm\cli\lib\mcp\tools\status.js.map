{"version": 3, "sources": ["../../../../../../src/cli/lib/mcp/tools/status.ts"], "sourcesContent": ["import { encodeDeploymentSelector, RequestContext } from \"../requestContext.js\";\nimport {\n  DeploymentSelectionWithinProject,\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"../../api.js\";\nimport { z } from \"zod\";\nimport { ConvexTool } from \"./index.js\";\nimport { deploymentDashboardUrlPage } from \"../../../lib/dashboard.js\";\nimport { getDeploymentSelection } from \"../../../lib/deploymentSelection.js\";\n\nconst projectDirDescription = `\nThe root directory of the Convex project. This is usually the editor's workspace directory\nand often includes the 'package.json' file and the 'convex/' folder.\n\nPass this option unless explicitly instructed not to.\n`;\n\nconst inputSchema = z.object({\n  projectDir: z.string().optional().describe(projectDirDescription),\n});\nconst outputSchema = z.object({\n  availableDeployments: z.array(\n    z.object({\n      kind: z.string(),\n      deploymentSelector: z.string(),\n      url: z.string(),\n      dashboardUrl: z.string().optional(),\n    }),\n  ),\n});\n\nconst description = `\nGet all available deployments for a given Convex project directory.\n\nUse this tool to find the deployment selector, URL, and dashboard URL for each\ndeployment associated with the project. Pass the deployment selector to other\ntools to target a specific deployment.\n\nWhen deployed to Convex Cloud, projects have a development ({\"kind\": \"ownDev\"}) and\nproduction ({\"kind\": \"prod\"}) deployment. Generally default to using the development\ndeployment unless you'd specifically like to debug issues in production.\n\nWhen running locally, there will be a single \"urlWithAdminKey\" deployment.\n`.trim();\n\nexport const StatusTool: ConvexTool<typeof inputSchema, typeof outputSchema> = {\n  name: \"status\",\n  description,\n  inputSchema,\n  outputSchema,\n  handler: async (ctx: RequestContext, input) => {\n    const projectDir = input.projectDir ?? ctx.options.projectDir;\n    if (projectDir === undefined) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage:\n          \"No project directory provided. Either provide the `projectDir` argument or configure the MCP server with the `--project-dir` flag.\",\n      });\n    }\n    process.chdir(projectDir);\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, ctx.options);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      selectionWithinProject,\n    );\n    let availableDeployments = [\n      {\n        kind: selectionWithinProject.kind,\n        deploymentSelector: encodeDeploymentSelector(\n          projectDir,\n          selectionWithinProject,\n        ),\n        url: credentials.url,\n        dashboardUrl:\n          credentials.deploymentFields?.deploymentName &&\n          deploymentDashboardUrlPage(\n            credentials.deploymentFields.deploymentName,\n            \"\",\n          ),\n      },\n    ];\n    // Also get the prod cloud deployment if we're using a cloud-hosted dev-deployment\n    if (\n      selectionWithinProject.kind === \"ownDev\" &&\n      !(\n        deploymentSelection.kind === \"existingDeployment\" &&\n        deploymentSelection.deploymentToActOn.deploymentFields === null\n      )\n    ) {\n      const prodDeployment: DeploymentSelectionWithinProject = { kind: \"prod\" };\n      const prodCredentials = await loadSelectedDeploymentCredentials(\n        ctx,\n        deploymentSelection,\n        prodDeployment,\n      );\n      if (\n        prodCredentials.deploymentFields?.deploymentName &&\n        prodCredentials.deploymentFields.deploymentType\n      ) {\n        availableDeployments.push({\n          kind: prodDeployment.kind,\n          deploymentSelector: encodeDeploymentSelector(\n            projectDir,\n            prodDeployment,\n          ),\n          url: prodCredentials.url,\n          dashboardUrl: deploymentDashboardUrlPage(\n            prodCredentials.deploymentFields.deploymentName,\n            \"\",\n          ),\n        });\n      }\n    }\n    if (ctx.productionDeploymentsDisabled) {\n      availableDeployments = availableDeployments.filter(\n        (d) => d.kind !== \"prod\",\n      );\n    }\n    return { availableDeployments };\n  },\n};\n"], "mappings": ";AAAA,SAAS,gCAAgD;AACzD;AAAA,EAEE;AAAA,EACA;AAAA,OACK;AACP,SAAS,SAAS;AAElB,SAAS,kCAAkC;AAC3C,SAAS,8BAA8B;AAEvC,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAO9B,MAAM,cAAc,EAAE,OAAO;AAAA,EAC3B,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,qBAAqB;AAClE,CAAC;AACD,MAAM,eAAe,EAAE,OAAO;AAAA,EAC5B,sBAAsB,EAAE;AAAA,IACtB,EAAE,OAAO;AAAA,MACP,MAAM,EAAE,OAAO;AAAA,MACf,oBAAoB,EAAE,OAAO;AAAA,MAC7B,KAAK,EAAE,OAAO;AAAA,MACd,cAAc,EAAE,OAAO,EAAE,SAAS;AAAA,IACpC,CAAC;AAAA,EACH;AACF,CAAC;AAED,MAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYlB,KAAK;AAEA,aAAM,aAAkE;AAAA,EAC7E,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,OAAO,KAAqB,UAAU;AAC7C,UAAM,aAAa,MAAM,cAAc,IAAI,QAAQ;AACnD,QAAI,eAAe,QAAW;AAC5B,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,MACJ,CAAC;AAAA,IACH;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,yBACJ,MAAM,4CAA4C,KAAK,IAAI,OAAO;AACpE,UAAM,sBAAsB,MAAM,uBAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,uBAAuB;AAAA,MACzB;AAAA,QACE,MAAM,uBAAuB;AAAA,QAC7B,oBAAoB;AAAA,UAClB;AAAA,UACA;AAAA,QACF;AAAA,QACA,KAAK,YAAY;AAAA,QACjB,cACE,YAAY,kBAAkB,kBAC9B;AAAA,UACE,YAAY,iBAAiB;AAAA,UAC7B;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AAEA,QACE,uBAAuB,SAAS,YAChC,EACE,oBAAoB,SAAS,wBAC7B,oBAAoB,kBAAkB,qBAAqB,OAE7D;AACA,YAAM,iBAAmD,EAAE,MAAM,OAAO;AACxE,YAAM,kBAAkB,MAAM;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,UACE,gBAAgB,kBAAkB,kBAClC,gBAAgB,iBAAiB,gBACjC;AACA,6BAAqB,KAAK;AAAA,UACxB,MAAM,eAAe;AAAA,UACrB,oBAAoB;AAAA,YAClB;AAAA,YACA;AAAA,UACF;AAAA,UACA,KAAK,gBAAgB;AAAA,UACrB,cAAc;AAAA,YACZ,gBAAgB,iBAAiB;AAAA,YACjC;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,IAAI,+BAA+B;AACrC,6BAAuB,qBAAqB;AAAA,QAC1C,CAAC,MAAM,EAAE,SAAS;AAAA,MACpB;AAAA,IACF;AACA,WAAO,EAAE,qBAAqB;AAAA,EAChC;AACF;", "names": []}