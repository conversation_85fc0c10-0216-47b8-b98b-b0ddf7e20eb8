{"version": 3, "sources": ["../../../../src/cli/lib/networkTest.ts"], "sourcesContent": ["import {\n  Context,\n  logFailure,\n  logFinishedStep,\n  logMessage,\n  logVerbose,\n  logWarning,\n} from \"../../bundler/context.js\";\nimport chalk from \"chalk\";\nimport * as net from \"net\";\nimport * as dns from \"dns\";\nimport * as crypto from \"crypto\";\nimport {\n  bareDeploymentFetch,\n  formatDuration,\n  formatSize,\n  ThrowingFetchError,\n} from \"./utils/utils.js\";\nimport ws from \"ws\";\nimport { BaseConvexClient } from \"../../browser/index.js\";\nimport { Logger } from \"../../browser/logging.js\";\nconst ipFamilyNumbers = { ipv4: 4, ipv6: 6, auto: 0 } as const;\nconst ipFamilyNames = { 4: \"ipv4\", 6: \"ipv6\", 0: \"auto\" } as const;\n\nexport async function runNetworkTestOnUrl(\n  ctx: Context,\n  { url, adminKey }: { url: string; adminKey: string | null },\n  options: {\n    ipFamily?: string;\n    speedTest?: boolean;\n  },\n) {\n  // First, check DNS to see if we can resolve the URL's hostname.\n  await checkDns(ctx, url);\n\n  // Second, check to see if we can open a TCP connection to the hostname.\n  await checkTcp(ctx, url, options.ipFamily ?? \"auto\");\n\n  // Third, do a simple HTTPS request and check that we receive a 200.\n  await checkHttp(ctx, url);\n\n  // Fourth, check that we can open a WebSocket connection to the hostname.\n  await checkWs(ctx, { url, adminKey });\n\n  // Fifth, check a small echo request, much smaller than most networks' MTU.\n  await checkEcho(ctx, url, 128);\n\n  // Finally, try a large echo request, much larger than most networks' MTU.\n  await checkEcho(ctx, url, 4 * 1024 * 1024);\n  // Also do a 64MiB echo test if the user has requested a speed test.\n  if (options.speedTest) {\n    await checkEcho(ctx, url, 64 * 1024 * 1024);\n  }\n\n  logFinishedStep(ctx, \"Network test passed.\");\n}\n\nasync function checkDns(ctx: Context, url: string) {\n  try {\n    const hostname = new URL(\"/\", url).hostname;\n    const start = performance.now();\n    type DnsResult = { duration: number; address: string; family: number };\n    const result = await new Promise<DnsResult>((resolve, reject) => {\n      dns.lookup(hostname, (err, address, family) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve({ duration: performance.now() - start, address, family });\n        }\n      });\n    });\n    logMessage(\n      ctx,\n      `${chalk.green(`✔`)} OK: DNS lookup => ${result.address}:${\n        ipFamilyNames[result.family as keyof typeof ipFamilyNames]\n      } (${formatDuration(result.duration)})`,\n    );\n  } catch (e: any) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      printedMessage: `FAIL: DNS lookup (${e})`,\n    });\n  }\n}\n\nasync function checkTcp(ctx: Context, urlString: string, ipFamilyOpt: string) {\n  const url = new URL(urlString);\n  if (url.protocol === \"http:\") {\n    const port = Number.parseInt(url.port || \"80\");\n    await checkTcpHostPort(ctx, url.hostname, port, ipFamilyOpt);\n  } else if (url.protocol === \"https:\") {\n    const port = Number.parseInt(url.port || \"443\");\n    await checkTcpHostPort(ctx, url.hostname, port, ipFamilyOpt);\n    // If we didn't specify a port, also try port 80.\n    if (!url.port) {\n      await checkTcpHostPort(ctx, url.hostname, 80, ipFamilyOpt);\n    }\n  } else {\n    // eslint-disable-next-line no-restricted-syntax\n    throw new Error(`Unknown protocol: ${url.protocol}`);\n  }\n}\n\nasync function checkTcpHostPort(\n  ctx: Context,\n  host: string,\n  port: number,\n  ipFamilyOpt: string,\n) {\n  const ipFamily = ipFamilyNumbers[ipFamilyOpt as keyof typeof ipFamilyNumbers];\n  const tcpString =\n    `TCP` + (ipFamilyOpt === \"auto\" ? \"\" : `/${ipFamilyOpt} ${host}:${port}`);\n  try {\n    const start = performance.now();\n    const duration = await new Promise<number>((resolve, reject) => {\n      const socket = net.connect(\n        {\n          host,\n          port,\n          noDelay: true,\n          family: ipFamily,\n        },\n        () => resolve(performance.now() - start),\n      );\n      socket.on(\"error\", (e) => reject(e));\n    });\n    logMessage(\n      ctx,\n      `${chalk.green(`✔`)} OK: ${tcpString} connect (${formatDuration(\n        duration,\n      )})`,\n    );\n  } catch (e: any) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      printedMessage: `FAIL: ${tcpString} connect (${e})`,\n    });\n  }\n}\n\nasync function checkHttp(ctx: Context, urlString: string) {\n  const url = new URL(urlString);\n  const isHttps = url.protocol === \"https:\";\n  if (isHttps) {\n    url.protocol = \"http:\";\n    url.port = \"80\";\n    await checkHttpOnce(ctx, \"HTTP\", url.toString(), false);\n  }\n  await checkHttpOnce(ctx, isHttps ? \"HTTPS\" : \"HTTP\", urlString, true);\n}\n\n// Be sure to test this function against *prod* (with both HTTP & HTTPS) when\n// making changes.\nasync function checkHttpOnce(\n  ctx: Context,\n  name: string,\n  url: string,\n  allowRedirects: boolean,\n) {\n  const start = performance.now();\n  try {\n    // Be sure to use the same `deploymentFetch` we use elsewhere so we're actually\n    // getting coverage of our network stack.\n    const fetch = bareDeploymentFetch(ctx, { deploymentUrl: url });\n    const instanceNameUrl = new URL(\"/instance_name\", url);\n    const resp = await fetch(instanceNameUrl.toString(), {\n      redirect: allowRedirects ? \"follow\" : \"manual\",\n    });\n    if (resp.status !== 200) {\n      // eslint-disable-next-line no-restricted-syntax\n      throw new Error(`Unexpected status code: ${resp.status}`);\n    }\n  } catch (e: any) {\n    // Redirects return a 301, which causes `bareDeploymentFetch` to throw an\n    // ThrowingFetchError. Catch that here and succeed if we're not following\n    // redirects.\n    const isOkayRedirect =\n      !allowRedirects &&\n      e instanceof ThrowingFetchError &&\n      e.response.status === 301;\n    if (!isOkayRedirect) {\n      return ctx.crash({\n        exitCode: 1,\n        errorType: \"transient\",\n        printedMessage: `FAIL: ${name} check (${e})`,\n      });\n    }\n  }\n  const duration = performance.now() - start;\n  logMessage(\n    ctx,\n    `${chalk.green(`✔`)} OK: ${name} check (${formatDuration(duration)})`,\n  );\n}\n\nasync function checkWs(\n  ctx: Context,\n  { url, adminKey }: { url: string; adminKey: string | null },\n) {\n  if (adminKey === null) {\n    logWarning(\n      ctx,\n      \"Skipping WebSocket check because no admin key was provided.\",\n    );\n    return;\n  }\n  let queryPromiseResolver: ((value: string) => void) | null = null;\n  const queryPromise = new Promise<string | null>((resolve) => {\n    queryPromiseResolver = resolve;\n  });\n  const logger = new Logger({\n    verbose: process.env.CONVEX_VERBOSE !== undefined,\n  });\n  logger.addLogLineListener((level, ...args) => {\n    switch (level) {\n      case \"debug\":\n        logVerbose(ctx, ...args);\n        break;\n      case \"info\":\n        logVerbose(ctx, ...args);\n        break;\n      case \"warn\":\n        logWarning(ctx, ...args);\n        break;\n      case \"error\":\n        // TODO: logFailure is a little hard to use here because it also interacts\n        // with the spinner and requires a string.\n        logWarning(ctx, ...args);\n        break;\n    }\n  });\n  const convexClient = new BaseConvexClient(\n    url,\n    (updatedQueries) => {\n      for (const queryToken of updatedQueries) {\n        const result = convexClient.localQueryResultByToken(queryToken);\n        if (typeof result === \"string\" && queryPromiseResolver !== null) {\n          queryPromiseResolver(result);\n          queryPromiseResolver = null;\n        }\n      }\n    },\n    {\n      webSocketConstructor: ws as unknown as typeof WebSocket,\n      unsavedChangesWarning: false,\n      logger,\n    },\n  );\n  convexClient.setAdminAuth(adminKey);\n  convexClient.subscribe(\"_system/cli/convexUrl:cloudUrl\", {});\n  const racePromise = Promise.race([\n    queryPromise,\n    new Promise((resolve) => setTimeout(() => resolve(null), 10000)),\n  ]);\n  const cloudUrl = await racePromise;\n  if (cloudUrl === null) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      printedMessage: \"FAIL: Failed to connect to deployment over WebSocket.\",\n    });\n  } else {\n    logMessage(\n      ctx,\n      `${chalk.green(`✔`)} OK: WebSocket connection established.`,\n    );\n  }\n}\n\nasync function checkEcho(ctx: Context, url: string, size: number) {\n  try {\n    const start = performance.now();\n    const fetch = bareDeploymentFetch(ctx, {\n      deploymentUrl: url,\n      onError: (err) => {\n        logFailure(\n          ctx,\n          chalk.red(`FAIL: echo ${formatSize(size)} (${err}), retrying...`),\n        );\n      },\n    });\n    const echoUrl = new URL(`/echo`, url);\n    const data = crypto.randomBytes(size);\n    const resp = await fetch(echoUrl.toString(), {\n      body: data,\n      method: \"POST\",\n    });\n    if (resp.status !== 200) {\n      // eslint-disable-next-line no-restricted-syntax\n      throw new Error(`Unexpected status code: ${resp.status}`);\n    }\n    const respData = await resp.arrayBuffer();\n    if (!data.equals(Buffer.from(respData))) {\n      // eslint-disable-next-line no-restricted-syntax\n      throw new Error(`Response data mismatch`);\n    }\n    const duration = performance.now() - start;\n    const bytesPerSecond = size / (duration / 1000);\n    logMessage(\n      ctx,\n      `${chalk.green(`✔`)} OK: echo ${formatSize(size)} (${formatDuration(\n        duration,\n      )}, ${formatSize(bytesPerSecond)}/s)`,\n    );\n  } catch (e: any) {\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"transient\",\n      printedMessage: `FAIL: echo ${formatSize(size)} (${e})`,\n    });\n  }\n}\n\nexport async function withTimeout<T>(\n  ctx: Context,\n  name: string,\n  timeoutMs: number,\n  f: Promise<T>,\n) {\n  let timer: NodeJS.Timeout | null = null;\n  try {\n    type TimeoutPromise = { kind: \"ok\"; result: T } | { kind: \"timeout\" };\n    const result = await Promise.race<TimeoutPromise>([\n      f.then((r) => {\n        return { kind: \"ok\", result: r };\n      }),\n      new Promise((resolve) => {\n        timer = setTimeout(() => {\n          resolve({ kind: \"timeout\" as const });\n          timer = null;\n        }, timeoutMs);\n      }),\n    ]);\n    if (result.kind === \"ok\") {\n      return result.result;\n    } else {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"transient\",\n        printedMessage: `FAIL: ${name} timed out after ${formatDuration(timeoutMs)}.`,\n      });\n    }\n  } finally {\n    if (timer !== null) {\n      clearTimeout(timer);\n    }\n  }\n}\n"], "mappings": ";AAAA;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,OAAO,WAAW;AAClB,YAAY,SAAS;AACrB,YAAY,SAAS;AACrB,YAAY,YAAY;AACxB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,OAAO,QAAQ;AACf,SAAS,wBAAwB;AACjC,SAAS,cAAc;AACvB,MAAM,kBAAkB,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE;AACpD,MAAM,gBAAgB,EAAE,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO;AAExD,sBAAsB,oBACpB,KACA,EAAE,KAAK,SAAS,GAChB,SAIA;AAEA,QAAM,SAAS,KAAK,GAAG;AAGvB,QAAM,SAAS,KAAK,KAAK,QAAQ,YAAY,MAAM;AAGnD,QAAM,UAAU,KAAK,GAAG;AAGxB,QAAM,QAAQ,KAAK,EAAE,KAAK,SAAS,CAAC;AAGpC,QAAM,UAAU,KAAK,KAAK,GAAG;AAG7B,QAAM,UAAU,KAAK,KAAK,IAAI,OAAO,IAAI;AAEzC,MAAI,QAAQ,WAAW;AACrB,UAAM,UAAU,KAAK,KAAK,KAAK,OAAO,IAAI;AAAA,EAC5C;AAEA,kBAAgB,KAAK,sBAAsB;AAC7C;AAEA,eAAe,SAAS,KAAc,KAAa;AACjD,MAAI;AACF,UAAM,WAAW,IAAI,IAAI,KAAK,GAAG,EAAE;AACnC,UAAM,QAAQ,YAAY,IAAI;AAE9B,UAAM,SAAS,MAAM,IAAI,QAAmB,CAAC,SAAS,WAAW;AAC/D,UAAI,OAAO,UAAU,CAAC,KAAK,SAAS,WAAW;AAC7C,YAAI,KAAK;AACP,iBAAO,GAAG;AAAA,QACZ,OAAO;AACL,kBAAQ,EAAE,UAAU,YAAY,IAAI,IAAI,OAAO,SAAS,OAAO,CAAC;AAAA,QAClE;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD;AAAA,MACE;AAAA,MACA,GAAG,MAAM,MAAM,QAAG,CAAC,sBAAsB,OAAO,OAAO,IACrD,cAAc,OAAO,MAAoC,CAC3D,KAAK,eAAe,OAAO,QAAQ,CAAC;AAAA,IACtC;AAAA,EACF,SAAS,GAAQ;AACf,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,qBAAqB,CAAC;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAEA,eAAe,SAAS,KAAc,WAAmB,aAAqB;AAC5E,QAAM,MAAM,IAAI,IAAI,SAAS;AAC7B,MAAI,IAAI,aAAa,SAAS;AAC5B,UAAM,OAAO,OAAO,SAAS,IAAI,QAAQ,IAAI;AAC7C,UAAM,iBAAiB,KAAK,IAAI,UAAU,MAAM,WAAW;AAAA,EAC7D,WAAW,IAAI,aAAa,UAAU;AACpC,UAAM,OAAO,OAAO,SAAS,IAAI,QAAQ,KAAK;AAC9C,UAAM,iBAAiB,KAAK,IAAI,UAAU,MAAM,WAAW;AAE3D,QAAI,CAAC,IAAI,MAAM;AACb,YAAM,iBAAiB,KAAK,IAAI,UAAU,IAAI,WAAW;AAAA,IAC3D;AAAA,EACF,OAAO;AAEL,UAAM,IAAI,MAAM,qBAAqB,IAAI,QAAQ,EAAE;AAAA,EACrD;AACF;AAEA,eAAe,iBACb,KACA,MACA,MACA,aACA;AACA,QAAM,WAAW,gBAAgB,WAA2C;AAC5E,QAAM,YACJ,SAAS,gBAAgB,SAAS,KAAK,IAAI,WAAW,IAAI,IAAI,IAAI,IAAI;AACxE,MAAI;AACF,UAAM,QAAQ,YAAY,IAAI;AAC9B,UAAM,WAAW,MAAM,IAAI,QAAgB,CAAC,SAAS,WAAW;AAC9D,YAAM,SAAS,IAAI;AAAA,QACjB;AAAA,UACE;AAAA,UACA;AAAA,UACA,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,MAAM,QAAQ,YAAY,IAAI,IAAI,KAAK;AAAA,MACzC;AACA,aAAO,GAAG,SAAS,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,IACrC,CAAC;AACD;AAAA,MACE;AAAA,MACA,GAAG,MAAM,MAAM,QAAG,CAAC,QAAQ,SAAS,aAAa;AAAA,QAC/C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,GAAQ;AACf,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,SAAS,SAAS,aAAa,CAAC;AAAA,IAClD,CAAC;AAAA,EACH;AACF;AAEA,eAAe,UAAU,KAAc,WAAmB;AACxD,QAAM,MAAM,IAAI,IAAI,SAAS;AAC7B,QAAM,UAAU,IAAI,aAAa;AACjC,MAAI,SAAS;AACX,QAAI,WAAW;AACf,QAAI,OAAO;AACX,UAAM,cAAc,KAAK,QAAQ,IAAI,SAAS,GAAG,KAAK;AAAA,EACxD;AACA,QAAM,cAAc,KAAK,UAAU,UAAU,QAAQ,WAAW,IAAI;AACtE;AAIA,eAAe,cACb,KACA,MACA,KACA,gBACA;AACA,QAAM,QAAQ,YAAY,IAAI;AAC9B,MAAI;AAGF,UAAM,QAAQ,oBAAoB,KAAK,EAAE,eAAe,IAAI,CAAC;AAC7D,UAAM,kBAAkB,IAAI,IAAI,kBAAkB,GAAG;AACrD,UAAM,OAAO,MAAM,MAAM,gBAAgB,SAAS,GAAG;AAAA,MACnD,UAAU,iBAAiB,WAAW;AAAA,IACxC,CAAC;AACD,QAAI,KAAK,WAAW,KAAK;AAEvB,YAAM,IAAI,MAAM,2BAA2B,KAAK,MAAM,EAAE;AAAA,IAC1D;AAAA,EACF,SAAS,GAAQ;AAIf,UAAM,iBACJ,CAAC,kBACD,aAAa,sBACb,EAAE,SAAS,WAAW;AACxB,QAAI,CAAC,gBAAgB;AACnB,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,SAAS,IAAI,WAAW,CAAC;AAAA,MAC3C,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,WAAW,YAAY,IAAI,IAAI;AACrC;AAAA,IACE;AAAA,IACA,GAAG,MAAM,MAAM,QAAG,CAAC,QAAQ,IAAI,WAAW,eAAe,QAAQ,CAAC;AAAA,EACpE;AACF;AAEA,eAAe,QACb,KACA,EAAE,KAAK,SAAS,GAChB;AACA,MAAI,aAAa,MAAM;AACrB;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA;AAAA,EACF;AACA,MAAI,uBAAyD;AAC7D,QAAM,eAAe,IAAI,QAAuB,CAAC,YAAY;AAC3D,2BAAuB;AAAA,EACzB,CAAC;AACD,QAAM,SAAS,IAAI,OAAO;AAAA,IACxB,SAAS,QAAQ,IAAI,mBAAmB;AAAA,EAC1C,CAAC;AACD,SAAO,mBAAmB,CAAC,UAAU,SAAS;AAC5C,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,mBAAW,KAAK,GAAG,IAAI;AACvB;AAAA,MACF,KAAK;AACH,mBAAW,KAAK,GAAG,IAAI;AACvB;AAAA,MACF,KAAK;AACH,mBAAW,KAAK,GAAG,IAAI;AACvB;AAAA,MACF,KAAK;AAGH,mBAAW,KAAK,GAAG,IAAI;AACvB;AAAA,IACJ;AAAA,EACF,CAAC;AACD,QAAM,eAAe,IAAI;AAAA,IACvB;AAAA,IACA,CAAC,mBAAmB;AAClB,iBAAW,cAAc,gBAAgB;AACvC,cAAM,SAAS,aAAa,wBAAwB,UAAU;AAC9D,YAAI,OAAO,WAAW,YAAY,yBAAyB,MAAM;AAC/D,+BAAqB,MAAM;AAC3B,iCAAuB;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,eAAa,aAAa,QAAQ;AAClC,eAAa,UAAU,kCAAkC,CAAC,CAAC;AAC3D,QAAM,cAAc,QAAQ,KAAK;AAAA,IAC/B;AAAA,IACA,IAAI,QAAQ,CAAC,YAAY,WAAW,MAAM,QAAQ,IAAI,GAAG,GAAK,CAAC;AAAA,EACjE,CAAC;AACD,QAAM,WAAW,MAAM;AACvB,MAAI,aAAa,MAAM;AACrB,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH,OAAO;AACL;AAAA,MACE;AAAA,MACA,GAAG,MAAM,MAAM,QAAG,CAAC;AAAA,IACrB;AAAA,EACF;AACF;AAEA,eAAe,UAAU,KAAc,KAAa,MAAc;AAChE,MAAI;AACF,UAAM,QAAQ,YAAY,IAAI;AAC9B,UAAM,QAAQ,oBAAoB,KAAK;AAAA,MACrC,eAAe;AAAA,MACf,SAAS,CAAC,QAAQ;AAChB;AAAA,UACE;AAAA,UACA,MAAM,IAAI,cAAc,WAAW,IAAI,CAAC,KAAK,GAAG,gBAAgB;AAAA,QAClE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,UAAU,IAAI,IAAI,SAAS,GAAG;AACpC,UAAM,OAAO,OAAO,YAAY,IAAI;AACpC,UAAM,OAAO,MAAM,MAAM,QAAQ,SAAS,GAAG;AAAA,MAC3C,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,CAAC;AACD,QAAI,KAAK,WAAW,KAAK;AAEvB,YAAM,IAAI,MAAM,2BAA2B,KAAK,MAAM,EAAE;AAAA,IAC1D;AACA,UAAM,WAAW,MAAM,KAAK,YAAY;AACxC,QAAI,CAAC,KAAK,OAAO,OAAO,KAAK,QAAQ,CAAC,GAAG;AAEvC,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AACA,UAAM,WAAW,YAAY,IAAI,IAAI;AACrC,UAAM,iBAAiB,QAAQ,WAAW;AAC1C;AAAA,MACE;AAAA,MACA,GAAG,MAAM,MAAM,QAAG,CAAC,aAAa,WAAW,IAAI,CAAC,KAAK;AAAA,QACnD;AAAA,MACF,CAAC,KAAK,WAAW,cAAc,CAAC;AAAA,IAClC;AAAA,EACF,SAAS,GAAQ;AACf,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,cAAc,WAAW,IAAI,CAAC,KAAK,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAEA,sBAAsB,YACpB,KACA,MACA,WACA,GACA;AACA,MAAI,QAA+B;AACnC,MAAI;AAEF,UAAM,SAAS,MAAM,QAAQ,KAAqB;AAAA,MAChD,EAAE,KAAK,CAAC,MAAM;AACZ,eAAO,EAAE,MAAM,MAAM,QAAQ,EAAE;AAAA,MACjC,CAAC;AAAA,MACD,IAAI,QAAQ,CAAC,YAAY;AACvB,gBAAQ,WAAW,MAAM;AACvB,kBAAQ,EAAE,MAAM,UAAmB,CAAC;AACpC,kBAAQ;AAAA,QACV,GAAG,SAAS;AAAA,MACd,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,SAAS,MAAM;AACxB,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,SAAS,IAAI,oBAAoB,eAAe,SAAS,CAAC;AAAA,MAC5E,CAAC;AAAA,IACH;AAAA,EACF,UAAE;AACA,QAAI,UAAU,MAAM;AAClB,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF;AACF;", "names": []}