{"version": 3, "sources": ["../../../../src/cli/lib/watch.ts"], "sourcesContent": ["import chokidar from \"chokidar\";\nimport path from \"path\";\nimport { Observations, RecordingFs, WatchEvent } from \"../../bundler/fs.js\";\nimport {\n  BigBrainAuth,\n  Context,\n  ErrorType,\n  logFailure,\n} from \"../../bundler/context.js\";\nimport * as Sentry from \"@sentry/node\";\nimport { Ora } from \"ora\";\n\nexport class Watcher {\n  private watch: chokidar.FSWatcher;\n  private readyCb: Promise<void>;\n\n  private bufferedEvents: WatchEvent[];\n  private waiters: (() => void)[];\n\n  constructor(observations: Observations) {\n    this.bufferedEvents = [];\n    this.waiters = [];\n\n    const watch = chokidar.watch(observations.paths(), { persistent: true });\n    watch.on(\"all\", (eventName, eventPath) => {\n      const absPath = path.resolve(eventPath);\n      this.bufferedEvents.push({ name: eventName, absPath });\n      for (const waiter of drain(this.waiters)) {\n        waiter();\n      }\n    });\n    this.readyCb = new Promise<void>((resolve) => {\n      watch.on(\"ready\", () => resolve());\n    });\n    this.watch = watch;\n  }\n\n  update(observations: Observations) {\n    const watchedDirs = new Set(Object.keys(this.watch.getWatched()));\n    for (const newPath of observations.paths()) {\n      if (!this.isWatched(watchedDirs, newPath)) {\n        this.watch.add(newPath);\n      }\n    }\n  }\n\n  isWatched(watchedDirs: Set<string>, observedPath: string) {\n    // Walk over all of path's parents (inclusively) to see if any of them are in the watch set.\n    // This function assumes that Chokidar recursively watches all directories, which is\n    // definitely true on Mac with its FSEvents-based watcher.\n    // TODO (CX-2151): Verify this condition on Windows and Linux.\n    let curPath = observedPath;\n    while (true) {\n      const parsed = path.parse(curPath);\n\n      // TODO(CX-2152): Check to see if this condition for walking parents works on Windows.\n      if (parsed.dir === curPath) {\n        break;\n      }\n      if (watchedDirs.has(curPath)) {\n        return true;\n      }\n      curPath = parsed.dir;\n    }\n    return false;\n  }\n\n  async ready(): Promise<void> {\n    await this.readyCb;\n  }\n\n  async waitForEvent(): Promise<void> {\n    while (this.bufferedEvents.length === 0) {\n      const newEvent = new Promise<void>((resolve) => {\n        this.waiters.push(resolve);\n      });\n      await newEvent;\n    }\n  }\n\n  drainEvents(): WatchEvent[] {\n    return drain(this.bufferedEvents);\n  }\n\n  async close() {\n    await this.watch.close();\n  }\n}\nfunction drain<T>(l: T[]): T[] {\n  return l.splice(0, l.length);\n}\n\nexport class Crash extends Error {\n  errorType?: ErrorType;\n\n  constructor(errorType?: ErrorType, err?: any) {\n    super(err?.message);\n    this.errorType = errorType;\n  }\n}\n\nexport class WatchContext implements Context {\n  private _cleanupFns: Record<\n    string,\n    (exitCode: number, err?: any) => Promise<void>\n  > = {};\n  fs: RecordingFs;\n  deprecationMessagePrinted: boolean;\n  spinner: Ora | undefined;\n  private _bigBrainAuth: BigBrainAuth | null;\n\n  constructor(traceEvents: boolean, bigBrainAuth: BigBrainAuth | null) {\n    this.fs = new RecordingFs(traceEvents);\n    this.deprecationMessagePrinted = false;\n    this._bigBrainAuth = bigBrainAuth;\n  }\n\n  async crash(args: {\n    exitCode: number;\n    errorType?: ErrorType;\n    errForSentry?: any;\n    printedMessage: string | null;\n  }): Promise<never> {\n    if (args.errForSentry) {\n      Sentry.captureException(args.errForSentry);\n    }\n    if (args.printedMessage !== null) {\n      logFailure(this, args.printedMessage);\n    }\n    for (const fn of Object.values(this._cleanupFns)) {\n      await fn(args.exitCode, args.errForSentry);\n    }\n    // Okay to throw here. We've wrapped it in a Crash that we'll catch later.\n    // eslint-disable-next-line no-restricted-syntax\n    throw new Crash(args.errorType, args.errForSentry);\n  }\n\n  registerCleanup(fn: (exitCode: number, err?: any) => Promise<void>): string {\n    const handle = Math.random().toString(36).slice(2);\n    this._cleanupFns[handle] = fn;\n    return handle;\n  }\n\n  removeCleanup(handle: string) {\n    const value = this._cleanupFns[handle];\n    delete this._cleanupFns[handle];\n    return value ?? null;\n  }\n\n  bigBrainAuth(): BigBrainAuth | null {\n    return this._bigBrainAuth;\n  }\n\n  _updateBigBrainAuth(auth: BigBrainAuth | null): void {\n    this._bigBrainAuth = auth;\n  }\n}\n"], "mappings": ";;;;AAAA,OAAO,cAAc;AACrB,OAAO,UAAU;AACjB,SAAuB,mBAA+B;AACtD;AAAA,EAIE;AAAA,OACK;AACP,YAAY,YAAY;AAGjB,aAAM,QAAQ;AAAA,EAOnB,YAAY,cAA4B;AANxC,wBAAQ;AACR,wBAAQ;AAER,wBAAQ;AACR,wBAAQ;AAGN,SAAK,iBAAiB,CAAC;AACvB,SAAK,UAAU,CAAC;AAEhB,UAAM,QAAQ,SAAS,MAAM,aAAa,MAAM,GAAG,EAAE,YAAY,KAAK,CAAC;AACvE,UAAM,GAAG,OAAO,CAAC,WAAW,cAAc;AACxC,YAAM,UAAU,KAAK,QAAQ,SAAS;AACtC,WAAK,eAAe,KAAK,EAAE,MAAM,WAAW,QAAQ,CAAC;AACrD,iBAAW,UAAU,MAAM,KAAK,OAAO,GAAG;AACxC,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,SAAK,UAAU,IAAI,QAAc,CAAC,YAAY;AAC5C,YAAM,GAAG,SAAS,MAAM,QAAQ,CAAC;AAAA,IACnC,CAAC;AACD,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OAAO,cAA4B;AACjC,UAAM,cAAc,IAAI,IAAI,OAAO,KAAK,KAAK,MAAM,WAAW,CAAC,CAAC;AAChE,eAAW,WAAW,aAAa,MAAM,GAAG;AAC1C,UAAI,CAAC,KAAK,UAAU,aAAa,OAAO,GAAG;AACzC,aAAK,MAAM,IAAI,OAAO;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU,aAA0B,cAAsB;AAKxD,QAAI,UAAU;AACd,WAAO,MAAM;AACX,YAAM,SAAS,KAAK,MAAM,OAAO;AAGjC,UAAI,OAAO,QAAQ,SAAS;AAC1B;AAAA,MACF;AACA,UAAI,YAAY,IAAI,OAAO,GAAG;AAC5B,eAAO;AAAA,MACT;AACA,gBAAU,OAAO;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,QAAuB;AAC3B,UAAM,KAAK;AAAA,EACb;AAAA,EAEA,MAAM,eAA8B;AAClC,WAAO,KAAK,eAAe,WAAW,GAAG;AACvC,YAAM,WAAW,IAAI,QAAc,CAAC,YAAY;AAC9C,aAAK,QAAQ,KAAK,OAAO;AAAA,MAC3B,CAAC;AACD,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,cAA4B;AAC1B,WAAO,MAAM,KAAK,cAAc;AAAA,EAClC;AAAA,EAEA,MAAM,QAAQ;AACZ,UAAM,KAAK,MAAM,MAAM;AAAA,EACzB;AACF;AACA,SAAS,MAAS,GAAa;AAC7B,SAAO,EAAE,OAAO,GAAG,EAAE,MAAM;AAC7B;AAEO,aAAM,cAAc,MAAM;AAAA,EAG/B,YAAY,WAAuB,KAAW;AAC5C,UAAM,KAAK,OAAO;AAHpB;AAIE,SAAK,YAAY;AAAA,EACnB;AACF;AAEO,aAAM,aAAgC;AAAA,EAU3C,YAAY,aAAsB,cAAmC;AATrE,wBAAQ,eAGJ,CAAC;AACL;AACA;AACA;AACA,wBAAQ;AAGN,SAAK,KAAK,IAAI,YAAY,WAAW;AACrC,SAAK,4BAA4B;AACjC,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAEA,MAAM,MAAM,MAKO;AACjB,QAAI,KAAK,cAAc;AACrB,aAAO,iBAAiB,KAAK,YAAY;AAAA,IAC3C;AACA,QAAI,KAAK,mBAAmB,MAAM;AAChC,iBAAW,MAAM,KAAK,cAAc;AAAA,IACtC;AACA,eAAW,MAAM,OAAO,OAAO,KAAK,WAAW,GAAG;AAChD,YAAM,GAAG,KAAK,UAAU,KAAK,YAAY;AAAA,IAC3C;AAGA,UAAM,IAAI,MAAM,KAAK,WAAW,KAAK,YAAY;AAAA,EACnD;AAAA,EAEA,gBAAgB,IAA4D;AAC1E,UAAM,SAAS,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AACjD,SAAK,YAAY,MAAM,IAAI;AAC3B,WAAO;AAAA,EACT;AAAA,EAEA,cAAc,QAAgB;AAC5B,UAAM,QAAQ,KAAK,YAAY,MAAM;AACrC,WAAO,KAAK,YAAY,MAAM;AAC9B,WAAO,SAAS;AAAA,EAClB;AAAA,EAEA,eAAoC;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,oBAAoB,MAAiC;AACnD,SAAK,gBAAgB;AAAA,EACvB;AACF;", "names": []}