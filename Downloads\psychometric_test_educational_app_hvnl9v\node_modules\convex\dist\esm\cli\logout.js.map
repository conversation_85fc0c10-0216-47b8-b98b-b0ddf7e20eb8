{"version": 3, "sources": ["../../../src/cli/logout.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport { logFinishedStep, oneoffContext } from \"../bundler/context.js\";\nimport { recursivelyDelete } from \"./lib/fsUtils.js\";\nimport { globalConfigPath } from \"./lib/utils/globalConfig.js\";\n\nexport const logout = new Command(\"logout\")\n  .description(\"Log out of Convex on this machine\")\n  .allowExcessArguments(false)\n  .action(async () => {\n    const ctx = await oneoffContext({\n      url: undefined,\n      adminKey: undefined,\n      envFile: undefined,\n    });\n\n    if (ctx.fs.exists(globalConfigPath())) {\n      recursivelyDelete(ctx, globalConfigPath());\n    }\n\n    logFinishedStep(\n      ctx,\n      \"You have been logged out of Convex.\\n  Run `npx convex dev` to log in.\",\n    );\n  });\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,SAAS,iBAAiB,qBAAqB;AAC/C,SAAS,yBAAyB;AAClC,SAAS,wBAAwB;AAE1B,aAAM,SAAS,IAAI,QAAQ,QAAQ,EACvC,YAAY,mCAAmC,EAC/C,qBAAqB,KAAK,EAC1B,OAAO,YAAY;AAClB,QAAM,MAAM,MAAM,cAAc;AAAA,IAC9B,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AAED,MAAI,IAAI,GAAG,OAAO,iBAAiB,CAAC,GAAG;AACrC,sBAAkB,KAAK,iBAAiB,CAAC;AAAA,EAC3C;AAEA;AAAA,IACE;AAAA,IACA;AAAA,EACF;AACF,CAAC;", "names": []}