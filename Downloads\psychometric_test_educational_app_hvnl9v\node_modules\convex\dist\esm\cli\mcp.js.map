{"version": 3, "sources": ["../../../src/cli/mcp.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport { oneoffContext } from \"../bundler/context.js\";\nimport { Server } from \"@modelcontextprotocol/sdk/server/index.js\";\nimport { StdioServerTransport } from \"@modelcontextprotocol/sdk/server/stdio.js\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { checkAuthorization } from \"./lib/login.js\";\nimport {\n  CallToolRequest,\n  CallToolRequestSchema,\n  ListToolsRequestSchema,\n} from \"@modelcontextprotocol/sdk/types.js\";\nimport {\n  McpOptions,\n  RequestContext,\n  RequestCrash,\n} from \"./lib/mcp/requestContext.js\";\nimport { mcpTool, convexTools, ConvexTool } from \"./lib/mcp/tools/index.js\";\nimport { Mutex } from \"./lib/utils/mutex.js\";\nimport { initializeBigBrainAuth } from \"./lib/deploymentSelection.js\";\n\nconst allToolNames = convexTools.map((t) => t.name).sort();\n\nexport const mcp = new Command(\"mcp\")\n  .summary(\"Manage the Model Context Protocol server for Convex [BETA]\")\n  .description(\n    \"Commands to initialize and run a Model Context Protocol server for Convex that can be used with AI tools.\\n\" +\n      \"This server exposes your Convex codebase to AI tools in a structured way.\",\n  )\n  .allowExcessArguments(false);\n\nmcp\n  .command(\"start\")\n  .summary(\"Start the MCP server\")\n  .description(\n    \"Start the Model Context Protocol server for Convex that can be used with AI tools.\",\n  )\n  .option(\n    \"--project-dir <project-dir>\",\n    \"Run the MCP server for a single project. By default, the MCP server can run for multiple projects, and each tool call specifies its project directory.\",\n  )\n  .option(\n    \"--disable-tools <tool-names>\",\n    `Comma separated list of tool names to disable (options: ${allToolNames.join(\", \")})`,\n  )\n  .option(\n    \"--disable-production-deployments\",\n    \"Disable the MCP server from accessing production deployments.\",\n  )\n  .addDeploymentSelectionOptions(actionDescription(\"Run the MCP server on\"))\n  .action(async (options) => {\n    const ctx = await oneoffContext(options);\n    try {\n      const server = makeServer(options);\n      const transport = new StdioServerTransport();\n      await server.connect(transport);\n      // Keep the process running\n      await new Promise(() => {});\n    } catch (error: any) {\n      await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        errForSentry: `Failed to start MCP server: ${error}`,\n        printedMessage: `Failed to start MCP server: ${error}`,\n      });\n    }\n  });\n\nfunction makeServer(options: McpOptions) {\n  const disabledToolNames = new Set<string>();\n  for (const toolName of options.disableTools?.split(\",\") ?? []) {\n    const name = toolName.trim();\n    if (!allToolNames.includes(name)) {\n      // eslint-disable-next-line no-restricted-syntax\n      throw new Error(\n        `Disabled tool ${name} not found (valid tools: ${allToolNames.join(\", \")})`,\n      );\n    }\n    disabledToolNames.add(name);\n  }\n\n  const enabledToolsByName: Record<string, ConvexTool<any, any>> = {};\n  for (const tool of convexTools) {\n    if (!disabledToolNames.has(tool.name)) {\n      enabledToolsByName[tool.name] = tool;\n    }\n  }\n\n  const mutex = new Mutex();\n  const server = new Server(\n    {\n      name: \"Convex MCP Server\",\n      version: \"0.0.1\",\n    },\n    {\n      capabilities: {\n        tools: {},\n      },\n    },\n  );\n  server.setRequestHandler(\n    CallToolRequestSchema,\n    async (request: CallToolRequest) => {\n      const ctx = new RequestContext(options);\n      await initializeBigBrainAuth(ctx, options);\n      try {\n        const authorized = await checkAuthorization(ctx, false);\n        if (!authorized) {\n          await ctx.crash({\n            exitCode: 1,\n            errorType: \"fatal\",\n            printedMessage:\n              \"Not Authorized: Run `npx convex dev` to login to your Convex project.\",\n          });\n        }\n        if (!request.params.arguments) {\n          await ctx.crash({\n            exitCode: 1,\n            errorType: \"fatal\",\n            printedMessage: \"No arguments provided\",\n          });\n        }\n        const convexTool = enabledToolsByName[request.params.name];\n        if (!convexTool) {\n          await ctx.crash({\n            exitCode: 1,\n            errorType: \"fatal\",\n            printedMessage: `Tool ${request.params.name} not found`,\n          });\n        }\n        const input = convexTool.inputSchema.parse(request.params.arguments);\n\n        // Serialize tool handlers since they're mutating the current working directory.\n        const result = await mutex.runExclusive(async () => {\n          return await convexTool.handler(ctx, input);\n        });\n        return {\n          content: [\n            {\n              type: \"text\",\n              text: JSON.stringify(result),\n            },\n          ],\n        };\n      } catch (error: any) {\n        let message: string;\n        if (error instanceof RequestCrash) {\n          message = error.printedMessage;\n        } else if (error instanceof Error) {\n          message = error.message;\n        } else {\n          message = String(error);\n        }\n        return {\n          content: [\n            {\n              type: \"text\",\n              text: JSON.stringify({ error: message }),\n            },\n          ],\n          isError: true,\n        };\n      }\n    },\n  );\n  server.setRequestHandler(ListToolsRequestSchema, async () => {\n    return {\n      tools: Object.values(enabledToolsByName).map(mcpTool),\n    };\n  });\n  return server;\n}\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,SAAS,qBAAqB;AAC9B,SAAS,cAAc;AACvB,SAAS,4BAA4B;AACrC,SAAS,yBAAyB;AAClC,SAAS,0BAA0B;AACnC;AAAA,EAEE;AAAA,EACA;AAAA,OACK;AACP;AAAA,EAEE;AAAA,EACA;AAAA,OACK;AACP,SAAS,SAAS,mBAA+B;AACjD,SAAS,aAAa;AACtB,SAAS,8BAA8B;AAEvC,MAAM,eAAe,YAAY,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK;AAElD,aAAM,MAAM,IAAI,QAAQ,KAAK,EACjC,QAAQ,4DAA4D,EACpE;AAAA,EACC;AAEF,EACC,qBAAqB,KAAK;AAE7B,IACG,QAAQ,OAAO,EACf,QAAQ,sBAAsB,EAC9B;AAAA,EACC;AACF,EACC;AAAA,EACC;AAAA,EACA;AACF,EACC;AAAA,EACC;AAAA,EACA,2DAA2D,aAAa,KAAK,IAAI,CAAC;AACpF,EACC;AAAA,EACC;AAAA,EACA;AACF,EACC,8BAA8B,kBAAkB,uBAAuB,CAAC,EACxE,OAAO,OAAO,YAAY;AACzB,QAAM,MAAM,MAAM,cAAc,OAAO;AACvC,MAAI;AACF,UAAM,SAAS,WAAW,OAAO;AACjC,UAAM,YAAY,IAAI,qBAAqB;AAC3C,UAAM,OAAO,QAAQ,SAAS;AAE9B,UAAM,IAAI,QAAQ,MAAM;AAAA,IAAC,CAAC;AAAA,EAC5B,SAAS,OAAY;AACnB,UAAM,IAAI,MAAM;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc,+BAA+B,KAAK;AAAA,MAClD,gBAAgB,+BAA+B,KAAK;AAAA,IACtD,CAAC;AAAA,EACH;AACF,CAAC;AAEH,SAAS,WAAW,SAAqB;AACvC,QAAM,oBAAoB,oBAAI,IAAY;AAC1C,aAAW,YAAY,QAAQ,cAAc,MAAM,GAAG,KAAK,CAAC,GAAG;AAC7D,UAAM,OAAO,SAAS,KAAK;AAC3B,QAAI,CAAC,aAAa,SAAS,IAAI,GAAG;AAEhC,YAAM,IAAI;AAAA,QACR,iBAAiB,IAAI,4BAA4B,aAAa,KAAK,IAAI,CAAC;AAAA,MAC1E;AAAA,IACF;AACA,sBAAkB,IAAI,IAAI;AAAA,EAC5B;AAEA,QAAM,qBAA2D,CAAC;AAClE,aAAW,QAAQ,aAAa;AAC9B,QAAI,CAAC,kBAAkB,IAAI,KAAK,IAAI,GAAG;AACrC,yBAAmB,KAAK,IAAI,IAAI;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,QAAQ,IAAI,MAAM;AACxB,QAAM,SAAS,IAAI;AAAA,IACjB;AAAA,MACE,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA;AAAA,MACE,cAAc;AAAA,QACZ,OAAO,CAAC;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,OAAO,YAA6B;AAClC,YAAM,MAAM,IAAI,eAAe,OAAO;AACtC,YAAM,uBAAuB,KAAK,OAAO;AACzC,UAAI;AACF,cAAM,aAAa,MAAM,mBAAmB,KAAK,KAAK;AACtD,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MAAM;AAAA,YACd,UAAU;AAAA,YACV,WAAW;AAAA,YACX,gBACE;AAAA,UACJ,CAAC;AAAA,QACH;AACA,YAAI,CAAC,QAAQ,OAAO,WAAW;AAC7B,gBAAM,IAAI,MAAM;AAAA,YACd,UAAU;AAAA,YACV,WAAW;AAAA,YACX,gBAAgB;AAAA,UAClB,CAAC;AAAA,QACH;AACA,cAAM,aAAa,mBAAmB,QAAQ,OAAO,IAAI;AACzD,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MAAM;AAAA,YACd,UAAU;AAAA,YACV,WAAW;AAAA,YACX,gBAAgB,QAAQ,QAAQ,OAAO,IAAI;AAAA,UAC7C,CAAC;AAAA,QACH;AACA,cAAM,QAAQ,WAAW,YAAY,MAAM,QAAQ,OAAO,SAAS;AAGnE,cAAM,SAAS,MAAM,MAAM,aAAa,YAAY;AAClD,iBAAO,MAAM,WAAW,QAAQ,KAAK,KAAK;AAAA,QAC5C,CAAC;AACD,eAAO;AAAA,UACL,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM,KAAK,UAAU,MAAM;AAAA,YAC7B;AAAA,UACF;AAAA,QACF;AAAA,MACF,SAAS,OAAY;AACnB,YAAI;AACJ,YAAI,iBAAiB,cAAc;AACjC,oBAAU,MAAM;AAAA,QAClB,WAAW,iBAAiB,OAAO;AACjC,oBAAU,MAAM;AAAA,QAClB,OAAO;AACL,oBAAU,OAAO,KAAK;AAAA,QACxB;AACA,eAAO;AAAA,UACL,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM,KAAK,UAAU,EAAE,OAAO,QAAQ,CAAC;AAAA,YACzC;AAAA,UACF;AAAA,UACA,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,kBAAkB,wBAAwB,YAAY;AAC3D,WAAO;AAAA,MACL,OAAO,OAAO,OAAO,kBAAkB,EAAE,IAAI,OAAO;AAAA,IACtD;AAAA,EACF,CAAC;AACD,SAAO;AACT;", "names": []}