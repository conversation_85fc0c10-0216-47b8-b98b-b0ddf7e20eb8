{"version": 3, "sources": ["../../../src/cli/run.ts"], "sourcesContent": ["import { Command } from \"@commander-js/extra-typings\";\nimport { oneoffContext } from \"../bundler/context.js\";\nimport {\n  deploymentSelectionWithinProjectFromOptions,\n  loadSelectedDeploymentCredentials,\n} from \"./lib/api.js\";\nimport { actionDescription } from \"./lib/command.js\";\nimport { runInDeployment } from \"./lib/run.js\";\nimport { ensureHasConvexDependency } from \"./lib/utils/utils.js\";\nimport { getDeploymentSelection } from \"./lib/deploymentSelection.js\";\n\nexport const run = new Command(\"run\")\n  .description(\"Run a function (query, mutation, or action) on your deployment\")\n  .allowExcessArguments(false)\n  .addRunOptions()\n  .addDeploymentSelectionOptions(actionDescription(\"Run the function on\"))\n  .showHelpAfterError()\n  .action(async (functionName, argsString, options) => {\n    const ctx = await oneoffContext(options);\n    await ensureHasConvexDependency(ctx, \"run\");\n    const selectionWithinProject =\n      await deploymentSelectionWithinProjectFromOptions(ctx, options);\n    const deploymentSelection = await getDeploymentSelection(ctx, options);\n    const deployment = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      selectionWithinProject,\n    );\n\n    if (\n      deployment.deploymentFields?.deploymentType === \"prod\" &&\n      options.push\n    ) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage:\n          `\\`convex run\\` doesn't support pushing functions to prod deployments. ` +\n          `Remove the --push flag. To push to production use \\`npx convex deploy\\`.`,\n      });\n    }\n\n    await runInDeployment(ctx, {\n      deploymentUrl: deployment.url,\n      adminKey: deployment.adminKey,\n      deploymentName: deployment.deploymentFields?.deploymentName ?? null,\n      functionName,\n      argsString: argsString ?? \"{}\",\n      componentPath: options.component,\n      identityString: options.identity,\n      push: !!options.push,\n      watch: !!options.watch,\n      typecheck: options.typecheck,\n      typecheckComponents: options.typecheckComponents,\n      codegen: options.codegen === \"enable\",\n      liveComponentSources: !!options.liveComponentSources,\n    });\n  });\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,SAAS,qBAAqB;AAC9B;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,yBAAyB;AAClC,SAAS,uBAAuB;AAChC,SAAS,iCAAiC;AAC1C,SAAS,8BAA8B;AAEhC,aAAM,MAAM,IAAI,QAAQ,KAAK,EACjC,YAAY,gEAAgE,EAC5E,qBAAqB,KAAK,EAC1B,cAAc,EACd,8BAA8B,kBAAkB,qBAAqB,CAAC,EACtE,mBAAmB,EACnB,OAAO,OAAO,cAAc,YAAY,YAAY;AACnD,QAAM,MAAM,MAAM,cAAc,OAAO;AACvC,QAAM,0BAA0B,KAAK,KAAK;AAC1C,QAAM,yBACJ,MAAM,4CAA4C,KAAK,OAAO;AAChE,QAAM,sBAAsB,MAAM,uBAAuB,KAAK,OAAO;AACrE,QAAM,aAAa,MAAM;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,MACE,WAAW,kBAAkB,mBAAmB,UAChD,QAAQ,MACR;AACA,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBACE;AAAA,IAEJ,CAAC;AAAA,EACH;AAEA,QAAM,gBAAgB,KAAK;AAAA,IACzB,eAAe,WAAW;AAAA,IAC1B,UAAU,WAAW;AAAA,IACrB,gBAAgB,WAAW,kBAAkB,kBAAkB;AAAA,IAC/D;AAAA,IACA,YAAY,cAAc;AAAA,IAC1B,eAAe,QAAQ;AAAA,IACvB,gBAAgB,QAAQ;AAAA,IACxB,MAAM,CAAC,CAAC,QAAQ;AAAA,IAChB,OAAO,CAAC,CAAC,QAAQ;AAAA,IACjB,WAAW,QAAQ;AAAA,IACnB,qBAAqB,QAAQ;AAAA,IAC7B,SAAS,QAAQ,YAAY;AAAA,IAC7B,sBAAsB,CAAC,CAAC,QAAQ;AAAA,EAClC,CAAC;AACH,CAAC;", "names": []}