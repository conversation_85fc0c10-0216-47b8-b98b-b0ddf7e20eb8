{"version": 3, "sources": ["../../../src/common/index.ts"], "sourcesContent": ["import type { Value } from \"../values/value.js\";\n\n/**\n * Validate that the arguments to a Convex function are an object, defaulting\n * `undefined` to `{}`.\n */\nexport function parseArgs(\n  args: Record<string, Value> | undefined,\n): Record<string, Value> {\n  if (args === undefined) {\n    return {};\n  }\n  if (!isSimpleObject(args)) {\n    throw new Error(\n      `The arguments to a Convex function must be an object. Received: ${\n        args as any\n      }`,\n    );\n  }\n  return args;\n}\n\nexport function validateDeploymentUrl(deploymentUrl: string) {\n  // Don't use things like `new URL(deploymentUrl).hostname` since these aren't\n  // supported by React Native's JS environment\n  if (typeof deploymentUrl === \"undefined\") {\n    throw new Error(\n      `Client created with undefined deployment address. If you used an environment variable, check that it's set.`,\n    );\n  }\n  if (typeof deploymentUrl !== \"string\") {\n    throw new Error(\n      `Invalid deployment address: found ${deploymentUrl as any}\".`,\n    );\n  }\n  if (\n    !(deploymentUrl.startsWith(\"http:\") || deploymentUrl.startsWith(\"https:\"))\n  ) {\n    throw new Error(\n      `Invalid deployment address: Must start with \"https://\" or \"http://\". Found \"${deploymentUrl}\".`,\n    );\n  }\n\n  // Most clients should connect to \".convex.cloud\". But we also support localhost and\n  // custom custom. We validate the deployment url is a valid url, which is the most\n  // common failure pattern.\n  try {\n    new URL(deploymentUrl);\n  } catch {\n    throw new Error(\n      `Invalid deployment address: \"${deploymentUrl}\" is not a valid URL. If you believe this URL is correct, use the \\`skipConvexDeploymentUrlCheck\\` option to bypass this.`,\n    );\n  }\n\n  // If a user uses .convex.site, this is very likely incorrect.\n  if (deploymentUrl.endsWith(\".convex.site\")) {\n    throw new Error(\n      `Invalid deployment address: \"${deploymentUrl}\" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \\`skipConvexDeploymentUrlCheck\\` option to bypass this.`,\n    );\n  }\n}\n\n/**\n * Check whether a value is a plain old JavaScript object.\n */\nexport function isSimpleObject(value: unknown) {\n  const isObject = typeof value === \"object\";\n  const prototype = Object.getPrototypeOf(value);\n  const isSimple =\n    prototype === null ||\n    prototype === Object.prototype ||\n    // Objects generated from other contexts (e.g. across Node.js `vm` modules) will not satisfy the previous\n    // conditions but are still simple objects.\n    prototype?.constructor?.name === \"Object\";\n  return isObject && isSimple;\n}\n"], "mappings": ";AAMO,gBAAS,UACd,MACuB;AACvB,MAAI,SAAS,QAAW;AACtB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,CAAC,eAAe,IAAI,GAAG;AACzB,UAAM,IAAI;AAAA,MACR,mEACE,IACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEO,gBAAS,sBAAsB,eAAuB;AAG3D,MAAI,OAAO,kBAAkB,aAAa;AACxC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,kBAAkB,UAAU;AACrC,UAAM,IAAI;AAAA,MACR,qCAAqC,aAAoB;AAAA,IAC3D;AAAA,EACF;AACA,MACE,EAAE,cAAc,WAAW,OAAO,KAAK,cAAc,WAAW,QAAQ,IACxE;AACA,UAAM,IAAI;AAAA,MACR,+EAA+E,aAAa;AAAA,IAC9F;AAAA,EACF;AAKA,MAAI;AACF,QAAI,IAAI,aAAa;AAAA,EACvB,QAAQ;AACN,UAAM,IAAI;AAAA,MACR,gCAAgC,aAAa;AAAA,IAC/C;AAAA,EACF;AAGA,MAAI,cAAc,SAAS,cAAc,GAAG;AAC1C,UAAM,IAAI;AAAA,MACR,gCAAgC,aAAa;AAAA,IAC/C;AAAA,EACF;AACF;AAKO,gBAAS,eAAe,OAAgB;AAC7C,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,QAAM,WACJ,cAAc,QACd,cAAc,OAAO;AAAA;AAAA,EAGrB,WAAW,aAAa,SAAS;AACnC,SAAO,YAAY;AACrB;", "names": []}