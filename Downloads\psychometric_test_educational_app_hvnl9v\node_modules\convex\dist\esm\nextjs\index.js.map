{"version": 3, "sources": ["../../../src/nextjs/index.ts"], "sourcesContent": ["/**\n * Helpers for integrating Convex into Next.js applications using server rendering.\n *\n * This module contains:\n * 1. {@link preloadQuery}, for preloading data for reactive client components.\n * 2. {@link fetchQuery}, {@link fetchMutation} and {@link fetchAction} for loading and mutating Convex data\n *   from Next.js Server Components, Server Actions and Route Handlers.\n *\n * ## Usage\n *\n * All exported functions assume that a Convex deployment URL is set in the\n * `NEXT_PUBLIC_CONVEX_URL` environment variable. `npx convex dev` will\n * automatically set it during local development.\n *\n * ### Preloading data\n *\n * Preload data inside a Server Component:\n *\n * ```typescript\n * import { preloadQuery } from \"convex/nextjs\";\n * import { api } from \"@/convex/_generated/api\";\n * import ClientComponent from \"./ClientComponent\";\n *\n * export async function ServerComponent() {\n *   const preloaded = await preloadQuery(api.foo.baz);\n *   return <ClientComponent preloaded={preloaded} />;\n * }\n * ```\n *\n * And pass it to a Client Component:\n * ```typescript\n * import { Preloaded, usePreloadedQuery } from \"convex/nextjs\";\n * import { api } from \"@/convex/_generated/api\";\n *\n * export function ClientComponent(props: {\n *   preloaded: Preloaded<typeof api.foo.baz>;\n * }) {\n *   const data = await usePreloadedQuery(props.preloaded);\n *   // render `data`...\n * }\n * ```\n *\n * @module\n */\n\nimport { ConvexHttpClient } from \"../browser/index.js\";\nimport { validateDeploymentUrl } from \"../common/index.js\";\nimport { Preloaded } from \"../react/index.js\";\nimport {\n  ArgsAndOptions,\n  FunctionReference,\n  FunctionReturnType,\n  getFunctionName,\n} from \"../server/index.js\";\nimport { convexToJson, jsonToConvex } from \"../values/index.js\";\n\n/**\n * Options to {@link preloadQuery}, {@link fetchQuery}, {@link fetchMutation} and {@link fetchAction}.\n */\nexport type NextjsOptions = {\n  /**\n   * The JWT-encoded OpenID Connect authentication token to use for the function call.\n   */\n  token?: string;\n  /**\n   * The URL of the Convex deployment to use for the function call.\n   * Defaults to `process.env.NEXT_PUBLIC_CONVEX_URL`.\n   */\n  url?: string;\n\n  /**\n   * @internal\n   */\n  adminToken?: string;\n  /**\n   * Skip validating that the Convex deployment URL looks like\n   * `https://happy-animal-123.convex.cloud` or localhost.\n   *\n   * This can be useful if running a self-hosted Convex backend that uses a different\n   * URL.\n   *\n   * The default value is `false`\n   */\n  skipConvexDeploymentUrlCheck?: boolean;\n};\n\n/**\n * Execute a Convex query function and return a `Preloaded`\n * payload which can be passed to {@link react.usePreloadedQuery} in a Client\n * Component.\n *\n * @param query - a {@link server.FunctionReference} for the public query to run\n * like `api.dir1.dir2.filename.func`.\n * @param args - The arguments object for the query. If this is omitted,\n * the arguments will be `{}`.\n * @param options -  A {@link NextjsOptions} options object for the query.\n * @returns A promise of the `Preloaded` payload.\n */\nexport async function preloadQuery<Query extends FunctionReference<\"query\">>(\n  query: Query,\n  ...args: ArgsAndOptions<Query, NextjsOptions>\n): Promise<Preloaded<Query>> {\n  const value = await fetchQuery(query, ...args);\n  const preloaded = {\n    _name: getFunctionName(query),\n    _argsJSON: convexToJson(args[0] ?? {}),\n    _valueJSON: convexToJson(value),\n  };\n  return preloaded as any;\n}\n\n/**\n * Returns the result of executing a query via {@link preloadQuery}.\n *\n * @param preloaded - The `Preloaded` payload returned by {@link preloadQuery}.\n * @returns The query result.\n */\nexport function preloadedQueryResult<Query extends FunctionReference<\"query\">>(\n  preloaded: Preloaded<Query>,\n): FunctionReturnType<Query> {\n  return jsonToConvex(preloaded._valueJSON);\n}\n\n/**\n * Execute a Convex query function.\n *\n * @param query - a {@link server.FunctionReference} for the public query to run\n * like `api.dir1.dir2.filename.func`.\n * @param args - The arguments object for the query. If this is omitted,\n * the arguments will be `{}`.\n * @param options -  A {@link NextjsOptions} options object for the query.\n * @returns A promise of the query's result.\n */\nexport async function fetchQuery<Query extends FunctionReference<\"query\">>(\n  query: Query,\n  ...args: ArgsAndOptions<Query, NextjsOptions>\n): Promise<FunctionReturnType<Query>> {\n  const [fnArgs, options] = args;\n  const client = setupClient(options ?? {});\n  return client.query(query, fnArgs);\n}\n\n/**\n * Execute a Convex mutation function.\n *\n * @param mutation - A {@link server.FunctionReference} for the public mutation\n * to run like `api.dir1.dir2.filename.func`.\n * @param args - The arguments object for the mutation. If this is omitted,\n * the arguments will be `{}`.\n * @param options -  A {@link NextjsOptions} options object for the mutation.\n * @returns A promise of the mutation's result.\n */\nexport async function fetchMutation<\n  Mutation extends FunctionReference<\"mutation\">,\n>(\n  mutation: Mutation,\n  ...args: ArgsAndOptions<Mutation, NextjsOptions>\n): Promise<FunctionReturnType<Mutation>> {\n  const [fnArgs, options] = args;\n  const client = setupClient(options ?? {});\n  return client.mutation(mutation, fnArgs);\n}\n\n/**\n * Execute a Convex action function.\n *\n * @param action - A {@link server.FunctionReference} for the public action\n * to run like `api.dir1.dir2.filename.func`.\n * @param args - The arguments object for the action. If this is omitted,\n * the arguments will be `{}`.\n * @param options -  A {@link NextjsOptions} options object for the action.\n * @returns A promise of the action's result.\n */\nexport async function fetchAction<Action extends FunctionReference<\"action\">>(\n  action: Action,\n  ...args: ArgsAndOptions<Action, NextjsOptions>\n): Promise<FunctionReturnType<Action>> {\n  const [fnArgs, options] = args;\n  const client = setupClient(options ?? {});\n  return client.action(action, fnArgs);\n}\n\nfunction setupClient(options: NextjsOptions) {\n  const client = new ConvexHttpClient(\n    getConvexUrl(options.url, options.skipConvexDeploymentUrlCheck ?? false),\n  );\n  if (options.token !== undefined) {\n    client.setAuth(options.token);\n  }\n  if (options.adminToken !== undefined) {\n    client.setAdminAuth(options.adminToken);\n  }\n  client.setFetchOptions({ cache: \"no-store\" });\n  return client;\n}\n\nfunction getConvexUrl(\n  deploymentUrl: string | undefined,\n  skipConvexDeploymentUrlCheck: boolean,\n) {\n  const url = deploymentUrl ?? process.env.NEXT_PUBLIC_CONVEX_URL;\n  const isFromEnv = deploymentUrl === undefined;\n  if (typeof url !== \"string\") {\n    throw new Error(\n      isFromEnv\n        ? `Environment variable NEXT_PUBLIC_CONVEX_URL is not set.`\n        : `Convex function called with invalid deployment address.`,\n    );\n  }\n  if (!skipConvexDeploymentUrlCheck) {\n    validateDeploymentUrl(url);\n  }\n  return url!;\n}\n"], "mappings": ";AA6CA,SAAS,wBAAwB;AACjC,SAAS,6BAA6B;AAEtC;AAAA,EAIE;AAAA,OACK;AACP,SAAS,cAAc,oBAAoB;AA4C3C,sBAAsB,aACpB,UACG,MACwB;AAC3B,QAAM,QAAQ,MAAM,WAAW,OAAO,GAAG,IAAI;AAC7C,QAAM,YAAY;AAAA,IAChB,OAAO,gBAAgB,KAAK;AAAA,IAC5B,WAAW,aAAa,KAAK,CAAC,KAAK,CAAC,CAAC;AAAA,IACrC,YAAY,aAAa,KAAK;AAAA,EAChC;AACA,SAAO;AACT;AAQO,gBAAS,qBACd,WAC2B;AAC3B,SAAO,aAAa,UAAU,UAAU;AAC1C;AAYA,sBAAsB,WACpB,UACG,MACiC;AACpC,QAAM,CAAC,QAAQ,OAAO,IAAI;AAC1B,QAAM,SAAS,YAAY,WAAW,CAAC,CAAC;AACxC,SAAO,OAAO,MAAM,OAAO,MAAM;AACnC;AAYA,sBAAsB,cAGpB,aACG,MACoC;AACvC,QAAM,CAAC,QAAQ,OAAO,IAAI;AAC1B,QAAM,SAAS,YAAY,WAAW,CAAC,CAAC;AACxC,SAAO,OAAO,SAAS,UAAU,MAAM;AACzC;AAYA,sBAAsB,YACpB,WACG,MACkC;AACrC,QAAM,CAAC,QAAQ,OAAO,IAAI;AAC1B,QAAM,SAAS,YAAY,WAAW,CAAC,CAAC;AACxC,SAAO,OAAO,OAAO,QAAQ,MAAM;AACrC;AAEA,SAAS,YAAY,SAAwB;AAC3C,QAAM,SAAS,IAAI;AAAA,IACjB,aAAa,QAAQ,KAAK,QAAQ,gCAAgC,KAAK;AAAA,EACzE;AACA,MAAI,QAAQ,UAAU,QAAW;AAC/B,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AACA,MAAI,QAAQ,eAAe,QAAW;AACpC,WAAO,aAAa,QAAQ,UAAU;AAAA,EACxC;AACA,SAAO,gBAAgB,EAAE,OAAO,WAAW,CAAC;AAC5C,SAAO;AACT;AAEA,SAAS,aACP,eACA,8BACA;AACA,QAAM,MAAM,iBAAiB,QAAQ,IAAI;AACzC,QAAM,YAAY,kBAAkB;AACpC,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI;AAAA,MACR,YACI,4DACA;AAAA,IACN;AAAA,EACF;AACA,MAAI,CAAC,8BAA8B;AACjC,0BAAsB,GAAG;AAAA,EAC3B;AACA,SAAO;AACT;", "names": []}