{"version": 3, "sources": ["../../../src/react-clerk/ConvexProviderWithClerk.tsx"], "sourcesContent": ["import React from \"react\";\n\nimport { ReactNode, useCallback, useMemo } from \"react\";\nimport { AuthTokenFetcher } from \"../browser/sync/client.js\";\nimport { ConvexProviderWithAuth } from \"../react/ConvexAuthState.js\";\n\n// Until we can import from our own entry points (requires TypeScript 4.7),\n// just describe the interface enough to help users pass the right type.\ntype IConvexReactClient = {\n  setAuth(fetchToken: AuthTokenFetcher): void;\n  clearAuth(): void;\n};\n\n// https://clerk.com/docs/reference/clerk-react/useauth\ntype UseAuth = () => {\n  isLoaded: boolean;\n  isSignedIn: boolean | undefined;\n  getToken: (options: {\n    template?: \"convex\";\n    skipCache?: boolean;\n  }) => Promise<string | null>;\n  // We don't use these properties but they should trigger a new token fetch.\n  orgId: string | undefined | null;\n  orgRole: string | undefined | null;\n};\n\n/**\n * A wrapper React component which provides a {@link react.ConvexReactClient}\n * authenticated with Clerk.\n *\n * It must be wrapped by a configured `Clerk<PERSON><PERSON><PERSON>`, from\n * `@clerk/clerk-react`, `@clerk/clerk-expo`, `@clerk/nextjs` or\n * another React-based Clerk client library and have the corresponding\n * `useAuth` hook passed in.\n *\n * See [Convex Clerk](https://docs.convex.dev/auth/clerk) on how to set up\n * Convex with Clerk.\n *\n * @public\n */\nexport function ConvexProviderWithClerk({\n  children,\n  client,\n  useAuth,\n}: {\n  children: ReactNode;\n  client: IConvexReactClient;\n  useAuth: UseAuth; // useAuth from Clerk\n}) {\n  const useAuthFromClerk = useUseAuthFromClerk(useAuth);\n  return (\n    <ConvexProviderWithAuth client={client} useAuth={useAuthFromClerk}>\n      {children}\n    </ConvexProviderWithAuth>\n  );\n}\n\nfunction useUseAuthFromClerk(useAuth: UseAuth) {\n  return useMemo(\n    () =>\n      function useAuthFromClerk() {\n        const { isLoaded, isSignedIn, getToken, orgId, orgRole } = useAuth();\n        const fetchAccessToken = useCallback(\n          async ({ forceRefreshToken }: { forceRefreshToken: boolean }) => {\n            try {\n              return getToken({\n                template: \"convex\",\n                skipCache: forceRefreshToken,\n              });\n            } catch {\n              return null;\n            }\n          },\n          // Build a new fetchAccessToken to trigger setAuth() whenever these change.\n          // Anything else from the JWT Clerk wants to be reactive goes here too.\n          // Clerk's Expo useAuth hook is not memoized so we don't include getToken.\n          // eslint-disable-next-line react-hooks/exhaustive-deps\n          [orgId, orgRole],\n        );\n        return useMemo(\n          () => ({\n            isLoading: !isLoaded,\n            isAuthenticated: isSignedIn ?? false,\n            fetchAccessToken,\n          }),\n          [isLoaded, isSignedIn, fetchAccessToken],\n        );\n      },\n    [useAuth],\n  );\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAElB,SAAoB,aAAa,eAAe;AAEhD,SAAS,8BAA8B;AAoChC,gBAAS,wBAAwB;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,QAAM,mBAAmB,oBAAoB,OAAO;AACpD,SACE,oCAAC,0BAAuB,QAAgB,SAAS,oBAC9C,QACH;AAEJ;AAEA,SAAS,oBAAoB,SAAkB;AAC7C,SAAO;AAAA,IACL,MACE,SAAS,mBAAmB;AAC1B,YAAM,EAAE,UAAU,YAAY,UAAU,OAAO,QAAQ,IAAI,QAAQ;AACnE,YAAM,mBAAmB;AAAA,QACvB,OAAO,EAAE,kBAAkB,MAAsC;AAC/D,cAAI;AACF,mBAAO,SAAS;AAAA,cACd,UAAU;AAAA,cACV,WAAW;AAAA,YACb,CAAC;AAAA,UACH,QAAQ;AACN,mBAAO;AAAA,UACT;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,CAAC,OAAO,OAAO;AAAA,MACjB;AACA,aAAO;AAAA,QACL,OAAO;AAAA,UACL,WAAW,CAAC;AAAA,UACZ,iBAAiB,cAAc;AAAA,UAC/B;AAAA,QACF;AAAA,QACA,CAAC,UAAU,YAAY,gBAAgB;AAAA,MACzC;AAAA,IACF;AAAA,IACF,CAAC,OAAO;AAAA,EACV;AACF;", "names": []}