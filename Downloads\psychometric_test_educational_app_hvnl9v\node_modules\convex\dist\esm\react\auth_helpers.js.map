{"version": 3, "sources": ["../../../src/react/auth_helpers.tsx"], "sourcesContent": ["import React from \"react\";\nimport { ReactNode } from \"react\";\nimport { useConvexAuth } from \"./ConvexAuthState.js\";\n\n/**\n * Renders children if the client is authenticated.\n *\n * @public\n */\nexport function Authenticated({ children }: { children: ReactNode }) {\n  const { isLoading, isAuthenticated } = useConvexAuth();\n  if (isLoading || !isAuthenticated) {\n    return null;\n  }\n  return <>{children}</>;\n}\n\n/**\n * Renders children if the client is using authentication but is not authenticated.\n *\n * @public\n */\nexport function Unauthenticated({ children }: { children: ReactNode }) {\n  const { isLoading, isAuthenticated } = useConvexAuth();\n  if (isLoading || isAuthenticated) {\n    return null;\n  }\n  return <>{children}</>;\n}\n\n/**\n * Renders children if the client isn't using authentication or is in the process\n * of authenticating.\n *\n * @public\n */\nexport function AuthLoading({ children }: { children: ReactNode }) {\n  const { isLoading } = useConvexAuth();\n  if (!isLoading) {\n    return null;\n  }\n  return <>{children}</>;\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAElB,SAAS,qBAAqB;AAOvB,gBAAS,cAAc,EAAE,SAAS,GAA4B;AACnE,QAAM,EAAE,WAAW,gBAAgB,IAAI,cAAc;AACrD,MAAI,aAAa,CAAC,iBAAiB;AACjC,WAAO;AAAA,EACT;AACA,SAAO,0DAAG,QAAS;AACrB;AAOO,gBAAS,gBAAgB,EAAE,SAAS,GAA4B;AACrE,QAAM,EAAE,WAAW,gBAAgB,IAAI,cAAc;AACrD,MAAI,aAAa,iBAAiB;AAChC,WAAO;AAAA,EACT;AACA,SAAO,0DAAG,QAAS;AACrB;AAQO,gBAAS,YAAY,EAAE,SAAS,GAA4B;AACjE,QAAM,EAAE,UAAU,IAAI,cAAc;AACpC,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,SAAO,0DAAG,QAAS;AACrB;", "names": []}