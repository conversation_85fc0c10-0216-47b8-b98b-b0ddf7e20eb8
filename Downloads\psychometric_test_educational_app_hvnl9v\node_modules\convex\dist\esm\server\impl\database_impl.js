"use strict";
import {
  convexTo<PERSON>son,
  jsonToConvex
} from "../../values/index.js";
import { performAsyncSyscall, performSyscall } from "./syscall.js";
import { QueryInitializerImpl } from "./query_impl.js";
import { validateArg } from "./validate.js";
import { version } from "../../index.js";
import { patchValueToJson } from "../../values/value.js";
async function get(id, isSystem) {
  validateArg(id, 1, "get", "id");
  if (typeof id !== "string") {
    throw new Error(
      `Invalid argument \`id\` for \`db.get\`, expected string but got '${typeof id}': ${id}`
    );
  }
  const args = {
    id: convexToJson(id),
    isSystem,
    version
  };
  const syscallJSON = await performAsyncSyscall("1.0/get", args);
  return jsonToConvex(syscallJSON);
}
export function setupReader() {
  const reader = (isSystem = false) => {
    return {
      get: async (id) => {
        return await get(id, isSystem);
      },
      query: (tableName) => {
        return new TableReader(tableName, isSystem).query();
      },
      normalizeId: (tableName, id) => {
        validateArg(tableName, 1, "normalizeId", "tableName");
        validateArg(id, 2, "normalizeId", "id");
        const accessingSystemTable = tableName.startsWith("_");
        if (accessingSystemTable !== isSystem) {
          throw new Error(
            `${accessingSystemTable ? "System" : "User"} tables can only be accessed from db.${isSystem ? "" : "system."}normalizeId().`
          );
        }
        const syscallJSON = performSyscall("1.0/db/normalizeId", {
          table: tableName,
          idString: id
        });
        const syscallResult = jsonToConvex(syscallJSON);
        return syscallResult.id;
      },
      // We set the system reader on the next line
      system: null,
      table: (tableName) => {
        return new TableReader(tableName, isSystem);
      }
    };
  };
  const { system: _, ...rest } = reader(true);
  const r = reader();
  r.system = rest;
  return r;
}
async function insert(tableName, value) {
  if (tableName.startsWith("_")) {
    throw new Error("System tables (prefixed with `_`) are read-only.");
  }
  validateArg(tableName, 1, "insert", "table");
  validateArg(value, 2, "insert", "value");
  const syscallJSON = await performAsyncSyscall("1.0/insert", {
    table: tableName,
    value: convexToJson(value)
  });
  const syscallResult = jsonToConvex(syscallJSON);
  return syscallResult._id;
}
async function patch(id, value) {
  validateArg(id, 1, "patch", "id");
  validateArg(value, 2, "patch", "value");
  await performAsyncSyscall("1.0/shallowMerge", {
    id: convexToJson(id),
    value: patchValueToJson(value)
  });
}
async function replace(id, value) {
  validateArg(id, 1, "replace", "id");
  validateArg(value, 2, "replace", "value");
  await performAsyncSyscall("1.0/replace", {
    id: convexToJson(id),
    value: convexToJson(value)
  });
}
async function delete_(id) {
  validateArg(id, 1, "delete", "id");
  await performAsyncSyscall("1.0/remove", { id: convexToJson(id) });
}
export function setupWriter() {
  const reader = setupReader();
  return {
    get: reader.get,
    query: reader.query,
    normalizeId: reader.normalizeId,
    system: reader.system,
    insert: async (table, value) => {
      return await insert(table, value);
    },
    patch: async (id, value) => {
      return await patch(id, value);
    },
    replace: async (id, value) => {
      return await replace(id, value);
    },
    delete: async (id) => {
      return await delete_(id);
    },
    table: (tableName) => {
      return new TableWriter(tableName, false);
    }
  };
}
class TableReader {
  constructor(tableName, isSystem) {
    this.tableName = tableName;
    this.isSystem = isSystem;
  }
  async get(id) {
    return get(id, this.isSystem);
  }
  query() {
    const accessingSystemTable = this.tableName.startsWith("_");
    if (accessingSystemTable !== this.isSystem) {
      throw new Error(
        `${accessingSystemTable ? "System" : "User"} tables can only be accessed from db.${this.isSystem ? "" : "system."}query().`
      );
    }
    return new QueryInitializerImpl(this.tableName);
  }
}
class TableWriter extends TableReader {
  async insert(value) {
    return insert(this.tableName, value);
  }
  async patch(id, value) {
    return patch(id, value);
  }
  async replace(id, value) {
    return replace(id, value);
  }
  async delete(id) {
    return delete_(id);
  }
}
//# sourceMappingURL=database_impl.js.map
