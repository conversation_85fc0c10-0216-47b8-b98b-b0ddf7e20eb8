{"version": 3, "sources": ["../../../../src/server/impl/filter_builder_impl.ts"], "sourcesContent": ["import { JSONValue, Value, NumericValue } from \"../../values/index.js\";\nimport { convexOrUndefinedTo<PERSON><PERSON> } from \"../../values/value.js\";\nimport { GenericTableInfo } from \"../data_model.js\";\nimport {\n  Expression,\n  ExpressionOrValue,\n  FilterBuilder,\n} from \"../filter_builder.js\";\n\n// The `any` type parameter in `Expression<any>` allows us to use this class\n// in place of any `Expression` type in `filterBuilderImpl`.\nexport class ExpressionImpl extends Expression<any> {\n  private inner: JSONValue;\n  constructor(inner: JSONValue) {\n    super();\n    this.inner = inner;\n  }\n\n  serialize(): JSONValue {\n    return this.inner;\n  }\n}\n\nexport function serializeExpression(\n  expr: ExpressionOrValue<Value | undefined>,\n): JSONValue {\n  if (expr instanceof ExpressionImpl) {\n    return expr.serialize();\n  } else {\n    // Assume that the expression is a literal Convex value, which we'll serialize\n    // to its JSON representation.\n    return { $literal: convexOrUndefinedToJson(expr as Value | undefined) };\n  }\n}\n\nexport const filterBuilderImpl: FilterBuilder<GenericTableInfo> = {\n  //  Comparisons  /////////////////////////////////////////////////////////////\n\n  eq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $eq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neq<T extends Value | undefined>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $neq: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  lte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $lte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gt<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gt: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  gte<T extends Value>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<boolean> {\n    return new ExpressionImpl({\n      $gte: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  //  Arithmetic  //////////////////////////////////////////////////////////////\n\n  add<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $add: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  sub<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $sub: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mul<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mul: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  div<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $div: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  mod<T extends NumericValue>(\n    l: ExpressionOrValue<T>,\n    r: ExpressionOrValue<T>,\n  ): Expression<T> {\n    return new ExpressionImpl({\n      $mod: [serializeExpression(l), serializeExpression(r)],\n    });\n  },\n\n  neg<T extends NumericValue>(x: ExpressionOrValue<T>): Expression<T> {\n    return new ExpressionImpl({ $neg: serializeExpression(x) });\n  },\n\n  //  Logic  ///////////////////////////////////////////////////////////////////\n\n  and(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $and: exprs.map(serializeExpression) });\n  },\n\n  or(...exprs: Array<ExpressionOrValue<boolean>>): Expression<boolean> {\n    return new ExpressionImpl({ $or: exprs.map(serializeExpression) });\n  },\n\n  not(x: ExpressionOrValue<boolean>): Expression<boolean> {\n    return new ExpressionImpl({ $not: serializeExpression(x) });\n  },\n\n  //  Other  ///////////////////////////////////////////////////////////////////\n  field(fieldPath: string): Expression<any> {\n    return new ExpressionImpl({ $field: fieldPath });\n  },\n};\n"], "mappings": ";;;;AACA,SAAS,+BAA+B;AAExC;AAAA,EACE;AAAA,OAGK;AAIA,aAAM,uBAAuB,WAAgB;AAAA,EAElD,YAAY,OAAkB;AAC5B,UAAM;AAFR,wBAAQ;AAGN,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,YAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AACF;AAEO,gBAAS,oBACd,MACW;AACX,MAAI,gBAAgB,gBAAgB;AAClC,WAAO,KAAK,UAAU;AAAA,EACxB,OAAO;AAGL,WAAO,EAAE,UAAU,wBAAwB,IAAyB,EAAE;AAAA,EACxE;AACF;AAEO,aAAM,oBAAqD;AAAA;AAAA,EAGhE,GACE,GACA,GACqB;AACrB,WAAO,IAAI,eAAe;AAAA,MACxB,KAAK,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AAAA,EAEA,IACE,GACA,GACqB;AACrB,WAAO,IAAI,eAAe;AAAA,MACxB,MAAM,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,GACE,GACA,GACqB;AACrB,WAAO,IAAI,eAAe;AAAA,MACxB,KAAK,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AAAA,EAEA,IACE,GACA,GACqB;AACrB,WAAO,IAAI,eAAe;AAAA,MACxB,MAAM,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,GACE,GACA,GACqB;AACrB,WAAO,IAAI,eAAe;AAAA,MACxB,KAAK,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AAAA,EAEA,IACE,GACA,GACqB;AACrB,WAAO,IAAI,eAAe;AAAA,MACxB,MAAM,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AAAA;AAAA,EAIA,IACE,GACA,GACe;AACf,WAAO,IAAI,eAAe;AAAA,MACxB,MAAM,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,IACE,GACA,GACe;AACf,WAAO,IAAI,eAAe;AAAA,MACxB,MAAM,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,IACE,GACA,GACe;AACf,WAAO,IAAI,eAAe;AAAA,MACxB,MAAM,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,IACE,GACA,GACe;AACf,WAAO,IAAI,eAAe;AAAA,MACxB,MAAM,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,IACE,GACA,GACe;AACf,WAAO,IAAI,eAAe;AAAA,MACxB,MAAM,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,IAA4B,GAAwC;AAClE,WAAO,IAAI,eAAe,EAAE,MAAM,oBAAoB,CAAC,EAAE,CAAC;AAAA,EAC5D;AAAA;AAAA,EAIA,OAAO,OAA+D;AACpE,WAAO,IAAI,eAAe,EAAE,MAAM,MAAM,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACpE;AAAA,EAEA,MAAM,OAA+D;AACnE,WAAO,IAAI,eAAe,EAAE,KAAK,MAAM,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACnE;AAAA,EAEA,IAAI,GAAoD;AACtD,WAAO,IAAI,eAAe,EAAE,MAAM,oBAAoB,CAAC,EAAE,CAAC;AAAA,EAC5D;AAAA;AAAA,EAGA,MAAM,WAAoC;AACxC,WAAO,IAAI,eAAe,EAAE,QAAQ,UAAU,CAAC;AAAA,EACjD;AACF;", "names": []}