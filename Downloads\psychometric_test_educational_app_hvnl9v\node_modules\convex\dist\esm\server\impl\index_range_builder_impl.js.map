{"version": 3, "sources": ["../../../../src/server/impl/index_range_builder_impl.ts"], "sourcesContent": ["import { convexT<PERSON><PERSON><PERSON>, JSONValue, Value } from \"../../values/index.js\";\nimport { convexOrUndefinedToJson } from \"../../values/value.js\";\nimport { GenericDocument, GenericIndexFields } from \"../data_model.js\";\nimport {\n  IndexRange,\n  IndexRangeBuilder,\n  LowerBoundIndexRangeBuilder,\n  UpperBoundIndexRangeBuilder,\n} from \"../index_range_builder.js\";\n\nexport type SerializedRangeExpression = {\n  type: \"Eq\" | \"Gt\" | \"Gte\" | \"Lt\" | \"Lte\";\n  fieldPath: string;\n  value: JSONValue;\n};\n\nexport class IndexRangeBuilderImpl\n  extends IndexRange\n  implements\n    IndexRangeBuilder<GenericDocument, GenericIndexFields>,\n    LowerBoundIndexRangeBuilder<GenericDocument, string>,\n    UpperBoundIndexRangeBuilder<GenericDocument, string>\n{\n  private rangeExpressions: ReadonlyArray<SerializedRangeExpression>;\n  private isConsumed: boolean;\n  private constructor(\n    rangeExpressions: ReadonlyArray<SerializedRangeExpression>,\n  ) {\n    super();\n    this.rangeExpressions = rangeExpressions;\n    this.isConsumed = false;\n  }\n\n  static new(): IndexRangeBuilderImpl {\n    return new IndexRangeBuilderImpl([]);\n  }\n\n  private consume() {\n    if (this.isConsumed) {\n      throw new Error(\n        \"IndexRangeBuilder has already been used! Chain your method calls like `q => q.eq(...).eq(...)`. See https://docs.convex.dev/using/indexes\",\n      );\n    }\n    this.isConsumed = true;\n  }\n\n  eq(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Eq\",\n        fieldPath: fieldName,\n        value: convexOrUndefinedToJson(value),\n      }),\n    );\n  }\n\n  gt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gt\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  gte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Gte\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  lt(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lt\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n  lte(fieldName: string, value: Value) {\n    this.consume();\n    return new IndexRangeBuilderImpl(\n      this.rangeExpressions.concat({\n        type: \"Lte\",\n        fieldPath: fieldName,\n        value: convexToJson(value),\n      }),\n    );\n  }\n\n  export() {\n    this.consume();\n    return this.rangeExpressions;\n  }\n}\n"], "mappings": ";;;;AAAA,SAAS,oBAAsC;AAC/C,SAAS,+BAA+B;AAExC;AAAA,EACE;AAAA,OAIK;AAQA,aAAM,8BACH,WAKV;AAAA,EAGU,YACN,kBACA;AACA,UAAM;AALR,wBAAQ;AACR,wBAAQ;AAKN,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,OAAO,MAA6B;AAClC,WAAO,IAAI,sBAAsB,CAAC,CAAC;AAAA,EACrC;AAAA,EAEQ,UAAU;AAChB,QAAI,KAAK,YAAY;AACnB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,GAAG,WAAmB,OAAc;AAClC,SAAK,QAAQ;AACb,WAAO,IAAI;AAAA,MACT,KAAK,iBAAiB,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,WAAW;AAAA,QACX,OAAO,wBAAwB,KAAK;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,GAAG,WAAmB,OAAc;AAClC,SAAK,QAAQ;AACb,WAAO,IAAI;AAAA,MACT,KAAK,iBAAiB,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,WAAW;AAAA,QACX,OAAO,aAAa,KAAK;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,IAAI,WAAmB,OAAc;AACnC,SAAK,QAAQ;AACb,WAAO,IAAI;AAAA,MACT,KAAK,iBAAiB,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,WAAW;AAAA,QACX,OAAO,aAAa,KAAK;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,GAAG,WAAmB,OAAc;AAClC,SAAK,QAAQ;AACb,WAAO,IAAI;AAAA,MACT,KAAK,iBAAiB,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,WAAW;AAAA,QACX,OAAO,aAAa,KAAK;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,IAAI,WAAmB,OAAc;AACnC,SAAK,QAAQ;AACb,WAAO,IAAI;AAAA,MACT,KAAK,iBAAiB,OAAO;AAAA,QAC3B,MAAM;AAAA,QACN,WAAW;AAAA,QACX,OAAO,aAAa,KAAK;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,SAAS;AACP,SAAK,QAAQ;AACb,WAAO,KAAK;AAAA,EACd;AACF;", "names": []}