{"version": 3, "sources": ["../../../../src/server/impl/registration_impl.ts"], "sourcesContent": ["import {\n  ConvexError,\n  convexTo<PERSON>son,\n  GenericValidator,\n  jsonToConvex,\n  v,\n  Validator,\n  Value,\n} from \"../../values/index.js\";\nimport { GenericDataModel } from \"../data_model.js\";\nimport {\n  ActionBuilder,\n  DefaultFunctionArgs,\n  GenericActionCtx,\n  GenericMutationCtx,\n  GenericQueryCtx,\n  MutationBuilder,\n  PublicHttpAction,\n  QueryBuilder,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n} from \"../registration.js\";\nimport { setupActionCalls } from \"./actions_impl.js\";\nimport { setupActionVectorSearch } from \"./vector_search_impl.js\";\nimport { setupAuth } from \"./authentication_impl.js\";\nimport { setupReader, setupWriter } from \"./database_impl.js\";\nimport { QueryImpl, QueryInitializerImpl } from \"./query_impl.js\";\nimport {\n  setupActionScheduler,\n  setupMutationScheduler,\n} from \"./scheduler_impl.js\";\nimport {\n  setupStorageActionWriter,\n  setupStorageReader,\n  setupStorageWriter,\n} from \"./storage_impl.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { asObjectValidator } from \"../../values/validator.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nasync function invokeMutation<\n  F extends (ctx: GenericMutationCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const mutationCtx = {\n    db: setupWriter(),\n    auth: setupAuth(requestId),\n    storage: setupStorageWriter(requestId),\n    scheduler: setupMutationScheduler(),\n\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n    runMutation: (reference: any, args?: any) =>\n      runUdf(\"mutation\", reference, args),\n  };\n  const result = await invokeFunction(func, mutationCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\nexport function validateReturnValue(v: any) {\n  if (v instanceof QueryInitializerImpl || v instanceof QueryImpl) {\n    throw new Error(\n      \"Return value is a Query. Results must be retrieved with `.collect()`, `.take(n), `.unique()`, or `.first()`.\",\n    );\n  }\n}\n\nexport async function invokeFunction<\n  Ctx,\n  Args extends any[],\n  F extends (ctx: Ctx, ...args: Args) => any,\n>(func: F, ctx: Ctx, args: Args) {\n  let result;\n  try {\n    result = await Promise.resolve(func(ctx, ...args));\n  } catch (thrown: unknown) {\n    throw serializeConvexErrorData(thrown);\n  }\n  return result;\n}\n\nfunction dontCallDirectly(\n  funcType: string,\n  handler: (ctx: any, args: any) => any,\n): unknown {\n  return (ctx: any, args: any) => {\n    globalThis.console.warn(\n      \"Convex functions should not directly call other Convex functions. Consider calling a helper function instead. \" +\n        `e.g. \\`export const foo = ${funcType}(...); await foo(ctx);\\` is not supported. ` +\n        \"See https://docs.convex.dev/production/best-practices/#use-helper-functions-to-write-shared-code\",\n    );\n    return handler(ctx, args);\n  };\n}\n\n// Keep in sync with node executor\nfunction serializeConvexErrorData(thrown: unknown) {\n  if (\n    typeof thrown === \"object\" &&\n    thrown !== null &&\n    Symbol.for(\"ConvexError\") in thrown\n  ) {\n    const error = thrown as ConvexError<any>;\n    error.data = JSON.stringify(\n      convexToJson(error.data === undefined ? null : error.data),\n    );\n    (error as any).ConvexErrorSymbol = Symbol.for(\"ConvexError\");\n    return error;\n  } else {\n    return thrown;\n  }\n}\n\n/**\n * Guard against Convex functions accidentally getting included in a browser bundle.\n * Convex functions may include secret logic or credentials that should not be\n * send to untrusted clients (browsers).\n */\nfunction assertNotBrowser() {\n  if (\n    typeof window === \"undefined\" ||\n    !(window as any).__convexAllowFunctionsInBrowser\n  ) {\n    return;\n  }\n  // JSDom doesn't count, developers are allowed to use JSDom in Convex functions.\n  const isRealBrowser =\n    Object.getOwnPropertyDescriptor(globalThis, \"window\")\n      ?.get?.toString()\n      .includes(\"[native code]\") ?? false;\n  if (isRealBrowser) {\n    throw new Error(\"Convex functions should not be imported in the browser.\");\n  }\n}\n\ntype FunctionDefinition =\n  | ((ctx: any, args: DefaultFunctionArgs) => any)\n  | {\n      args?: GenericValidator | Record<string, GenericValidator>;\n      returns?: GenericValidator | Record<string, GenericValidator>;\n      handler: (ctx: any, args: DefaultFunctionArgs) => any;\n    };\n\nfunction exportArgs(functionDefinition: FunctionDefinition) {\n  return () => {\n    let args: GenericValidator = v.any();\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.args !== undefined\n    ) {\n      args = asObjectValidator(functionDefinition.args);\n    }\n    return JSON.stringify(args.json);\n  };\n}\n\nfunction exportReturns(functionDefinition: FunctionDefinition) {\n  return () => {\n    let returns: Validator<any, any, any> | undefined;\n    if (\n      typeof functionDefinition === \"object\" &&\n      functionDefinition.returns !== undefined\n    ) {\n      returns = asObjectValidator(functionDefinition.returns);\n    }\n    return JSON.stringify(returns ? returns.json : null);\n  };\n}\n\n/**\n * Define a mutation in this Convex app's public API.\n *\n * This function will be allowed to modify your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `mutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const mutationGeneric: MutationBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"mutation\", handler) as RegisteredMutation<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isPublic = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"public\">;\n\n/**\n * Define a mutation that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to modify your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalMutation` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The mutation function. It receives a {@link GenericMutationCtx} as its first argument.\n * @returns The wrapped mutation. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalMutationGeneric: MutationBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericMutationCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\n    \"internalMutation\",\n    handler,\n  ) as RegisteredMutation<\"internal\", any, any>;\n\n  assertNotBrowser();\n  func.isMutation = true;\n  func.isInternal = true;\n  func.invokeMutation = (argsStr) => invokeMutation(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as MutationBuilder<any, \"internal\">;\n\nasync function invokeQuery<\n  F extends (ctx: GenericQueryCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, argsStr: string) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since queries are only running in V8.\n  const requestId = \"\";\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const queryCtx = {\n    db: setupReader(),\n    auth: setupAuth(requestId),\n    storage: setupStorageReader(requestId),\n    runQuery: (reference: any, args?: any) => runUdf(\"query\", reference, args),\n  };\n  const result = await invokeFunction(func, queryCtx, args as any);\n  validateReturnValue(result);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define a query in this Convex app's public API.\n *\n * This function will be allowed to read your Convex database and will be accessible from the client.\n *\n * If you're using code generation, use the `query` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const queryGeneric: QueryBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"query\", handler) as RegisteredQuery<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isPublic = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"public\">;\n\n/**\n * Define a query that is only accessible from other Convex functions (but not from the client).\n *\n * This function will be allowed to read from your Convex database. It will not be accessible from the client.\n *\n * If you're using code generation, use the `internalQuery` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The query function. It receives a {@link GenericQueryCtx} as its first argument.\n * @returns The wrapped query. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalQueryGeneric: QueryBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericQueryCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalQuery\", handler) as RegisteredQuery<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isQuery = true;\n  func.isInternal = true;\n  func.invokeQuery = (argsStr) => invokeQuery(handler as any, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as QueryBuilder<any, \"internal\">;\n\nasync function invokeAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, ...args: any) => any,\n>(func: F, requestId: string, argsStr: string) {\n  const args = jsonToConvex(JSON.parse(argsStr));\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    scheduler: setupActionScheduler(requestId),\n    storage: setupStorageActionWriter(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  const result = await invokeFunction(func, ctx, args as any);\n  return JSON.stringify(convexToJson(result === undefined ? null : result));\n}\n\n/**\n * Define an action in this Convex app's public API.\n *\n * If you're using code generation, use the `action` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const actionGeneric: ActionBuilder<any, \"public\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"action\", handler) as RegisteredAction<\n    \"public\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isPublic = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"public\">;\n\n/**\n * Define an action that is only accessible from other Convex functions (but not from the client).\n *\n * If you're using code generation, use the `internalAction` function in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @param func - The function. It receives a {@link GenericActionCtx} as its first argument.\n * @returns The wrapped function. Include this as an `export` to name it and make it accessible.\n *\n * @public\n */\nexport const internalActionGeneric: ActionBuilder<any, \"internal\"> = ((\n  functionDefinition: FunctionDefinition,\n) => {\n  const handler = (\n    typeof functionDefinition === \"function\"\n      ? functionDefinition\n      : functionDefinition.handler\n  ) as (ctx: GenericActionCtx<any>, args: any) => any;\n  const func = dontCallDirectly(\"internalAction\", handler) as RegisteredAction<\n    \"internal\",\n    any,\n    any\n  >;\n\n  assertNotBrowser();\n  func.isAction = true;\n  func.isInternal = true;\n  func.invokeAction = (requestId, argsStr) =>\n    invokeAction(handler, requestId, argsStr);\n  func.exportArgs = exportArgs(functionDefinition);\n  func.exportReturns = exportReturns(functionDefinition);\n  func._handler = handler;\n  return func;\n}) as ActionBuilder<any, \"internal\">;\n\nasync function invokeHttpAction<\n  F extends (ctx: GenericActionCtx<GenericDataModel>, request: Request) => any,\n>(func: F, request: Request) {\n  // TODO(presley): Change the function signature and propagate the requestId from Rust.\n  // Ok, to mock it out for now, since http endpoints are only running in V8.\n  const requestId = \"\";\n  const calls = setupActionCalls(requestId);\n  const ctx = {\n    ...calls,\n    auth: setupAuth(requestId),\n    storage: setupStorageActionWriter(requestId),\n    scheduler: setupActionScheduler(requestId),\n    vectorSearch: setupActionVectorSearch(requestId) as any,\n  };\n  return await invokeFunction(func, ctx, [request]);\n}\n\n/**\n * Define a Convex HTTP action.\n *\n * @param func - The function. It receives an {@link GenericActionCtx} as its first argument, and a `Request` object\n * as its second.\n * @returns The wrapped function. Route a URL path to this function in `convex/http.js`.\n *\n * @public\n */\nexport const httpActionGeneric = (\n  func: (\n    ctx: GenericActionCtx<GenericDataModel>,\n    request: Request,\n  ) => Promise<Response>,\n): PublicHttpAction => {\n  const q = dontCallDirectly(\"httpAction\", func) as PublicHttpAction;\n  assertNotBrowser();\n  q.isHttp = true;\n  q.invokeHttpAction = (request) => invokeHttpAction(func as any, request);\n  q._handler = func;\n  return q;\n};\n\nasync function runUdf(\n  udfType: \"query\" | \"mutation\",\n  f: any,\n  args?: Record<string, Value>,\n): Promise<any> {\n  const queryArgs = parseArgs(args);\n  const syscallArgs = {\n    udfType,\n    args: convexToJson(queryArgs),\n    ...getFunctionAddress(f),\n  };\n  const result = await performAsyncSyscall(\"1.0/runUdf\", syscallArgs);\n  return jsonToConvex(result);\n}\n"], "mappings": ";AAAA;AAAA,EAEE;AAAA,EAEA;AAAA,EACA;AAAA,OAGK;AAeP,SAAS,wBAAwB;AACjC,SAAS,+BAA+B;AACxC,SAAS,iBAAiB;AAC1B,SAAS,aAAa,mBAAmB;AACzC,SAAS,WAAW,4BAA4B;AAChD;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,iBAAiB;AAC1B,SAAS,2BAA2B;AACpC,SAAS,yBAAyB;AAClC,SAAS,0BAA0B;AAEnC,eAAe,eAEb,MAAS,SAAiB;AAG1B,QAAM,YAAY;AAClB,QAAM,OAAO,aAAa,KAAK,MAAM,OAAO,CAAC;AAC7C,QAAM,cAAc;AAAA,IAClB,IAAI,YAAY;AAAA,IAChB,MAAM,UAAU,SAAS;AAAA,IACzB,SAAS,mBAAmB,SAAS;AAAA,IACrC,WAAW,uBAAuB;AAAA,IAElC,UAAU,CAAC,WAAgBA,UAAe,OAAO,SAAS,WAAWA,KAAI;AAAA,IACzE,aAAa,CAAC,WAAgBA,UAC5B,OAAO,YAAY,WAAWA,KAAI;AAAA,EACtC;AACA,QAAM,SAAS,MAAM,eAAe,MAAM,aAAa,IAAW;AAClE,sBAAoB,MAAM;AAC1B,SAAO,KAAK,UAAU,aAAa,WAAW,SAAY,OAAO,MAAM,CAAC;AAC1E;AAEO,gBAAS,oBAAoBC,IAAQ;AAC1C,MAAIA,cAAa,wBAAwBA,cAAa,WAAW;AAC/D,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAEA,sBAAsB,eAIpB,MAAS,KAAU,MAAY;AAC/B,MAAI;AACJ,MAAI;AACF,aAAS,MAAM,QAAQ,QAAQ,KAAK,KAAK,GAAG,IAAI,CAAC;AAAA,EACnD,SAAS,QAAiB;AACxB,UAAM,yBAAyB,MAAM;AAAA,EACvC;AACA,SAAO;AACT;AAEA,SAAS,iBACP,UACA,SACS;AACT,SAAO,CAAC,KAAU,SAAc;AAC9B,eAAW,QAAQ;AAAA,MACjB,2IAC+B,QAAQ;AAAA,IAEzC;AACA,WAAO,QAAQ,KAAK,IAAI;AAAA,EAC1B;AACF;AAGA,SAAS,yBAAyB,QAAiB;AACjD,MACE,OAAO,WAAW,YAClB,WAAW,QACX,OAAO,IAAI,aAAa,KAAK,QAC7B;AACA,UAAM,QAAQ;AACd,UAAM,OAAO,KAAK;AAAA,MAChB,aAAa,MAAM,SAAS,SAAY,OAAO,MAAM,IAAI;AAAA,IAC3D;AACA,IAAC,MAAc,oBAAoB,OAAO,IAAI,aAAa;AAC3D,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAOA,SAAS,mBAAmB;AAC1B,MACE,OAAO,WAAW,eAClB,CAAE,OAAe,iCACjB;AACA;AAAA,EACF;AAEA,QAAM,gBACJ,OAAO,yBAAyB,YAAY,QAAQ,GAChD,KAAK,SAAS,EACf,SAAS,eAAe,KAAK;AAClC,MAAI,eAAe;AACjB,UAAM,IAAI,MAAM,yDAAyD;AAAA,EAC3E;AACF;AAUA,SAAS,WAAW,oBAAwC;AAC1D,SAAO,MAAM;AACX,QAAI,OAAyB,EAAE,IAAI;AACnC,QACE,OAAO,uBAAuB,YAC9B,mBAAmB,SAAS,QAC5B;AACA,aAAO,kBAAkB,mBAAmB,IAAI;AAAA,IAClD;AACA,WAAO,KAAK,UAAU,KAAK,IAAI;AAAA,EACjC;AACF;AAEA,SAAS,cAAc,oBAAwC;AAC7D,SAAO,MAAM;AACX,QAAI;AACJ,QACE,OAAO,uBAAuB,YAC9B,mBAAmB,YAAY,QAC/B;AACA,gBAAU,kBAAkB,mBAAmB,OAAO;AAAA,IACxD;AACA,WAAO,KAAK,UAAU,UAAU,QAAQ,OAAO,IAAI;AAAA,EACrD;AACF;AAeO,aAAM,kBAAmD,CAC9D,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,YAAY,OAAO;AAMjD,mBAAiB;AACjB,OAAK,aAAa;AAClB,OAAK,WAAW;AAChB,OAAK,iBAAiB,CAAC,YAAY,eAAe,SAAS,OAAO;AAClE,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAeO,aAAM,0BAA6D,CACxE,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAEA,mBAAiB;AACjB,OAAK,aAAa;AAClB,OAAK,aAAa;AAClB,OAAK,iBAAiB,CAAC,YAAY,eAAe,SAAS,OAAO;AAClE,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAEA,eAAe,YAEb,MAAS,SAAiB;AAG1B,QAAM,YAAY;AAClB,QAAM,OAAO,aAAa,KAAK,MAAM,OAAO,CAAC;AAC7C,QAAM,WAAW;AAAA,IACf,IAAI,YAAY;AAAA,IAChB,MAAM,UAAU,SAAS;AAAA,IACzB,SAAS,mBAAmB,SAAS;AAAA,IACrC,UAAU,CAAC,WAAgBD,UAAe,OAAO,SAAS,WAAWA,KAAI;AAAA,EAC3E;AACA,QAAM,SAAS,MAAM,eAAe,MAAM,UAAU,IAAW;AAC/D,sBAAoB,MAAM;AAC1B,SAAO,KAAK,UAAU,aAAa,WAAW,SAAY,OAAO,MAAM,CAAC;AAC1E;AAeO,aAAM,eAA6C,CACxD,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,SAAS,OAAO;AAM9C,mBAAiB;AACjB,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,cAAc,CAAC,YAAY,YAAY,SAAS,OAAO;AAC5D,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAeO,aAAM,uBAAuD,CAClE,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,iBAAiB,OAAO;AAMtD,mBAAiB;AACjB,OAAK,UAAU;AACf,OAAK,aAAa;AAClB,OAAK,cAAc,CAAC,YAAY,YAAY,SAAgB,OAAO;AACnE,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAEA,eAAe,aAEb,MAAS,WAAmB,SAAiB;AAC7C,QAAM,OAAO,aAAa,KAAK,MAAM,OAAO,CAAC;AAC7C,QAAM,QAAQ,iBAAiB,SAAS;AACxC,QAAM,MAAM;AAAA,IACV,GAAG;AAAA,IACH,MAAM,UAAU,SAAS;AAAA,IACzB,WAAW,qBAAqB,SAAS;AAAA,IACzC,SAAS,yBAAyB,SAAS;AAAA,IAC3C,cAAc,wBAAwB,SAAS;AAAA,EACjD;AACA,QAAM,SAAS,MAAM,eAAe,MAAM,KAAK,IAAW;AAC1D,SAAO,KAAK,UAAU,aAAa,WAAW,SAAY,OAAO,MAAM,CAAC;AAC1E;AAaO,aAAM,gBAA+C,CAC1D,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,UAAU,OAAO;AAM/C,mBAAiB;AACjB,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,eAAe,CAAC,WAAW,YAC9B,aAAa,SAAS,WAAW,OAAO;AAC1C,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAaO,aAAM,wBAAyD,CACpE,uBACG;AACH,QAAM,UACJ,OAAO,uBAAuB,aAC1B,qBACA,mBAAmB;AAEzB,QAAM,OAAO,iBAAiB,kBAAkB,OAAO;AAMvD,mBAAiB;AACjB,OAAK,WAAW;AAChB,OAAK,aAAa;AAClB,OAAK,eAAe,CAAC,WAAW,YAC9B,aAAa,SAAS,WAAW,OAAO;AAC1C,OAAK,aAAa,WAAW,kBAAkB;AAC/C,OAAK,gBAAgB,cAAc,kBAAkB;AACrD,OAAK,WAAW;AAChB,SAAO;AACT;AAEA,eAAe,iBAEb,MAAS,SAAkB;AAG3B,QAAM,YAAY;AAClB,QAAM,QAAQ,iBAAiB,SAAS;AACxC,QAAM,MAAM;AAAA,IACV,GAAG;AAAA,IACH,MAAM,UAAU,SAAS;AAAA,IACzB,SAAS,yBAAyB,SAAS;AAAA,IAC3C,WAAW,qBAAqB,SAAS;AAAA,IACzC,cAAc,wBAAwB,SAAS;AAAA,EACjD;AACA,SAAO,MAAM,eAAe,MAAM,KAAK,CAAC,OAAO,CAAC;AAClD;AAWO,aAAM,oBAAoB,CAC/B,SAIqB;AACrB,QAAM,IAAI,iBAAiB,cAAc,IAAI;AAC7C,mBAAiB;AACjB,IAAE,SAAS;AACX,IAAE,mBAAmB,CAAC,YAAY,iBAAiB,MAAa,OAAO;AACvE,IAAE,WAAW;AACb,SAAO;AACT;AAEA,eAAe,OACb,SACA,GACA,MACc;AACd,QAAM,YAAY,UAAU,IAAI;AAChC,QAAM,cAAc;AAAA,IAClB;AAAA,IACA,MAAM,aAAa,SAAS;AAAA,IAC5B,GAAG,mBAAmB,CAAC;AAAA,EACzB;AACA,QAAM,SAAS,MAAM,oBAAoB,cAAc,WAAW;AAClE,SAAO,aAAa,MAAM;AAC5B;", "names": ["args", "v"]}