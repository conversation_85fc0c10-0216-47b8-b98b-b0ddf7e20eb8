{"version": 3, "sources": ["../../../../src/server/impl/storage_impl.ts"], "sourcesContent": ["import {\n  FileMetadata,\n  StorageActionWriter,\n  FileStorageId,\n  StorageReader,\n  StorageWriter,\n} from \"../storage.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall, performJsSyscall } from \"./syscall.js\";\nimport { validateArg } from \"./validate.js\";\n\nexport function setupStorageReader(requestId: string): StorageReader {\n  return {\n    getUrl: async (storageId: FileStorageId) => {\n      validateArg(storageId, 1, \"getUrl\", \"storageId\");\n      return await performAsyncSyscall(\"1.0/storageGetUrl\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getMetadata: async (storageId: FileStorageId): Promise<FileMetadata> => {\n      return await performAsyncSyscall(\"1.0/storageGetMetadata\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n\nexport function setupStorageWriter(requestId: string): StorageWriter {\n  const reader = setupStorageReader(requestId);\n  return {\n    generateUploadUrl: async () => {\n      return await performAsyncSyscall(\"1.0/storageGenerateUploadUrl\", {\n        requestId,\n        version,\n      });\n    },\n    delete: async (storageId: FileStorageId) => {\n      await performAsyncSyscall(\"1.0/storageDelete\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n    getUrl: reader.getUrl,\n    getMetadata: reader.getMetadata,\n  };\n}\n\nexport function setupStorageActionWriter(\n  requestId: string,\n): StorageActionWriter {\n  const writer = setupStorageWriter(requestId);\n  return {\n    ...writer,\n    store: async (blob: Blob, options?: { sha256?: string }) => {\n      return await performJsSyscall(\"storage/storeBlob\", {\n        requestId,\n        version,\n        blob,\n        options,\n      });\n    },\n    get: async (storageId: FileStorageId) => {\n      return await performJsSyscall(\"storage/getBlob\", {\n        requestId,\n        version,\n        storageId,\n      });\n    },\n  };\n}\n"], "mappings": ";AAOA,SAAS,eAAe;AACxB,SAAS,qBAAqB,wBAAwB;AACtD,SAAS,mBAAmB;AAErB,gBAAS,mBAAmB,WAAkC;AACnE,SAAO;AAAA,IACL,QAAQ,OAAO,cAA6B;AAC1C,kBAAY,WAAW,GAAG,UAAU,WAAW;AAC/C,aAAO,MAAM,oBAAoB,qBAAqB;AAAA,QACpD;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,aAAa,OAAO,cAAoD;AACtE,aAAO,MAAM,oBAAoB,0BAA0B;AAAA,QACzD;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEO,gBAAS,mBAAmB,WAAkC;AACnE,QAAM,SAAS,mBAAmB,SAAS;AAC3C,SAAO;AAAA,IACL,mBAAmB,YAAY;AAC7B,aAAO,MAAM,oBAAoB,gCAAgC;AAAA,QAC/D;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,QAAQ,OAAO,cAA6B;AAC1C,YAAM,oBAAoB,qBAAqB;AAAA,QAC7C;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,QAAQ,OAAO;AAAA,IACf,aAAa,OAAO;AAAA,EACtB;AACF;AAEO,gBAAS,yBACd,WACqB;AACrB,QAAM,SAAS,mBAAmB,SAAS;AAC3C,SAAO;AAAA,IACL,GAAG;AAAA,IACH,OAAO,OAAO,MAAY,YAAkC;AAC1D,aAAO,MAAM,iBAAiB,qBAAqB;AAAA,QACjD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,KAAK,OAAO,cAA6B;AACvC,aAAO,MAAM,iBAAiB,mBAAmB;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;", "names": []}