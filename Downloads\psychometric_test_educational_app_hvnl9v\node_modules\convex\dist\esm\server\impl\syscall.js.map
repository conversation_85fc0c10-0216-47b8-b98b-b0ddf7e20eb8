{"version": 3, "sources": ["../../../../src/server/impl/syscall.ts"], "sourcesContent": ["import { ConvexError } from \"../../values/errors.js\";\nimport { jsonToConvex } from \"../../values/value.js\";\n\ndeclare const Convex: {\n  syscall: (op: string, jsonArgs: string) => string;\n  asyncSyscall: (op: string, jsonArgs: string) => Promise<string>;\n  jsSyscall: (op: string, args: Record<string, any>) => any;\n};\n/**\n * Perform a syscall, taking in a JSON-encodable object as an argument, serializing with\n * JSON.stringify, calling into Rust, and then parsing the response as a JSON-encodable\n * value. If one of your arguments is a Convex value, you must call `convexToJson` on it\n * before passing it to this function, and if the return value has a Convex value, you're\n * also responsible for calling `jsonToConvex`: This layer only deals in JSON.\n */\n\nexport function performSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.syscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  const resultStr = Convex.syscall(op, JSON.stringify(arg));\n  return JSON.parse(resultStr);\n}\n\nexport async function performAsyncSyscall(\n  op: string,\n  arg: Record<string, any>,\n): Promise<any> {\n  if (typeof Convex === \"undefined\" || Convex.asyncSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  let resultStr;\n  try {\n    resultStr = await Convex.asyncSyscall(op, JSON.stringify(arg));\n  } catch (e: any) {\n    // Rethrow the exception to attach stack trace starting from here.\n    // If the error came from JS it will include its own stack trace in the message.\n    // If it came from Rust it won't.\n\n    // This only happens if we're propagating ConvexErrors\n    if (e.data !== undefined) {\n      const rethrown = new ConvexError(e.message);\n      rethrown.data = jsonToConvex(e.data);\n      throw rethrown;\n    }\n    throw new Error(e.message);\n  }\n  return JSON.parse(resultStr);\n}\n\n/**\n * Call into a \"JS\" syscall. Like `performSyscall`, this calls a dynamically linked\n * function set up in the Convex function execution. Unlike `performSyscall`, the\n * arguments do not need to be JSON-encodable and neither does the return value.\n *\n * @param op\n * @param arg\n * @returns\n */\nexport function performJsSyscall(op: string, arg: Record<string, any>): any {\n  if (typeof Convex === \"undefined\" || Convex.jsSyscall === undefined) {\n    throw new Error(\n      \"The Convex database and auth objects are being used outside of a Convex backend. \" +\n        \"Did you mean to use `useQuery` or `useMutation` to call a Convex function?\",\n    );\n  }\n  return Convex.jsSyscall(op, arg);\n}\n"], "mappings": ";AAAA,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;AAetB,gBAAS,eAAe,IAAY,KAA+B;AACxE,MAAI,OAAO,WAAW,eAAe,OAAO,YAAY,QAAW;AACjE,UAAM,IAAI;AAAA,MACR;AAAA,IAEF;AAAA,EACF;AACA,QAAM,YAAY,OAAO,QAAQ,IAAI,KAAK,UAAU,GAAG,CAAC;AACxD,SAAO,KAAK,MAAM,SAAS;AAC7B;AAEA,sBAAsB,oBACpB,IACA,KACc;AACd,MAAI,OAAO,WAAW,eAAe,OAAO,iBAAiB,QAAW;AACtE,UAAM,IAAI;AAAA,MACR;AAAA,IAEF;AAAA,EACF;AACA,MAAI;AACJ,MAAI;AACF,gBAAY,MAAM,OAAO,aAAa,IAAI,KAAK,UAAU,GAAG,CAAC;AAAA,EAC/D,SAAS,GAAQ;AAMf,QAAI,EAAE,SAAS,QAAW;AACxB,YAAM,WAAW,IAAI,YAAY,EAAE,OAAO;AAC1C,eAAS,OAAO,aAAa,EAAE,IAAI;AACnC,YAAM;AAAA,IACR;AACA,UAAM,IAAI,MAAM,EAAE,OAAO;AAAA,EAC3B;AACA,SAAO,KAAK,MAAM,SAAS;AAC7B;AAWO,gBAAS,iBAAiB,IAAY,KAA+B;AAC1E,MAAI,OAAO,WAAW,eAAe,OAAO,cAAc,QAAW;AACnE,UAAM,IAAI;AAAA,MACR;AAAA,IAEF;AAAA,EACF;AACA,SAAO,OAAO,UAAU,IAAI,GAAG;AACjC;", "names": []}