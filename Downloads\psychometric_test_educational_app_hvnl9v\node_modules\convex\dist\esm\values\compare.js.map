{"version": 3, "sources": ["../../../src/values/compare.ts"], "sourcesContent": ["import { Value } from \"./value.js\";\nimport { compareUTF8 } from \"./compare_utf8.js\";\n\nexport function compareValues(k1: Value | undefined, k2: Value | undefined) {\n  return compareAsTuples(makeComparable(k1), makeComparable(k2));\n}\n\nfunction compareAsTuples<T>(a: [number, T], b: [number, T]): number {\n  if (a[0] === b[0]) {\n    return compareSameTypeValues(a[1], b[1]);\n  } else if (a[0] < b[0]) {\n    return -1;\n  }\n  return 1;\n}\n\nfunction compareSameTypeValues<T>(v1: T, v2: T): number {\n  if (v1 === undefined || v1 === null) {\n    return 0;\n  }\n  if (typeof v1 === \"number\") {\n    if (typeof v2 !== \"number\") {\n      throw new Error(`Unexpected type ${v2 as any}`);\n    }\n    return compareNumbers(v1, v2);\n  }\n  if (typeof v1 === \"string\") {\n    if (typeof v2 !== \"string\") {\n      throw new Error(`Unexpected type ${v2 as any}`);\n    }\n    return compareUTF8(v1, v2);\n  }\n  if (\n    typeof v1 === \"bigint\" ||\n    typeof v1 === \"boolean\" ||\n    typeof v1 === \"string\"\n  ) {\n    return v1 < v2 ? -1 : v1 === v2 ? 0 : 1;\n  }\n  if (!Array.isArray(v1) || !Array.isArray(v2)) {\n    throw new Error(`Unexpected type ${v1 as any}`);\n  }\n  for (let i = 0; i < v1.length && i < v2.length; i++) {\n    const cmp = compareAsTuples(v1[i], v2[i]);\n    if (cmp !== 0) {\n      return cmp;\n    }\n  }\n  if (v1.length < v2.length) {\n    return -1;\n  }\n  if (v1.length > v2.length) {\n    return 1;\n  }\n  return 0;\n}\n\nfunction compareNumbers(v1: number, v2: number): number {\n  // Handle NaN values\n  if (isNaN(v1) || isNaN(v2)) {\n    // Create DataViews for bit-level comparison\n    const buffer1 = new ArrayBuffer(8);\n    const buffer2 = new ArrayBuffer(8);\n    new DataView(buffer1).setFloat64(0, v1, /* little-endian */ true);\n    new DataView(buffer2).setFloat64(0, v2, /* little-endian */ true);\n\n    // Read as BigInt to compare bits\n    const v1Bits = BigInt(\n      new DataView(buffer1).getBigInt64(0, /* little-endian */ true),\n    );\n    const v2Bits = BigInt(\n      new DataView(buffer2).getBigInt64(0, /* little-endian */ true),\n    );\n\n    // The sign bit is the most significant bit (bit 63)\n    const v1Sign = (v1Bits & 0x8000000000000000n) !== 0n;\n    const v2Sign = (v2Bits & 0x8000000000000000n) !== 0n;\n\n    // If one value is NaN and the other isn't, use sign bits first\n    if (isNaN(v1) !== isNaN(v2)) {\n      // If v1 is NaN, compare based on sign bits\n      if (isNaN(v1)) {\n        return v1Sign ? -1 : 1;\n      }\n      // If v2 is NaN, compare based on sign bits\n      return v2Sign ? 1 : -1;\n    }\n\n    // If both are NaN, compare their binary representations\n    if (v1Sign !== v2Sign) {\n      return v1Sign ? -1 : 1; // true means negative\n    }\n    return v1Bits < v2Bits ? -1 : v1Bits === v2Bits ? 0 : 1;\n  }\n\n  if (Object.is(v1, v2)) {\n    return 0;\n  }\n\n  if (Object.is(v1, -0)) {\n    return Object.is(v2, 0) ? -1 : -Math.sign(v2);\n  }\n  if (Object.is(v2, -0)) {\n    return Object.is(v1, 0) ? 1 : Math.sign(v1);\n  }\n\n  // Handle regular number comparison\n  return v1 < v2 ? -1 : 1;\n}\n\n// Returns an array which can be compared to other arrays as if they were tuples.\n// For example, [1, null] < [2, 1n] means null sorts before all bigints\n// And [3, 5] < [3, 6] means floats sort as expected\n// And [7, [[5, \"a\"]]] < [7, [[5, \"a\"], [5, \"b\"]]] means arrays sort as expected\nfunction makeComparable(v: Value | undefined): [number, any] {\n  if (v === undefined) {\n    return [0, undefined];\n  }\n  if (v === null) {\n    return [1, null];\n  }\n  if (typeof v === \"bigint\") {\n    return [2, v];\n  }\n  if (typeof v === \"number\") {\n    return [3, v];\n  }\n  if (typeof v === \"boolean\") {\n    return [4, v];\n  }\n  if (typeof v === \"string\") {\n    return [5, v];\n  }\n  if (v instanceof ArrayBuffer) {\n    return [6, Array.from(new Uint8Array(v)).map(makeComparable)];\n  }\n  if (Array.isArray(v)) {\n    return [7, v.map(makeComparable)];\n  }\n  // Otherwise, it's an POJO.\n  const keys = Object.keys(v).sort();\n  const pojo: Value[] = keys.map((k) => [k, v[k]!]);\n  return [8, pojo.map(makeComparable)];\n}\n"], "mappings": ";AACA,SAAS,mBAAmB;AAErB,gBAAS,cAAc,IAAuB,IAAuB;AAC1E,SAAO,gBAAgB,eAAe,EAAE,GAAG,eAAe,EAAE,CAAC;AAC/D;AAEA,SAAS,gBAAmB,GAAgB,GAAwB;AAClE,MAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACjB,WAAO,sBAAsB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACzC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AACtB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAyB,IAAO,IAAe;AACtD,MAAI,OAAO,UAAa,OAAO,MAAM;AACnC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,OAAO,UAAU;AAC1B,QAAI,OAAO,OAAO,UAAU;AAC1B,YAAM,IAAI,MAAM,mBAAmB,EAAS,EAAE;AAAA,IAChD;AACA,WAAO,eAAe,IAAI,EAAE;AAAA,EAC9B;AACA,MAAI,OAAO,OAAO,UAAU;AAC1B,QAAI,OAAO,OAAO,UAAU;AAC1B,YAAM,IAAI,MAAM,mBAAmB,EAAS,EAAE;AAAA,IAChD;AACA,WAAO,YAAY,IAAI,EAAE;AAAA,EAC3B;AACA,MACE,OAAO,OAAO,YACd,OAAO,OAAO,aACd,OAAO,OAAO,UACd;AACA,WAAO,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI;AAAA,EACxC;AACA,MAAI,CAAC,MAAM,QAAQ,EAAE,KAAK,CAAC,MAAM,QAAQ,EAAE,GAAG;AAC5C,UAAM,IAAI,MAAM,mBAAmB,EAAS,EAAE;AAAA,EAChD;AACA,WAAS,IAAI,GAAG,IAAI,GAAG,UAAU,IAAI,GAAG,QAAQ,KAAK;AACnD,UAAM,MAAM,gBAAgB,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACxC,QAAI,QAAQ,GAAG;AACb,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,GAAG,SAAS,GAAG,QAAQ;AACzB,WAAO;AAAA,EACT;AACA,MAAI,GAAG,SAAS,GAAG,QAAQ;AACzB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,eAAe,IAAY,IAAoB;AAEtD,MAAI,MAAM,EAAE,KAAK,MAAM,EAAE,GAAG;AAE1B,UAAM,UAAU,IAAI,YAAY,CAAC;AACjC,UAAM,UAAU,IAAI,YAAY,CAAC;AACjC,QAAI,SAAS,OAAO,EAAE;AAAA,MAAW;AAAA,MAAG;AAAA;AAAA,MAAwB;AAAA,IAAI;AAChE,QAAI,SAAS,OAAO,EAAE;AAAA,MAAW;AAAA,MAAG;AAAA;AAAA,MAAwB;AAAA,IAAI;AAGhE,UAAM,SAAS;AAAA,MACb,IAAI,SAAS,OAAO,EAAE;AAAA,QAAY;AAAA;AAAA,QAAuB;AAAA,MAAI;AAAA,IAC/D;AACA,UAAM,SAAS;AAAA,MACb,IAAI,SAAS,OAAO,EAAE;AAAA,QAAY;AAAA;AAAA,QAAuB;AAAA,MAAI;AAAA,IAC/D;AAGA,UAAM,UAAU,SAAS,yBAAyB;AAClD,UAAM,UAAU,SAAS,yBAAyB;AAGlD,QAAI,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG;AAE3B,UAAI,MAAM,EAAE,GAAG;AACb,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,aAAO,SAAS,IAAI;AAAA,IACtB;AAGA,QAAI,WAAW,QAAQ;AACrB,aAAO,SAAS,KAAK;AAAA,IACvB;AACA,WAAO,SAAS,SAAS,KAAK,WAAW,SAAS,IAAI;AAAA,EACxD;AAEA,MAAI,OAAO,GAAG,IAAI,EAAE,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,GAAG,IAAI,EAAE,GAAG;AACrB,WAAO,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,EAAE;AAAA,EAC9C;AACA,MAAI,OAAO,GAAG,IAAI,EAAE,GAAG;AACrB,WAAO,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,EAAE;AAAA,EAC5C;AAGA,SAAO,KAAK,KAAK,KAAK;AACxB;AAMA,SAAS,eAAe,GAAqC;AAC3D,MAAI,MAAM,QAAW;AACnB,WAAO,CAAC,GAAG,MAAS;AAAA,EACtB;AACA,MAAI,MAAM,MAAM;AACd,WAAO,CAAC,GAAG,IAAI;AAAA,EACjB;AACA,MAAI,OAAO,MAAM,UAAU;AACzB,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AACA,MAAI,OAAO,MAAM,UAAU;AACzB,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AACA,MAAI,OAAO,MAAM,WAAW;AAC1B,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AACA,MAAI,OAAO,MAAM,UAAU;AACzB,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AACA,MAAI,aAAa,aAAa;AAC5B,WAAO,CAAC,GAAG,MAAM,KAAK,IAAI,WAAW,CAAC,CAAC,EAAE,IAAI,cAAc,CAAC;AAAA,EAC9D;AACA,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,WAAO,CAAC,GAAG,EAAE,IAAI,cAAc,CAAC;AAAA,EAClC;AAEA,QAAM,OAAO,OAAO,KAAK,CAAC,EAAE,KAAK;AACjC,QAAM,OAAgB,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAE,CAAC;AAChD,SAAO,CAAC,GAAG,KAAK,IAAI,cAAc,CAAC;AACrC;", "names": []}