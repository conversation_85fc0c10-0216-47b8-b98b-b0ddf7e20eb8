{"version": 3, "sources": ["../../../src/values/compare_utf8.ts"], "sourcesContent": ["/**\n * Taken from https://github.com/rocicorp/compare-utf8/blob/main/LICENSE\n * (Apache Version 2.0, January 2004)\n */\n\n/**\n * This is copied here instead of added as a dependency to avoid bundling issues.\n */\n\n/**\n * Compares two JavaScript strings as if they were UTF-8 encoded byte arrays.\n * @param {string} a\n * @param {string} b\n * @returns {number}\n */\nexport function compareUTF8(a: string, b: string): number {\n  const aLength = a.length;\n  const bLength = b.length;\n  const length = Math.min(aLength, bLength);\n  for (let i = 0; i < length; ) {\n    const aCodePoint = a.codePointAt(i)!;\n    const bCodePoint = b.codePointAt(i)!;\n    if (aCodePoint !== bCodePoint) {\n      // Code points below 0x80 are represented the same way in UTF-8 as in\n      // UTF-16.\n      if (aCodePoint < 0x80 && bCodePoint < 0x80) {\n        return aCodePoint - bCodePoint;\n      }\n\n      // get the UTF-8 bytes for the code points\n      const aLength = utf8Bytes(aCodePoint, aBytes);\n      const bLength = utf8Bytes(bCodePoint, bBytes);\n      return compareArrays(aBytes, aLength, bBytes, bLength);\n    }\n\n    i += utf16LengthForCodePoint(aCodePoint);\n  }\n\n  return aLength - bLength;\n}\n\n/**\n * @param {number[]} a\n * @param {number} aLength\n * @param {number[]} b\n * @param {number} bLength\n * @returns {number}\n */\nfunction compareArrays(\n  a: number[],\n  aLength: number,\n  b: number[],\n  bLength: number,\n) {\n  const length = Math.min(aLength, bLength);\n  for (let i = 0; i < length; i++) {\n    const aValue = a[i];\n    const bValue = b[i];\n    if (aValue !== bValue) {\n      return aValue - bValue;\n    }\n  }\n  return aLength - bLength;\n}\n\n/**\n * @param {number} aCodePoint\n * @returns {number}\n */\nexport function utf16LengthForCodePoint(aCodePoint: number) {\n  return aCodePoint > 0xffff ? 2 : 1;\n}\n\n// 2 preallocated arrays for utf8Bytes.\nconst arr = () => Array.from({ length: 4 }, () => 0);\nconst aBytes = arr();\nconst bBytes = arr();\n\n/**\n * @param {number} codePoint\n * @param {number[]} bytes\n * @returns {number}\n */\nfunction utf8Bytes(codePoint: number, bytes: number[]) {\n  if (codePoint < 0x80) {\n    bytes[0] = codePoint;\n    return 1;\n  }\n\n  let count;\n  let offset;\n\n  if (codePoint <= 0x07ff) {\n    count = 1;\n    offset = 0xc0;\n  } else if (codePoint <= 0xffff) {\n    count = 2;\n    offset = 0xe0;\n  } else if (codePoint <= 0x10ffff) {\n    count = 3;\n    offset = 0xf0;\n  } else {\n    throw new Error(\"Invalid code point\");\n  }\n\n  bytes[0] = (codePoint >> (6 * count)) + offset;\n  let i = 1;\n  for (; count > 0; count--) {\n    const temp = codePoint >> (6 * (count - 1));\n    bytes[i++] = 0x80 | (temp & 0x3f);\n  }\n  return i;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function greaterThan(a: string, b: string) {\n  return compareUTF8(a, b) > 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function greaterThanEq(a: string, b: string) {\n  return compareUTF8(a, b) >= 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function lessThan(a: string, b: string) {\n  return compareUTF8(a, b) < 0;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns {boolean}\n */\nexport function lessThanEq(a: string, b: string) {\n  return compareUTF8(a, b) <= 0;\n}\n"], "mappings": ";AAeO,gBAAS,YAAY,GAAW,GAAmB;AACxD,QAAM,UAAU,EAAE;AAClB,QAAM,UAAU,EAAE;AAClB,QAAM,SAAS,KAAK,IAAI,SAAS,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,UAAU;AAC5B,UAAM,aAAa,EAAE,YAAY,CAAC;AAClC,UAAM,aAAa,EAAE,YAAY,CAAC;AAClC,QAAI,eAAe,YAAY;AAG7B,UAAI,aAAa,OAAQ,aAAa,KAAM;AAC1C,eAAO,aAAa;AAAA,MACtB;AAGA,YAAMA,WAAU,UAAU,YAAY,MAAM;AAC5C,YAAMC,WAAU,UAAU,YAAY,MAAM;AAC5C,aAAO,cAAc,QAAQD,UAAS,QAAQC,QAAO;AAAA,IACvD;AAEA,SAAK,wBAAwB,UAAU;AAAA,EACzC;AAEA,SAAO,UAAU;AACnB;AASA,SAAS,cACP,GACA,SACA,GACA,SACA;AACA,QAAM,SAAS,KAAK,IAAI,SAAS,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,SAAS,EAAE,CAAC;AAClB,UAAM,SAAS,EAAE,CAAC;AAClB,QAAI,WAAW,QAAQ;AACrB,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,SAAO,UAAU;AACnB;AAMO,gBAAS,wBAAwB,YAAoB;AAC1D,SAAO,aAAa,QAAS,IAAI;AACnC;AAGA,MAAM,MAAM,MAAM,MAAM,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;AACnD,MAAM,SAAS,IAAI;AACnB,MAAM,SAAS,IAAI;AAOnB,SAAS,UAAU,WAAmB,OAAiB;AACrD,MAAI,YAAY,KAAM;AACpB,UAAM,CAAC,IAAI;AACX,WAAO;AAAA,EACT;AAEA,MAAI;AACJ,MAAI;AAEJ,MAAI,aAAa,MAAQ;AACvB,YAAQ;AACR,aAAS;AAAA,EACX,WAAW,aAAa,OAAQ;AAC9B,YAAQ;AACR,aAAS;AAAA,EACX,WAAW,aAAa,SAAU;AAChC,YAAQ;AACR,aAAS;AAAA,EACX,OAAO;AACL,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACtC;AAEA,QAAM,CAAC,KAAK,aAAc,IAAI,SAAU;AACxC,MAAI,IAAI;AACR,SAAO,QAAQ,GAAG,SAAS;AACzB,UAAM,OAAO,aAAc,KAAK,QAAQ;AACxC,UAAM,GAAG,IAAI,MAAQ,OAAO;AAAA,EAC9B;AACA,SAAO;AACT;AAOO,gBAAS,YAAY,GAAW,GAAW;AAChD,SAAO,YAAY,GAAG,CAAC,IAAI;AAC7B;AAOO,gBAAS,cAAc,GAAW,GAAW;AAClD,SAAO,YAAY,GAAG,CAAC,KAAK;AAC9B;AAOO,gBAAS,SAAS,GAAW,GAAW;AAC7C,SAAO,YAAY,GAAG,CAAC,IAAI;AAC7B;AAOO,gBAAS,WAAW,GAAW,GAAW;AAC/C,SAAO,YAAY,GAAG,CAAC,KAAK;AAC9B;", "names": ["a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>"]}