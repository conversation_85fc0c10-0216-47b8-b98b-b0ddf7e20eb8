{"version": 3, "sources": ["../../../src/values/value.ts"], "sourcesContent": ["/**\n * Utilities for working with values stored in Convex.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n * @module\n */\nimport * as Base64 from \"./base64.js\";\nimport { isSimpleObject } from \"../common/index.js\";\n\nconst LITTLE_ENDIAN = true;\n// This code is used by code that may not have bigint literals.\nconst MIN_INT64 = BigInt(\"-9223372036854775808\");\nconst MAX_INT64 = BigInt(\"9223372036854775807\");\nconst ZERO = BigInt(\"0\");\nconst EIGHT = BigInt(\"8\");\nconst TWOFIFTYSIX = BigInt(\"256\");\n\n/**\n * The type of JavaScript values serializable to JSON.\n *\n * @public\n */\nexport type JSONValue =\n  | null\n  | boolean\n  | number\n  | string\n  | JSONValue[]\n  | { [key: string]: JSONValue };\n\n/**\n * An identifier for a document in Convex.\n *\n * Convex documents are uniquely identified by their `Id`, which is accessible\n * on the `_id` field. To learn more, see [Document IDs](https://docs.convex.dev/database/document-ids).\n *\n * Documents can be loaded using `db.get(id)` in query and mutation functions.\n *\n * IDs are base 32 encoded strings which are URL safe.\n *\n * IDs are just strings at runtime, but this type can be used to distinguish them from other\n * strings at compile time.\n *\n * If you're using code generation, use the `Id` type generated for your data model in\n * `convex/_generated/dataModel.d.ts`.\n *\n * @typeParam TableName - A string literal type of the table name (like \"users\").\n *\n * @public\n */\nexport type Id<TableName extends string> = string & { __tableName: TableName };\n\n/**\n * A value supported by Convex.\n *\n * Values can be:\n * - stored inside of documents.\n * - used as arguments and return types to queries and mutation functions.\n *\n * You can see the full set of supported types at\n * [Types](https://docs.convex.dev/using/types).\n *\n * @public\n */\nexport type Value =\n  | null\n  | bigint\n  | number\n  | boolean\n  | string\n  | ArrayBuffer\n  | Value[]\n  | { [key: string]: undefined | Value };\n\n/**\n * The types of {@link Value} that can be used to represent numbers.\n *\n * @public\n */\nexport type NumericValue = bigint | number;\n\nfunction isSpecial(n: number) {\n  return Number.isNaN(n) || !Number.isFinite(n) || Object.is(n, -0);\n}\n\nexport function slowBigIntToBase64(value: bigint): string {\n  // the conversion is easy if we pretend it's unsigned\n  if (value < ZERO) {\n    value -= MIN_INT64 + MIN_INT64;\n  }\n  let hex = value.toString(16);\n  if (hex.length % 2 === 1) hex = \"0\" + hex;\n\n  const bytes = new Uint8Array(new ArrayBuffer(8));\n  let i = 0;\n  for (const hexByte of hex.match(/.{2}/g)!.reverse()) {\n    bytes.set([parseInt(hexByte, 16)], i++);\n    value >>= EIGHT;\n  }\n  return Base64.fromByteArray(bytes);\n}\n\nexport function slowBase64ToBigInt(encoded: string): bigint {\n  const integerBytes = Base64.toByteArray(encoded);\n  if (integerBytes.byteLength !== 8) {\n    throw new Error(\n      `Received ${integerBytes.byteLength} bytes, expected 8 for $integer`,\n    );\n  }\n  let value = ZERO;\n  let power = ZERO;\n  for (const byte of integerBytes) {\n    value += BigInt(byte) * TWOFIFTYSIX ** power;\n    power++;\n  }\n  if (value > MAX_INT64) {\n    value += MIN_INT64 + MIN_INT64;\n  }\n  return value;\n}\n\nexport function modernBigIntToBase64(value: bigint): string {\n  if (value < MIN_INT64 || MAX_INT64 < value) {\n    throw new Error(\n      `BigInt ${value} does not fit into a 64-bit signed integer.`,\n    );\n  }\n  const buffer = new ArrayBuffer(8);\n  new DataView(buffer).setBigInt64(0, value, true);\n  return Base64.fromByteArray(new Uint8Array(buffer));\n}\n\nexport function modernBase64ToBigInt(encoded: string): bigint {\n  const integerBytes = Base64.toByteArray(encoded);\n  if (integerBytes.byteLength !== 8) {\n    throw new Error(\n      `Received ${integerBytes.byteLength} bytes, expected 8 for $integer`,\n    );\n  }\n  const intBytesView = new DataView(integerBytes.buffer);\n  return intBytesView.getBigInt64(0, true);\n}\n\n// Fall back to a slower version on Safari 14 which lacks these APIs.\nexport const bigIntToBase64 = (DataView.prototype as any).setBigInt64\n  ? modernBigIntToBase64\n  : slowBigIntToBase64;\nexport const base64ToBigInt = (DataView.prototype as any).getBigInt64\n  ? modernBase64ToBigInt\n  : slowBase64ToBigInt;\n\nconst MAX_IDENTIFIER_LEN = 1024;\n\nfunction validateObjectField(k: string) {\n  if (k.length > MAX_IDENTIFIER_LEN) {\n    throw new Error(\n      `Field name ${k} exceeds maximum field name length ${MAX_IDENTIFIER_LEN}.`,\n    );\n  }\n  if (k.startsWith(\"$\")) {\n    throw new Error(`Field name ${k} starts with a '$', which is reserved.`);\n  }\n  for (let i = 0; i < k.length; i += 1) {\n    const charCode = k.charCodeAt(i);\n    // Non-control ASCII characters\n    if (charCode < 32 || charCode >= 127) {\n      throw new Error(\n        `Field name ${k} has invalid character '${k[i]}': Field names can only contain non-control ASCII characters`,\n      );\n    }\n  }\n}\n\n/**\n * Parse a Convex value from its JSON representation.\n *\n * This function will deserialize serialized Int64s to `BigInt`s, Bytes to `ArrayBuffer`s etc.\n *\n * To learn more about Convex values, see [Types](https://docs.convex.dev/using/types).\n *\n * @param value - The JSON representation of a Convex value previously created with {@link convexToJson}.\n * @returns The JavaScript representation of the Convex value.\n *\n * @public\n */\nexport function jsonToConvex(value: JSONValue): Value {\n  if (value === null) {\n    return value;\n  }\n  if (typeof value === \"boolean\") {\n    return value;\n  }\n  if (typeof value === \"number\") {\n    return value;\n  }\n  if (typeof value === \"string\") {\n    return value;\n  }\n  if (Array.isArray(value)) {\n    return value.map((value) => jsonToConvex(value));\n  }\n  if (typeof value !== \"object\") {\n    throw new Error(`Unexpected type of ${value as any}`);\n  }\n  const entries = Object.entries(value);\n  if (entries.length === 1) {\n    const key = entries[0][0];\n    if (key === \"$bytes\") {\n      if (typeof value.$bytes !== \"string\") {\n        throw new Error(`Malformed $bytes field on ${value as any}`);\n      }\n      return Base64.toByteArray(value.$bytes).buffer;\n    }\n    if (key === \"$integer\") {\n      if (typeof value.$integer !== \"string\") {\n        throw new Error(`Malformed $integer field on ${value as any}`);\n      }\n      return base64ToBigInt(value.$integer);\n    }\n    if (key === \"$float\") {\n      if (typeof value.$float !== \"string\") {\n        throw new Error(`Malformed $float field on ${value as any}`);\n      }\n      const floatBytes = Base64.toByteArray(value.$float);\n      if (floatBytes.byteLength !== 8) {\n        throw new Error(\n          `Received ${floatBytes.byteLength} bytes, expected 8 for $float`,\n        );\n      }\n      const floatBytesView = new DataView(floatBytes.buffer);\n      const float = floatBytesView.getFloat64(0, LITTLE_ENDIAN);\n      if (!isSpecial(float)) {\n        throw new Error(`Float ${float} should be encoded as a number`);\n      }\n      return float;\n    }\n    if (key === \"$set\") {\n      throw new Error(\n        `Received a Set which is no longer supported as a Convex type.`,\n      );\n    }\n    if (key === \"$map\") {\n      throw new Error(\n        `Received a Map which is no longer supported as a Convex type.`,\n      );\n    }\n  }\n  const out: { [key: string]: Value } = {};\n  for (const [k, v] of Object.entries(value)) {\n    validateObjectField(k);\n    out[k] = jsonToConvex(v);\n  }\n  return out;\n}\n\nexport function stringifyValueForError(value: any) {\n  return JSON.stringify(value, (_key, value) => {\n    if (value === undefined) {\n      // By default `JSON.stringify` converts undefined, functions, symbols,\n      // Infinity, and NaN to null which produces a confusing error message.\n      // We deal with `undefined` specifically because it's the most common.\n      // Ideally we'd use a pretty-printing library that prints `undefined`\n      // (no quotes), but it might not be worth the bundle size cost.\n      return \"undefined\";\n    }\n    if (typeof value === \"bigint\") {\n      // `JSON.stringify` throws on bigints by default.\n      return `${value.toString()}n`;\n    }\n    return value;\n  });\n}\n\nfunction convexToJsonInternal(\n  value: Value,\n  originalValue: Value,\n  context: string,\n  includeTopLevelUndefined: boolean,\n): JSONValue {\n  if (value === undefined) {\n    const contextText =\n      context &&\n      ` (present at path ${context} in original object ${stringifyValueForError(\n        originalValue,\n      )})`;\n    throw new Error(\n      `undefined is not a valid Convex value${contextText}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`,\n    );\n  }\n  if (value === null) {\n    return value;\n  }\n  if (typeof value === \"bigint\") {\n    if (value < MIN_INT64 || MAX_INT64 < value) {\n      throw new Error(\n        `BigInt ${value} does not fit into a 64-bit signed integer.`,\n      );\n    }\n    return { $integer: bigIntToBase64(value) };\n  }\n  if (typeof value === \"number\") {\n    if (isSpecial(value)) {\n      const buffer = new ArrayBuffer(8);\n      new DataView(buffer).setFloat64(0, value, LITTLE_ENDIAN);\n      return { $float: Base64.fromByteArray(new Uint8Array(buffer)) };\n    } else {\n      return value;\n    }\n  }\n  if (typeof value === \"boolean\") {\n    return value;\n  }\n  if (typeof value === \"string\") {\n    return value;\n  }\n  if (value instanceof ArrayBuffer) {\n    return { $bytes: Base64.fromByteArray(new Uint8Array(value)) };\n  }\n  if (Array.isArray(value)) {\n    return value.map((value, i) =>\n      convexToJsonInternal(value, originalValue, context + `[${i}]`, false),\n    );\n  }\n  if (value instanceof Set) {\n    throw new Error(\n      errorMessageForUnsupportedType(context, \"Set\", [...value], originalValue),\n    );\n  }\n  if (value instanceof Map) {\n    throw new Error(\n      errorMessageForUnsupportedType(context, \"Map\", [...value], originalValue),\n    );\n  }\n\n  if (!isSimpleObject(value)) {\n    const theType = value?.constructor?.name;\n    const typeName = theType ? `${theType} ` : \"\";\n    throw new Error(\n      errorMessageForUnsupportedType(context, typeName, value, originalValue),\n    );\n  }\n\n  const out: { [key: string]: JSONValue } = {};\n  const entries = Object.entries(value);\n  entries.sort(([k1, _v1], [k2, _v2]) => (k1 === k2 ? 0 : k1 < k2 ? -1 : 1));\n  for (const [k, v] of entries) {\n    if (v !== undefined) {\n      validateObjectField(k);\n      out[k] = convexToJsonInternal(v, originalValue, context + `.${k}`, false);\n    } else if (includeTopLevelUndefined) {\n      validateObjectField(k);\n      out[k] = convexOrUndefinedToJsonInternal(\n        v,\n        originalValue,\n        context + `.${k}`,\n      );\n    }\n  }\n  return out;\n}\n\nfunction errorMessageForUnsupportedType(\n  context: string,\n  typeName: string,\n  value: any,\n  originalValue: any,\n) {\n  if (context) {\n    return `${typeName}${stringifyValueForError(\n      value,\n    )} is not a supported Convex type (present at path ${context} in original object ${stringifyValueForError(\n      originalValue,\n    )}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`;\n  } else {\n    return `${typeName}${stringifyValueForError(\n      value,\n    )} is not a supported Convex type.`;\n  }\n}\n\n// convexOrUndefinedToJsonInternal wrapper exists so we can pipe through the\n// `originalValue` and `context` through for better error messaging.\nfunction convexOrUndefinedToJsonInternal(\n  value: Value | undefined,\n  originalValue: Value | undefined,\n  context: string,\n): JSONValue {\n  if (value === undefined) {\n    return { $undefined: null };\n  } else {\n    if (originalValue === undefined) {\n      // This should not happen.\n      throw new Error(\n        `Programming error. Current value is ${stringifyValueForError(\n          value,\n        )} but original value is undefined`,\n      );\n    }\n    return convexToJsonInternal(value, originalValue, context, false);\n  }\n}\n\n/**\n * Convert a Convex value to its JSON representation.\n *\n * Use {@link jsonToConvex} to recreate the original value.\n *\n * To learn more about Convex values, see [Types](https://docs.convex.dev/using/types).\n *\n * @param value - A Convex value to convert into JSON.\n * @returns The JSON representation of `value`.\n *\n * @public\n */\nexport function convexToJson(value: Value): JSONValue {\n  return convexToJsonInternal(value, value, \"\", false);\n}\n\n// Convert a Convex value or `undefined` into its JSON representation.\n// `undefined` is used in filters to represent a missing object field.\nexport function convexOrUndefinedToJson(value: Value | undefined): JSONValue {\n  return convexOrUndefinedToJsonInternal(value, value, \"\");\n}\n\n/**\n * Similar to convexToJson but also serializes top level undefined fields\n * using convexOrUndefinedToJson().\n *\n * @param value - A Convex value to convert into JSON.\n * @returns The JSON representation of `value`.\n */\nexport function patchValueToJson(value: Value): JSONValue {\n  return convexToJsonInternal(value, value, \"\", true);\n}\n"], "mappings": ";AAOA,YAAY,YAAY;AACxB,SAAS,sBAAsB;AAE/B,MAAM,gBAAgB;AAEtB,MAAM,YAAY,OAAO,sBAAsB;AAC/C,MAAM,YAAY,OAAO,qBAAqB;AAC9C,MAAM,OAAO,OAAO,GAAG;AACvB,MAAM,QAAQ,OAAO,GAAG;AACxB,MAAM,cAAc,OAAO,KAAK;AAkEhC,SAAS,UAAU,GAAW;AAC5B,SAAO,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,KAAK,OAAO,GAAG,GAAG,EAAE;AAClE;AAEO,gBAAS,mBAAmB,OAAuB;AAExD,MAAI,QAAQ,MAAM;AAChB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,MAAM,MAAM,SAAS,EAAE;AAC3B,MAAI,IAAI,SAAS,MAAM,EAAG,OAAM,MAAM;AAEtC,QAAM,QAAQ,IAAI,WAAW,IAAI,YAAY,CAAC,CAAC;AAC/C,MAAI,IAAI;AACR,aAAW,WAAW,IAAI,MAAM,OAAO,EAAG,QAAQ,GAAG;AACnD,UAAM,IAAI,CAAC,SAAS,SAAS,EAAE,CAAC,GAAG,GAAG;AACtC,cAAU;AAAA,EACZ;AACA,SAAO,OAAO,cAAc,KAAK;AACnC;AAEO,gBAAS,mBAAmB,SAAyB;AAC1D,QAAM,eAAe,OAAO,YAAY,OAAO;AAC/C,MAAI,aAAa,eAAe,GAAG;AACjC,UAAM,IAAI;AAAA,MACR,YAAY,aAAa,UAAU;AAAA,IACrC;AAAA,EACF;AACA,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,aAAW,QAAQ,cAAc;AAC/B,aAAS,OAAO,IAAI,IAAI,eAAe;AACvC;AAAA,EACF;AACA,MAAI,QAAQ,WAAW;AACrB,aAAS,YAAY;AAAA,EACvB;AACA,SAAO;AACT;AAEO,gBAAS,qBAAqB,OAAuB;AAC1D,MAAI,QAAQ,aAAa,YAAY,OAAO;AAC1C,UAAM,IAAI;AAAA,MACR,UAAU,KAAK;AAAA,IACjB;AAAA,EACF;AACA,QAAM,SAAS,IAAI,YAAY,CAAC;AAChC,MAAI,SAAS,MAAM,EAAE,YAAY,GAAG,OAAO,IAAI;AAC/C,SAAO,OAAO,cAAc,IAAI,WAAW,MAAM,CAAC;AACpD;AAEO,gBAAS,qBAAqB,SAAyB;AAC5D,QAAM,eAAe,OAAO,YAAY,OAAO;AAC/C,MAAI,aAAa,eAAe,GAAG;AACjC,UAAM,IAAI;AAAA,MACR,YAAY,aAAa,UAAU;AAAA,IACrC;AAAA,EACF;AACA,QAAM,eAAe,IAAI,SAAS,aAAa,MAAM;AACrD,SAAO,aAAa,YAAY,GAAG,IAAI;AACzC;AAGO,aAAM,iBAAkB,SAAS,UAAkB,cACtD,uBACA;AACG,aAAM,iBAAkB,SAAS,UAAkB,cACtD,uBACA;AAEJ,MAAM,qBAAqB;AAE3B,SAAS,oBAAoB,GAAW;AACtC,MAAI,EAAE,SAAS,oBAAoB;AACjC,UAAM,IAAI;AAAA,MACR,cAAc,CAAC,sCAAsC,kBAAkB;AAAA,IACzE;AAAA,EACF;AACA,MAAI,EAAE,WAAW,GAAG,GAAG;AACrB,UAAM,IAAI,MAAM,cAAc,CAAC,wCAAwC;AAAA,EACzE;AACA,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AACpC,UAAM,WAAW,EAAE,WAAW,CAAC;AAE/B,QAAI,WAAW,MAAM,YAAY,KAAK;AACpC,YAAM,IAAI;AAAA,QACR,cAAc,CAAC,2BAA2B,EAAE,CAAC,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AACF;AAcO,gBAAS,aAAa,OAAyB;AACpD,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,WAAW;AAC9B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,IAAI,CAACA,WAAU,aAAaA,MAAK,CAAC;AAAA,EACjD;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,MAAM,sBAAsB,KAAY,EAAE;AAAA,EACtD;AACA,QAAM,UAAU,OAAO,QAAQ,KAAK;AACpC,MAAI,QAAQ,WAAW,GAAG;AACxB,UAAM,MAAM,QAAQ,CAAC,EAAE,CAAC;AACxB,QAAI,QAAQ,UAAU;AACpB,UAAI,OAAO,MAAM,WAAW,UAAU;AACpC,cAAM,IAAI,MAAM,6BAA6B,KAAY,EAAE;AAAA,MAC7D;AACA,aAAO,OAAO,YAAY,MAAM,MAAM,EAAE;AAAA,IAC1C;AACA,QAAI,QAAQ,YAAY;AACtB,UAAI,OAAO,MAAM,aAAa,UAAU;AACtC,cAAM,IAAI,MAAM,+BAA+B,KAAY,EAAE;AAAA,MAC/D;AACA,aAAO,eAAe,MAAM,QAAQ;AAAA,IACtC;AACA,QAAI,QAAQ,UAAU;AACpB,UAAI,OAAO,MAAM,WAAW,UAAU;AACpC,cAAM,IAAI,MAAM,6BAA6B,KAAY,EAAE;AAAA,MAC7D;AACA,YAAM,aAAa,OAAO,YAAY,MAAM,MAAM;AAClD,UAAI,WAAW,eAAe,GAAG;AAC/B,cAAM,IAAI;AAAA,UACR,YAAY,WAAW,UAAU;AAAA,QACnC;AAAA,MACF;AACA,YAAM,iBAAiB,IAAI,SAAS,WAAW,MAAM;AACrD,YAAM,QAAQ,eAAe,WAAW,GAAG,aAAa;AACxD,UAAI,CAAC,UAAU,KAAK,GAAG;AACrB,cAAM,IAAI,MAAM,SAAS,KAAK,gCAAgC;AAAA,MAChE;AACA,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,QAAQ;AAClB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,QAAQ;AAClB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,MAAgC,CAAC;AACvC,aAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC1C,wBAAoB,CAAC;AACrB,QAAI,CAAC,IAAI,aAAa,CAAC;AAAA,EACzB;AACA,SAAO;AACT;AAEO,gBAAS,uBAAuB,OAAY;AACjD,SAAO,KAAK,UAAU,OAAO,CAAC,MAAMA,WAAU;AAC5C,QAAIA,WAAU,QAAW;AAMvB,aAAO;AAAA,IACT;AACA,QAAI,OAAOA,WAAU,UAAU;AAE7B,aAAO,GAAGA,OAAM,SAAS,CAAC;AAAA,IAC5B;AACA,WAAOA;AAAA,EACT,CAAC;AACH;AAEA,SAAS,qBACP,OACA,eACA,SACA,0BACW;AACX,MAAI,UAAU,QAAW;AACvB,UAAM,cACJ,WACA,qBAAqB,OAAO,uBAAuB;AAAA,MACjD;AAAA,IACF,CAAC;AACH,UAAM,IAAI;AAAA,MACR,wCAAwC,WAAW;AAAA,IACrD;AAAA,EACF;AACA,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,QAAQ,aAAa,YAAY,OAAO;AAC1C,YAAM,IAAI;AAAA,QACR,UAAU,KAAK;AAAA,MACjB;AAAA,IACF;AACA,WAAO,EAAE,UAAU,eAAe,KAAK,EAAE;AAAA,EAC3C;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,UAAU,KAAK,GAAG;AACpB,YAAM,SAAS,IAAI,YAAY,CAAC;AAChC,UAAI,SAAS,MAAM,EAAE,WAAW,GAAG,OAAO,aAAa;AACvD,aAAO,EAAE,QAAQ,OAAO,cAAc,IAAI,WAAW,MAAM,CAAC,EAAE;AAAA,IAChE,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,OAAO,UAAU,WAAW;AAC9B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,aAAa;AAChC,WAAO,EAAE,QAAQ,OAAO,cAAc,IAAI,WAAW,KAAK,CAAC,EAAE;AAAA,EAC/D;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM;AAAA,MAAI,CAACA,QAAO,MACvB,qBAAqBA,QAAO,eAAe,UAAU,IAAI,CAAC,KAAK,KAAK;AAAA,IACtE;AAAA,EACF;AACA,MAAI,iBAAiB,KAAK;AACxB,UAAM,IAAI;AAAA,MACR,+BAA+B,SAAS,OAAO,CAAC,GAAG,KAAK,GAAG,aAAa;AAAA,IAC1E;AAAA,EACF;AACA,MAAI,iBAAiB,KAAK;AACxB,UAAM,IAAI;AAAA,MACR,+BAA+B,SAAS,OAAO,CAAC,GAAG,KAAK,GAAG,aAAa;AAAA,IAC1E;AAAA,EACF;AAEA,MAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,UAAM,UAAU,OAAO,aAAa;AACpC,UAAM,WAAW,UAAU,GAAG,OAAO,MAAM;AAC3C,UAAM,IAAI;AAAA,MACR,+BAA+B,SAAS,UAAU,OAAO,aAAa;AAAA,IACxE;AAAA,EACF;AAEA,QAAM,MAAoC,CAAC;AAC3C,QAAM,UAAU,OAAO,QAAQ,KAAK;AACpC,UAAQ,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,MAAO,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK,CAAE;AACzE,aAAW,CAAC,GAAG,CAAC,KAAK,SAAS;AAC5B,QAAI,MAAM,QAAW;AACnB,0BAAoB,CAAC;AACrB,UAAI,CAAC,IAAI,qBAAqB,GAAG,eAAe,UAAU,IAAI,CAAC,IAAI,KAAK;AAAA,IAC1E,WAAW,0BAA0B;AACnC,0BAAoB,CAAC;AACrB,UAAI,CAAC,IAAI;AAAA,QACP;AAAA,QACA;AAAA,QACA,UAAU,IAAI,CAAC;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,+BACP,SACA,UACA,OACA,eACA;AACA,MAAI,SAAS;AACX,WAAO,GAAG,QAAQ,GAAG;AAAA,MACnB;AAAA,IACF,CAAC,oDAAoD,OAAO,uBAAuB;AAAA,MACjF;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,WAAO,GAAG,QAAQ,GAAG;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAIA,SAAS,gCACP,OACA,eACA,SACW;AACX,MAAI,UAAU,QAAW;AACvB,WAAO,EAAE,YAAY,KAAK;AAAA,EAC5B,OAAO;AACL,QAAI,kBAAkB,QAAW;AAE/B,YAAM,IAAI;AAAA,QACR,uCAAuC;AAAA,UACrC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,qBAAqB,OAAO,eAAe,SAAS,KAAK;AAAA,EAClE;AACF;AAcO,gBAAS,aAAa,OAAyB;AACpD,SAAO,qBAAqB,OAAO,OAAO,IAAI,KAAK;AACrD;AAIO,gBAAS,wBAAwB,OAAqC;AAC3E,SAAO,gCAAgC,OAAO,OAAO,EAAE;AACzD;AASO,gBAAS,iBAAiB,OAAyB;AACxD,SAAO,qBAAqB,OAAO,OAAO,IAAI,IAAI;AACpD;", "names": ["value"]}