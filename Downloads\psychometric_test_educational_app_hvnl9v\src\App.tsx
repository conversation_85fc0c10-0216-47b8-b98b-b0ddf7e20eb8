import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { Dashboard } from "./components/Dashboard";
import { TestInterface } from "./components/TestInterface";
import { useState } from "react";

export default function App() {
  const [currentView, setCurrentView] = useState<'dashboard' | 'test'>('dashboard');
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100">
      <header className="sticky top-0 z-10 bg-white/90 backdrop-blur-sm border-b border-blue-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 h-16 flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">PT</span>
            </div>
            <h1 className="text-xl font-bold text-gray-800">PsychoTest Pro</h1>
          </div>
          <Authenticated>
            <div className="flex items-center space-x-4">
              {currentView === 'test' && (
                <button
                  onClick={() => {
                    setCurrentView('dashboard');
                    setCurrentSessionId(null);
                  }}
                  className="px-4 py-2 text-blue-600 hover:text-blue-800 font-medium transition-colors"
                >
                  ← Kembali ke Dashboard
                </button>
              )}
              <SignOutButton />
            </div>
          </Authenticated>
        </div>
      </header>

      <main className="flex-1">
        <Unauthenticated>
          <div className="min-h-[calc(100vh-4rem)] flex items-center justify-center p-8">
            <div className="w-full max-w-md">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800 mb-4">
                  Selamat datang di PsychoTest Pro
                </h2>
                <p className="text-gray-600">
                  Kuasai tes psikometrik dengan platform latihan komprehensif kami
                </p>
              </div>
              <SignInForm />
            </div>
          </div>
        </Unauthenticated>

        <Authenticated>
          {currentView === 'dashboard' ? (
            <Dashboard 
              onStartTest={(sessionId) => {
                setCurrentSessionId(sessionId);
                setCurrentView('test');
              }}
            />
          ) : (
            <TestInterface 
              sessionId={currentSessionId}
              onComplete={() => {
                setCurrentView('dashboard');
                setCurrentSessionId(null);
              }}
            />
          )}
        </Authenticated>
      </main>
      <Toaster />
    </div>
  );
}
