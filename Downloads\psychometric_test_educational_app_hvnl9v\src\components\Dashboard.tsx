import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";

interface DashboardProps {
  onStartTest: (sessionId: string) => void;
}

export function Dashboard({ onStartTest }: DashboardProps) {
  const categories = useQuery(api.questions.getAllCategories);
  const userProgress = useQuery(api.questions.getUserProgress);
  const recentSessions = useQuery(api.questions.getRecentSessions, {});
  const startTestSession = useMutation(api.questions.startTestSession);
  const seedQuestions = useMutation(api.seedData.seedQuestions);
  
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [questionCount, setQuestionCount] = useState<number>(10);
  const [isStarting, setIsStarting] = useState(false);

  const handleStartTest = async () => {
    if (!selectedCategory) {
      toast.error("<PERSON>lakan pilih kategori tes");
      return;
    }

    setIsStarting(true);
    try {
      const sessionId = await startTestSession({
        category: selectedCategory,
        questionCount,
      });
      onStartTest(sessionId);
    } catch (error) {
      toast.error("Gagal memulai tes");
      console.error(error);
    } finally {
      setIsStarting(false);
    }
  };

  const handleSeedData = async () => {
    try {
      const result = await seedQuestions();
      toast.success(result);
    } catch (error) {
      toast.error("Gagal memuat soal-soal");
    }
  };

  const categoryInfo = {
    logical: {
      name: "Penalaran Logis",
      description: "Uji kemampuan berpikir analitis dan pemecahan masalah Anda",
      color: "from-purple-500 to-purple-600",
      icon: "🧠",
    },
    verbal: {
      name: "Kemampuan Verbal",
      description: "Nilai pemahaman bahasa dan keterampilan komunikasi Anda",
      color: "from-green-500 to-green-600",
      icon: "📝",
    },
    numerical: {
      name: "Penalaran Numerik",
      description: "Evaluasi kemampuan matematika dan kuantitatif Anda",
      color: "from-blue-500 to-blue-600",
      icon: "🔢",
    },
    personality: {
      name: "Penilaian Kepribadian",
      description: "Pahami gaya kerja dan preferensi perilaku Anda",
      color: "from-orange-500 to-orange-600",
      icon: "👤",
    },
  };

  if (!categories) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (categories.length === 0) {
    return (
      <div className="max-w-4xl mx-auto p-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Selamat datang di PsychoTest Pro!
          </h2>
          <p className="text-gray-600 mb-6">
            Mari siapkan soal-soal latihan untuk memulai.
          </p>
          <button
            onClick={handleSeedData}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Inisialisasi Database Soal
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-2">
          Dashboard Latihan
        </h2>
        <p className="text-gray-600">
          Pilih kategori dan mulai berlatih tes psikometrik
        </p>
      </div>

      {/* Quick Stats */}
      {userProgress && userProgress.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {userProgress.map((progress) => {
            const info = categoryInfo[progress.category as keyof typeof categoryInfo];
            const accuracy = progress.totalQuestions > 0 
              ? Math.round((progress.correctAnswers / progress.totalQuestions) * 100)
              : 0;
            
            return (
              <div key={progress.category} className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
                <div className="flex items-center space-x-3 mb-2">
                  <span className="text-2xl">{info?.icon}</span>
                  <h3 className="font-semibold text-gray-800 text-sm">{info?.name}</h3>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Akurasi</span>
                    <span className="font-medium text-gray-800">{accuracy}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Skor Terbaik</span>
                    <span className="font-medium text-gray-800">{progress.bestScore}%</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Test Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {categories.map((category) => {
          const info = categoryInfo[category.category as keyof typeof categoryInfo];
          if (!info) return null;

          return (
            <div
              key={category.category}
              className={`relative overflow-hidden rounded-xl bg-gradient-to-r ${info.color} p-6 text-white cursor-pointer transform transition-all duration-200 hover:scale-105 hover:shadow-lg ${
                selectedCategory === category.category ? 'ring-4 ring-white ring-opacity-50' : ''
              }`}
              onClick={() => setSelectedCategory(category.category)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-3xl">{info.icon}</span>
                    <h3 className="text-xl font-bold">{info.name}</h3>
                  </div>
                  <p className="text-white/90 mb-4">{info.description}</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="bg-white/20 px-3 py-1 rounded-full">
                      {category.count} soal
                    </span>
                  </div>
                </div>
              </div>
              {selectedCategory === category.category && (
                <div className="absolute top-4 right-4">
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                    <span className="text-green-600 text-sm">✓</span>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Test Configuration */}
      {selectedCategory && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Konfigurasi Tes</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Jumlah Soal
              </label>
              <select
                value={questionCount}
                onChange={(e) => setQuestionCount(Number(e.target.value))}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={5}>5 soal (Cepat)</option>
                <option value={10}>10 soal (Standar)</option>
                <option value={15}>15 soal (Diperpanjang)</option>
                <option value={20}>20 soal (Lengkap)</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={handleStartTest}
                disabled={isStarting}
                className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {isStarting ? "Memulai..." : "Mulai Tes"}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Recent Sessions */}
      {recentSessions && recentSessions.length > 0 && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Hasil Tes Terbaru</h3>
          <div className="space-y-3">
            {recentSessions.slice(0, 5).map((session) => {
              const info = categoryInfo[session.category as keyof typeof categoryInfo];
              return (
                <div key={session._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">{info?.icon}</span>
                    <div>
                      <p className="font-medium text-gray-800">{info?.name}</p>
                      <p className="text-sm text-gray-600">
                        {new Date(session.endTime || 0).toLocaleDateString('id-ID')}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-800">{session.score}%</p>
                    <p className="text-sm text-gray-600">
                      {session.answers.filter(a => a.isCorrect).length}/{session.answers.length}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
