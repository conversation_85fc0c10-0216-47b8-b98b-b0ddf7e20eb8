import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Id } from "../../convex/_generated/dataModel";

interface TestInterfaceProps {
  sessionId: string | null;
  onComplete: () => void;
}

export function TestInterface({ sessionId, onComplete }: TestInterfaceProps) {
  const session = useQuery(api.questions.getTestSession, 
    sessionId ? { sessionId: sessionId as Id<"testSessions"> } : "skip"
  );
  const submitAnswer = useMutation(api.questions.submitAnswer);
  const completeTest = useMutation(api.questions.completeTestSession);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string>("");
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [questionStartTime, setQuestionStartTime] = useState<number>(Date.now());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);
  const [lastFeedback, setLastFeedback] = useState<{ isCorrect?: boolean; explanation: string } | null>(null);

  const currentQuestion = session?.questions[currentQuestionIndex];

  // Timer effect
  useEffect(() => {
    if (!currentQuestion) return;

    setTimeLeft(currentQuestion.timeLimit);
    setQuestionStartTime(Date.now());
    
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          handleSubmitAnswer();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [currentQuestionIndex, currentQuestion]);

  const handleSubmitAnswer = async () => {
    if (!session || !currentQuestion || isSubmitting) return;

    setIsSubmitting(true);
    const timeSpent = Math.round((Date.now() - questionStartTime) / 1000);

    try {
      const result = await submitAnswer({
        sessionId: session._id,
        questionId: currentQuestion._id,
        answer: selectedAnswer || "Tidak ada jawaban",
        timeSpent,
      });

      setLastFeedback(result);
      setShowExplanation(true);
    } catch (error) {
      toast.error("Gagal mengirim jawaban");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNextQuestion = () => {
    setShowExplanation(false);
    setLastFeedback(null);
    setSelectedAnswer("");

    if (currentQuestionIndex < session!.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      handleCompleteTest();
    }
  };

  const handleCompleteTest = async () => {
    if (!session) return;

    try {
      const result = await completeTest({ sessionId: session._id });
      toast.success(`Tes selesai! Skor: ${result.score}%`);
      onComplete();
    } catch (error) {
      toast.error("Gagal menyelesaikan tes");
      console.error(error);
    }
  };

  if (!session || !currentQuestion) {
    return (
      <div className="flex items-center justify-center min-h-[400px] relative z-10">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto card-3d neon-glow mb-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
          <p className="text-white/80 text-lg">Memuat soal...</p>
        </div>
      </div>
    );
  }

  const progress = ((currentQuestionIndex + 1) / session.questions.length) * 100;

  const categoryNames = {
    logical: "Penalaran Logis",
    verbal: "Kemampuan Verbal", 
    numerical: "Penalaran Numerik",
    personality: "Penilaian Kepribadian"
  };

  return (
    <div className="max-w-4xl mx-auto p-6 relative z-10">
      {/* Progress Header */}
      <div className="glass rounded-2xl p-8 border border-white/20 mb-8 card-3d">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-semibold text-white">
              📝 Soal {currentQuestionIndex + 1} dari {session.questions.length}
            </h2>
            <p className="text-white/80 capitalize text-lg">{categoryNames[session.category as keyof typeof categoryNames]}</p>
          </div>
          <div className="text-right">
            <div className={`text-3xl font-bold ${timeLeft <= 10 ? 'text-red-400 neon-glow' : 'text-green-400'}`}>
              ⏰ {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, '0')}
            </div>
            <p className="text-sm text-white/70">Waktu tersisa</p>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-white/20 rounded-full h-3">
          <div
            className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500 neon-glow"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* Question Card */}
      <div className="glass rounded-2xl p-10 border border-white/20 card-3d">
        <div className="mb-8">
          <h3 className="text-2xl font-semibold text-white mb-6 leading-relaxed">
            {currentQuestion.question}
          </h3>
          <p className="text-sm text-white/70 mb-4">
            🎯 Tingkat kesulitan: <span className="capitalize font-medium text-purple-300">{currentQuestion.difficulty}</span>
          </p>
        </div>

        {/* Answer Options */}
        {currentQuestion.options && (
          <div className="space-y-4 mb-8">
            {currentQuestion.options.map((option, index) => (
              <label
                key={index}
                className={`flex items-center p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 card-3d ${
                  selectedAnswer === option
                    ? 'border-purple-400 bg-purple-500/20 neon-glow'
                    : 'border-white/20 hover:border-white/40 hover:bg-white/10 glass'
                } ${showExplanation ? 'cursor-not-allowed opacity-75' : ''}`}
              >
                <input
                  type="radio"
                  name="answer"
                  value={option}
                  checked={selectedAnswer === option}
                  onChange={(e) => setSelectedAnswer(e.target.value)}
                  disabled={showExplanation}
                  className="sr-only"
                />
                <div className={`w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center transition-all duration-300 ${
                  selectedAnswer === option ? 'border-purple-400 bg-purple-500' : 'border-white/40'
                }`}>
                  {selectedAnswer === option && (
                    <div className="w-3 h-3 rounded-full bg-white"></div>
                  )}
                </div>
                <span className="text-white font-medium text-lg">{option}</span>
              </label>
            ))}
          </div>
        )}

        {/* Feedback */}
        {showExplanation && lastFeedback && (
          <div className={`p-6 rounded-xl mb-8 glass border card-3d ${
            lastFeedback.isCorrect === true
              ? 'border-green-400/50 bg-green-500/20'
              : lastFeedback.isCorrect === false
              ? 'border-red-400/50 bg-red-500/20'
              : 'border-blue-400/50 bg-blue-500/20'
          }`}>
            <div className="flex items-start space-x-4">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-lg font-bold card-3d ${
                lastFeedback.isCorrect === true
                  ? 'bg-green-500 neon-glow'
                  : lastFeedback.isCorrect === false
                  ? 'bg-red-500 neon-glow'
                  : 'bg-blue-500 neon-glow'
              }`}>
                {lastFeedback.isCorrect === true ? '✓' : lastFeedback.isCorrect === false ? '✗' : 'i'}
              </div>
              <div>
                <p className={`font-semibold mb-2 text-lg ${
                  lastFeedback.isCorrect === true
                    ? 'text-green-300'
                    : lastFeedback.isCorrect === false
                    ? 'text-red-300'
                    : 'text-blue-300'
                }`}>
                  {lastFeedback.isCorrect === true
                    ? '🎉 Benar!'
                    : lastFeedback.isCorrect === false
                    ? '❌ Salah'
                    : '📝 Jawaban Tercatat'}
                </p>
                <p className="text-white/90 text-base">{lastFeedback.explanation}</p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between items-center">
          <div className="text-sm text-white/70">
            🎯 Tingkat kesulitan: <span className="capitalize font-medium text-purple-300">
              {currentQuestion.difficulty === 'easy' ? 'Mudah' :
               currentQuestion.difficulty === 'medium' ? 'Sedang' : 'Sulit'}
            </span>
          </div>

          <div className="space-x-4">
            {!showExplanation ? (
              <button
                onClick={handleSubmitAnswer}
                disabled={!selectedAnswer || isSubmitting}
                className="px-8 py-3 text-white font-semibold rounded-xl transition-all duration-300 btn-3d disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? "📤 Mengirim..." : "📝 Kirim Jawaban"}
              </button>
            ) : (
              <button
                onClick={handleNextQuestion}
                className="px-8 py-3 text-white font-semibold rounded-xl transition-all duration-300 glass border border-green-400/50 hover:border-green-400 card-3d hover:bg-green-500/20"
              >
                {currentQuestionIndex < session.questions.length - 1 ? "➡️ Soal Berikutnya" : "🏁 Selesaikan Tes"}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
