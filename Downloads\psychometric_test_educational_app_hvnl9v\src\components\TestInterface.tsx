import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Id } from "../../convex/_generated/dataModel";

interface TestInterfaceProps {
  sessionId: string | null;
  onComplete: () => void;
}

export function TestInterface({ sessionId, onComplete }: TestInterfaceProps) {
  const session = useQuery(api.questions.getTestSession, 
    sessionId ? { sessionId: sessionId as Id<"testSessions"> } : "skip"
  );
  const submitAnswer = useMutation(api.questions.submitAnswer);
  const completeTest = useMutation(api.questions.completeTestSession);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string>("");
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [questionStartTime, setQuestionStartTime] = useState<number>(Date.now());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);
  const [lastFeedback, setLastFeedback] = useState<{ isCorrect?: boolean; explanation: string } | null>(null);

  const currentQuestion = session?.questions[currentQuestionIndex];

  // Timer effect
  useEffect(() => {
    if (!currentQuestion) return;

    setTimeLeft(currentQuestion.timeLimit);
    setQuestionStartTime(Date.now());
    
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          handleSubmitAnswer();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [currentQuestionIndex, currentQuestion]);

  const handleSubmitAnswer = async () => {
    if (!session || !currentQuestion || isSubmitting) return;

    setIsSubmitting(true);
    const timeSpent = Math.round((Date.now() - questionStartTime) / 1000);

    try {
      const result = await submitAnswer({
        sessionId: session._id,
        questionId: currentQuestion._id,
        answer: selectedAnswer || "Tidak ada jawaban",
        timeSpent,
      });

      setLastFeedback(result);
      setShowExplanation(true);
    } catch (error) {
      toast.error("Gagal mengirim jawaban");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNextQuestion = () => {
    setShowExplanation(false);
    setLastFeedback(null);
    setSelectedAnswer("");

    if (currentQuestionIndex < session!.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      handleCompleteTest();
    }
  };

  const handleCompleteTest = async () => {
    if (!session) return;

    try {
      const result = await completeTest({ sessionId: session._id });
      toast.success(`Tes selesai! Skor: ${result.score}%`);
      onComplete();
    } catch (error) {
      toast.error("Gagal menyelesaikan tes");
      console.error(error);
    }
  };

  if (!session || !currentQuestion) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const progress = ((currentQuestionIndex + 1) / session.questions.length) * 100;

  const categoryNames = {
    logical: "Penalaran Logis",
    verbal: "Kemampuan Verbal", 
    numerical: "Penalaran Numerik",
    personality: "Penilaian Kepribadian"
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Header */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Soal {currentQuestionIndex + 1} dari {session.questions.length}
            </h2>
            <p className="text-gray-600 capitalize">{categoryNames[session.category as keyof typeof categoryNames]}</p>
          </div>
          <div className="text-right">
            <div className={`text-2xl font-bold ${timeLeft <= 10 ? 'text-red-600' : 'text-blue-600'}`}>
              {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, '0')}
            </div>
            <p className="text-sm text-gray-600">Waktu tersisa</p>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* Question Card */}
      <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4 leading-relaxed">
            {currentQuestion.question}
          </h3>
        </div>

        {/* Answer Options */}
        {currentQuestion.options && (
          <div className="space-y-3 mb-6">
            {currentQuestion.options.map((option, index) => (
              <label
                key={index}
                className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedAnswer === option
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                } ${showExplanation ? 'cursor-not-allowed opacity-75' : ''}`}
              >
                <input
                  type="radio"
                  name="answer"
                  value={option}
                  checked={selectedAnswer === option}
                  onChange={(e) => setSelectedAnswer(e.target.value)}
                  disabled={showExplanation}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                  selectedAnswer === option ? 'border-blue-500' : 'border-gray-300'
                }`}>
                  {selectedAnswer === option && (
                    <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                  )}
                </div>
                <span className="text-gray-800">{option}</span>
              </label>
            ))}
          </div>
        )}

        {/* Feedback */}
        {showExplanation && lastFeedback && (
          <div className={`p-4 rounded-lg mb-6 ${
            lastFeedback.isCorrect === true 
              ? 'bg-green-50 border border-green-200' 
              : lastFeedback.isCorrect === false
              ? 'bg-red-50 border border-red-200'
              : 'bg-blue-50 border border-blue-200'
          }`}>
            <div className="flex items-start space-x-3">
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                lastFeedback.isCorrect === true 
                  ? 'bg-green-500' 
                  : lastFeedback.isCorrect === false
                  ? 'bg-red-500'
                  : 'bg-blue-500'
              }`}>
                {lastFeedback.isCorrect === true ? '✓' : lastFeedback.isCorrect === false ? '✗' : 'i'}
              </div>
              <div>
                <p className={`font-medium mb-1 ${
                  lastFeedback.isCorrect === true 
                    ? 'text-green-800' 
                    : lastFeedback.isCorrect === false
                    ? 'text-red-800'
                    : 'text-blue-800'
                }`}>
                  {lastFeedback.isCorrect === true 
                    ? 'Benar!' 
                    : lastFeedback.isCorrect === false
                    ? 'Salah'
                    : 'Jawaban Tercatat'}
                </p>
                <p className="text-gray-700 text-sm">{lastFeedback.explanation}</p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between">
          <div className="text-sm text-gray-500">
            Tingkat kesulitan: <span className="capitalize font-medium">
              {currentQuestion.difficulty === 'easy' ? 'Mudah' : 
               currentQuestion.difficulty === 'medium' ? 'Sedang' : 'Sulit'}
            </span>
          </div>
          
          <div className="space-x-3">
            {!showExplanation ? (
              <button
                onClick={handleSubmitAnswer}
                disabled={!selectedAnswer || isSubmitting}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {isSubmitting ? "Mengirim..." : "Kirim Jawaban"}
              </button>
            ) : (
              <button
                onClick={handleNextQuestion}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
              >
                {currentQuestionIndex < session.questions.length - 1 ? "Soal Berikutnya" : "Selesaikan Tes"}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
